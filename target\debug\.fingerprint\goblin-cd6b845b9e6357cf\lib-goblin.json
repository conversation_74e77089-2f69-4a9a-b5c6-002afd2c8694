{"rustc": 1842507548689473721, "features": "[\"alloc\", \"archive\", \"default\", \"elf32\", \"elf64\", \"endian_fd\", \"log\", \"mach32\", \"mach64\", \"pe32\", \"pe64\", \"std\", \"te\"]", "declared_features": "[\"alloc\", \"archive\", \"default\", \"elf32\", \"elf64\", \"endian_fd\", \"log\", \"mach32\", \"mach64\", \"pe32\", \"pe64\", \"std\", \"te\"]", "target": 13895075870230038315, "profile": 15657897354478470176, "path": 8178070041108516982, "deps": [[5986029879202738730, "log", false, 5542591549586353644], [10122320484574630998, "scroll", false, 3783561493704471845], [17082805128634993388, "plain", false, 17819802747001739346]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\goblin-cd6b845b9e6357cf\\dep-lib-goblin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}