{"rustc": 1842507548689473721, "features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"fastrand\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 12734930653945463777, "deps": [[376837177317575824, "build_script_build", false, 5621506271792915505], [4143744114649553716, "raw_window_handle", false, 5354778983053800373], [5986029879202738730, "log", false, 6163580429061228050], [10281541584571964250, "windows_sys", false, 6469558621669871774]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-d13223179a07926d\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}