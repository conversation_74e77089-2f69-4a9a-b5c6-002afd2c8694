//! # OmniForge: Advanced Cyberpunk GUI Interface
//!
//! Built with SlintMaster v2.0 Framework for ArcMoon Studios' OmniCodex project.
//! This comprehensive UI system provides real-time compilation monitoring, binary
//! analysis visualization, and advanced project management with cyberpunk aesthetics.
//!
//! ## Key Features
//!
//! - **File Search & Drag-Drop**: Integrated file discovery and drag-drop import
//! - **Real-time Compilation**: Live progress tracking with performance metrics
//! - **Binary Analysis**: Advanced security and performance analysis visualization
//! - **Multi-language Support**: Rust, C, C++, Go, Zig, Assembly syntax highlighting
//! - **Theme System**: Multiple cyberpunk themes with customizable effects
//! - **Hardware Acceleration**: Integration with HAAL (AHAW) for optimal performance
//!
//! ## Architecture Integration
//!
//! This UI integrates seamlessly with:
//! - `src/gui/mod.rs`: Rust backend interface and state management
//! - `src/ahaw/mod.rs`: Hardware acceleration layer (HAAL bridge)
//! - `haal/`: C++/CUDA kernels for high-performance computing
//!
//! ## Usage
//!
//! ```slint
//! import { MainWindow } from "omniforge.slint";
//! 
//! MainWindow {
//!     project-name: "MyProject";
//!     compilation-status: "Ready";
//! }
//! ```
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣


import { Button, VerticalBox, HorizontalBox, ScrollView, ListView, LineEdit,
         ComboBox, CheckBox, Slider, ProgressIndicator, TabWidget, TextEdit } from "std-widgets.slint";

// ================================================================================================
// CORE COMPONENTS (Moved to top for proper declaration order)
// ================================================================================================

// Neon Glow Effect Component
component NeonGlow inherits Rectangle {
    in-out property <color> glow-color: #00ffff;
    in-out property <length> glow-radius: 20px;
    in-out property <bool> active: false;
    in-out property <float> intensity: 1.0;

    drop-shadow-blur: active ? glow-radius * intensity : 0px;
    drop-shadow-color: glow-color;
    drop-shadow-offset-x: 0px;
    drop-shadow-offset-y: 0px;

    animate drop-shadow-blur {
        duration: 200ms;
        easing: ease-in-out;
    }
}

// Cyberpunk Button Component
export component CyberButton inherits Rectangle {
    in-out property <string> text: "Button";
    in-out property <bool> primary: true;
    in-out property <bool> disabled: false;
    in-out property <bool> loading: false;
    in-out property <color> accent-color: #0080ff;

    callback clicked;

    min-width: 120px;
    min-height: 40px;

    // Base Styling
    background: primary ? accent-color : #2a2a2a;
    border-radius: 4px;
    border-width: 1px;
    border-color: primary ? #00ffff : #4a4a4a;

    // State Transitions
    animate background {
        duration: 150ms;
        easing: ease-in-out;
    }

    animate border-color {
        duration: 150ms;
        easing: ease-in-out;
    }

    // Neon Glow Effect
    NeonGlow {
        glow-color: primary ? #00ffff : #4a4a4a;
        active: (touch-area.has-hover || touch-area.pressed) && !disabled;
        intensity: touch-area.pressed ? 1.5 : 1.0;
    }

    // Content Layout
    HorizontalLayout {
        padding: 12px;
        spacing: 8px;
        alignment: center;

        // Loading Spinner
        if loading: Text {
            text: "⟳";
            font-size: 16px;
            color: #00ffff;
        }

        // Button Text
        Text {
            text: root.text;
            font-size: 14px;
            font-weight: 600;
            color: disabled ? #666666 : #ffffff;

            animate color {
                duration: 150ms;
            }
        }
    }

    // Touch Handling
    touch-area := TouchArea {
        enabled: !disabled;
        clicked => {
            if (!loading && !disabled) {
                clicked();
            }
        }
    }
}

// ================================================================================================
// CYBERPUNK DESIGN SYSTEM
// ================================================================================================

// Core Color Palette
export global CyberpunkPalette {
    // Primary Background Hierarchy
    in-out property <color> void-black:       #0a0a0a;
    in-out property <color> deep-charcoal:    #1a1a1a;
    in-out property <color> carbon-steel:     #2a2a2a;
    in-out property <color> graphite-shadow:  #3a3a3a;
    in-out property <color> slate-gray:       #4a4a4a;
    
    // Accent Colors
    in-out property <color> neon-cyan:        #00ffff;
    in-out property <color> electric-blue:    #0080ff;
    in-out property <color> plasma-purple:    #8000ff;
    in-out property <color> danger-red:       #ff0040;
    in-out property <color> warning-orange:   #ff8000;
    in-out property <color> success-green:    #00ff40;
    in-out property <color> matrix-green:     #00ff00;
    
    // Chrome Effects
    in-out property <color> chrome-highlight: #c0c0c0;
    in-out property <color> chrome-shadow:    #606060;
    in-out property <color> chrome-mid:       #909090;
    in-out property <color> chrome-dark:      #404040;
    
    // Text Hierarchy
    in-out property <color> text-primary:     #ffffff;
    in-out property <color> text-secondary:   #b0b0b0;
    in-out property <color> text-tertiary:    #808080;
    in-out property <color> text-accent:      #00ffff;
    in-out property <color> text-success:     #00ff40;
    in-out property <color> text-warning:     #ff8000;
    in-out property <color> text-error:       #ff0040;
    
    // Glass Effects
    in-out property <color> glass-dark:       rgba(26, 26, 26, 0.8);
    in-out property <color> glass-light:      rgba(58, 58, 58, 0.6);
    in-out property <color> glass-accent:     rgba(0, 255, 255, 0.1);
}

// Typography System
export global CyberpunkTypography {
    // Font Families
    in-out property <string> primary-font: "Orbitron, 'Courier New', monospace";
    in-out property <string> secondary-font: "Rajdhani, 'Arial', sans-serif";
    in-out property <string> mono-font: "'Fira Code', 'Consolas', monospace";
    
    // Size Scale
    in-out property <length> text-xs:   10px;
    in-out property <length> text-sm:   12px;
    in-out property <length> text-base: 14px;
    in-out property <length> text-lg:   16px;
    in-out property <length> text-xl:   18px;
    in-out property <length> text-2xl:  24px;
    in-out property <length> text-3xl:  32px;
    in-out property <length> text-4xl:  48px;
}

// Animation System
export global CyberpunkAnimations {
    // Timing Functions
    in-out property <duration> fast:       150ms;
    in-out property <duration> medium:     300ms;
    in-out property <duration> slow:       500ms;
    in-out property <duration> ultra-slow: 1000ms;
    
    // Easing Curves
    in-out property <easing> cyber-ease:    cubic-bezier(0.25, 0.1, 0.25, 1);
    in-out property <easing> neon-pulse:    cubic-bezier(0.4, 0, 0.6, 1);
    in-out property <easing> glitch-snap:   cubic-bezier(0.68, -0.55, 0.265, 1.55);
    in-out property <easing> chrome-smooth: cubic-bezier(0.23, 1, 0.32, 1);
}

// ================================================================================================
// FILE MANAGEMENT COMPONENTS
// ================================================================================================

/// Advanced file search component with real-time filtering
/// 
/// Provides instant file discovery across project directories with support for:
/// - Multi-criteria filtering (name, type, size, date)
/// - Real-time search results
/// - Fuzzy matching for improved usability
/// - Integration with project file tree
export component FileSearchPanel inherits Rectangle {
    /// Current search query string
    in-out property <string> search-query: "";
    /// Array of search results
    in-out property <[string]> search-results: [];
    /// Whether search is currently active
    in-out property <bool> searching: false;
    /// File type filter
    in-out property <string> file-type-filter: "all";
    
    /// Callback when file is selected from search results
    callback file-selected(string);
    /// Callback when search is initiated
    callback search-initiated(string, string);
    
    background: CyberpunkPalette.deep-charcoal;
    border-radius: 8px;
    border-width: 1px;
    border-color: CyberpunkPalette.chrome-shadow;
    
    VerticalLayout {
        padding: 16px;
        spacing: 12px;
        
        // Search Header
        Text {
            text: "File Search";
            font-family: CyberpunkTypography.primary-font;
            font-size: CyberpunkTypography.text-lg;
            color: CyberpunkPalette.text-accent;
        }
        
        // Search Controls
        HorizontalLayout {
            spacing: 8px;
            
            // Search Input
            Rectangle {
                height: 36px;
                background: CyberpunkPalette.graphite-shadow;
                border-radius: 4px;
                border-width: 1px;
                border-color: CyberpunkPalette.chrome-shadow;
                
                HorizontalLayout {
                    padding: 8px;
                    
                    LineEdit {
                        text: search-query;
                        placeholder-text: "Search files...";
                        font-size: CyberpunkTypography.text-base;
                        
                        edited(query) => {
                            search-query = query;
                            search-initiated(query, file-type-filter);
                        }
                    }
                }
            }
            
            // File Type Filter
            ComboBox {
                width: 120px;
                model: ["All", "Rust", "C/C++", "Go", "Zig", "Assembly", "Headers", "Config"];
                current-value: file-type-filter;
                
                selected(value) => {
                    file-type-filter = value;
                    if (search-query != "") {
                        search-initiated(search-query, value);
                    }
                }
            }
        }
        
        // Search Results
        ScrollView {
            ListView {
                for result in search-results: Rectangle {
                    height: 32px;
                    background: touch-area.has-hover ? CyberpunkPalette.carbon-steel : Colors.transparent;
                    
                    animate background {
                        duration: CyberpunkAnimations.fast;
                    }
                    
                    HorizontalLayout {
                        padding: 8px;
                        spacing: 8px;
                        
                        // File Icon
                        Rectangle {
                            width: 16px;
                            height: 16px;
                            
                            Path {
                                width: 14px;
                                height: 14px;
                                fill: CyberpunkPalette.text-secondary;
                                commands: "M 3 1 L 9 1 L 12 4 L 12 13 L 3 13 Z M 9 1 L 9 4 L 12 4";
                            }
                        }
                        
                        // File Name
                        Text {
                            text: result;
                            font-family: CyberpunkTypography.mono-font;
                            font-size: CyberpunkTypography.text-sm;
                            color: CyberpunkPalette.text-secondary;
                            vertical-alignment: center;
                        }
                    }
                    
                    touch-area := TouchArea {
                        clicked => {
                            file-selected(result);
                        }
                    }
                }
            }
        }
        
        // Search Status
        if searching: Text {
            text: "Searching...";
            font-family: CyberpunkTypography.secondary-font;
            font-size: CyberpunkTypography.text-sm;
            color: CyberpunkPalette.text-tertiary;
        }
    }
}

/// Drag and drop zone for file imports
/// 
/// Provides visual feedback for drag-and-drop operations with support for:
/// - Multiple file selection
/// - File type validation
/// - Visual drop indicators
/// - Integration with project management
export component DragDropZone inherits Rectangle {
    /// Whether a file is currently being dragged over the zone
    in-out property <bool> drag-active: false;
    /// Whether the zone accepts the current drag operation
    in-out property <bool> drag-accepted: true;
    /// List of accepted file extensions
    in-out property <[string]> accepted-extensions: ["rs", "c", "cpp", "go", "zig", "s", "h", "hpp"];
    /// Instructional text displayed in the zone
    in-out property <string> instruction-text: "Drop files here or click to browse";
    
    /// Callback when files are dropped
    callback files-dropped([string]);
    /// Callback when browse button is clicked
    callback browse-clicked();
    
    min-height: 120px;
    background: drag-active ? (drag-accepted ? CyberpunkPalette.glass-accent : rgba(255, 0, 64, 0.1)) : CyberpunkPalette.glass-dark;
    border-radius: 8px;
    border-width: 2px;
    border-color: drag-active ? (drag-accepted ? CyberpunkPalette.success-green : CyberpunkPalette.danger-red) : CyberpunkPalette.chrome-shadow;
    
    // Animated border styles
    animate border-color {
        duration: CyberpunkAnimations.fast;
        easing: CyberpunkAnimations.cyber-ease;
    }
    
    animate background {
        duration: CyberpunkAnimations.fast;
        easing: CyberpunkAnimations.cyber-ease;
    }
    
    // Glow effect during drag operations
    NeonGlow {
        glow-color: drag-accepted ? CyberpunkPalette.success-green : CyberpunkPalette.danger-red;
        active: drag-active;
        intensity: 0.6;
    }
    
    VerticalLayout {
        padding: 24px;
        spacing: 16px;
        alignment: center;
        
        // Drop Icon
        Rectangle {
            width: 48px;
            height: 48px;
            
            Path {
                width: 40px;
                height: 40px;
                fill: drag-active ? (drag-accepted ? CyberpunkPalette.success-green : CyberpunkPalette.danger-red) : CyberpunkPalette.chrome-mid;
                commands: "M 20 5 L 20 30 M 10 20 L 20 30 L 30 20 M 5 35 L 35 35";
                
                animate fill {
                    duration: CyberpunkAnimations.fast;
                }
            }
        }
        
        // Instruction Text
        Text {
            text: drag-active ? (drag-accepted ? "Release to add files" : "File type not supported") : instruction-text;
            font-family: CyberpunkTypography.secondary-font;
            font-size: CyberpunkTypography.text-base;
            color: drag-active ? (drag-accepted ? CyberpunkPalette.success-green : CyberpunkPalette.danger-red) : CyberpunkPalette.text-secondary;
            horizontal-alignment: center;
            
            animate color {
                duration: CyberpunkAnimations.fast;
            }
        }
        
        // Browse Button
        CyberButton {
            text: "Browse Files";
            primary: false;
            accent-color: CyberpunkPalette.electric-blue;
            clicked => {
                browse-clicked();
            }
        }
        
        // Supported Formats
        Text {
            text: "Supported: .onnx, .pt, .tflite, .h5, .pkl";
            font-family: CyberpunkTypography.mono-font;
            font-size: CyberpunkTypography.text-xs;
            color: CyberpunkPalette.text-tertiary;
            horizontal-alignment: center;
        }
    }
    
    // Touch handling for click-to-browse
    TouchArea {
        clicked => {
            browse-clicked();
        }
    }
}


// Cyberpunk Panel Component
export component CyberPanel inherits Rectangle {
    in-out property <string> title: "";
    in-out property <bool> collapsible: false;
    in-out property <bool> collapsed: false;
    in-out property <bool> glass-effect: true;
    
    callback toggle-collapse;
    
    // Panel Styling
    background: glass-effect ? CyberpunkPalette.glass-dark : CyberpunkPalette.deep-charcoal;
    border-radius: 8px;
    border-width: 1px;
    border-color: CyberpunkPalette.chrome-shadow;
    
    // Drop Shadow
    drop-shadow-blur: 20px;
    drop-shadow-color: rgba(0, 0, 0, 0.6);
    drop-shadow-offset-y: 8px;
    
    // Main Layout
    VerticalLayout {
        spacing: 0px;
        
        // Header
        Rectangle {
            height: 48px;
            background: CyberpunkPalette.carbon-steel;
            border-radius: 8px;
            
            // Header Glow
            NeonGlow {
                glow-color: CyberpunkPalette.electric-blue;
                active: true;
                intensity: 0.3;
            }
            
            HorizontalLayout {
                padding: 16px;
                alignment: space-between;
                
                // Title
                Text {
                    text: root.title;
                    font-family: CyberpunkTypography.primary-font;
                    font-size: CyberpunkTypography.text-lg;
                    font-weight: 700;
                    color: CyberpunkPalette.text-accent;
                    vertical-alignment: center;
                }
                
                // Collapse Button
                if collapsible: Rectangle {
                    width: 24px;
                    height: 24px;
                    background: Colors.transparent;
                    
                    // Chevron Icon
                    Path {
                        width: 16px;
                        height: 16px;
                        fill: CyberpunkPalette.text-secondary;
                        commands: collapsed ? "M 4 6 L 8 10 L 12 6" : "M 4 10 L 8 6 L 12 10";
                    }
                    
                    TouchArea {
                        clicked => {
                            toggle-collapse();
                        }
                    }
                }
            }
        }
        
        // Content Area
        Rectangle {
            background: Colors.transparent;
            visible: !collapsed;

            @children
        }
    }
}

// Progress Bar Component
export component CyberProgressBar inherits Rectangle {
    in-out property <float> progress: 0.0;
    in-out property <string> label: "";
    in-out property <color> bar-color: CyberpunkPalette.neon-cyan;
    in-out property <bool> animated: true;
    
    height: 24px;
    background: CyberpunkPalette.graphite-shadow;
    border-radius: 12px;
    border-width: 1px;
    border-color: CyberpunkPalette.chrome-shadow;
    
    // Progress Fill
    Rectangle {
        width: parent.width * progress;
        height: parent.height - 4px;
        x: 2px;
        y: 2px;
        background: bar-color;
        border-radius: 10px;
        
        // Animated gradient effect
        if animated: Rectangle {
            width: 40px;
            height: parent.height;
            background: @linear-gradient(90deg, Colors.transparent, rgba(255, 255, 255, 0.3), Colors.transparent);

            animate x {
                duration: CyberpunkAnimations.ultra-slow;
                iteration-count: -1;
                easing: linear;
            }
        }
        
        // Progress glow
        NeonGlow {
            glow-color: bar-color;
            active: progress > 0;
            intensity: 0.8;
        }
        
        animate width {
            duration: CyberpunkAnimations.medium;
            easing: CyberpunkAnimations.cyber-ease;
        }
    }
    
    // Label Text
    if label != "": Text {
        text: label;
        font-family: CyberpunkTypography.secondary-font;
        font-size: CyberpunkTypography.text-sm;
        color: CyberpunkPalette.text-primary;
        horizontal-alignment: center;
        vertical-alignment: center;
    }
}

// Input Field Component
export component CyberInputField inherits Rectangle {
    in-out property <string> text: "";
    in-out property <string> placeholder: "";
    in-out property <bool> focused: false;
    in-out property <bool> error: false;
    
    callback edited(string);
    
    min-height: 40px;
    background: CyberpunkPalette.graphite-shadow;
    border-radius: 4px;
    border-width: 2px;
    border-color: focused ? CyberpunkPalette.neon-cyan : (error ? CyberpunkPalette.danger-red : CyberpunkPalette.chrome-shadow);
    
    // Glow effect on focus
    NeonGlow {
        glow-color: error ? CyberpunkPalette.danger-red : CyberpunkPalette.neon-cyan;
        active: focused;
        intensity: 0.6;
    }
    
    animate border-color {
        duration: CyberpunkAnimations.fast;
        easing: CyberpunkAnimations.cyber-ease;
    }
    
    HorizontalLayout {
        padding: 12px;
        
        LineEdit {
            text: root.text;
            placeholder-text: root.placeholder;
            font-size: CyberpunkTypography.text-base;

            edited(text) => {
                root.text = text;
                root.edited(text);
            }
        }
    }
}

// Status Indicator Component
export component StatusIndicator inherits Rectangle {
    in-out property <string> status: "idle";
    in-out property <string> message: "";
    
    width: 200px;
    height: 32px;
    background: CyberpunkPalette.carbon-steel;
    border-radius: 16px;
    border-width:  1px;
    border-color:  CyberpunkPalette.chrome-shadow;
    
    HorizontalLayout {
        padding:   8px;
        spacing:   8px;
        alignment: center;
        
        // Status Indicator
        Rectangle {
            width: 16px;
            height: 16px;
            border-radius: 8px;
            background: status == "idle" ? CyberpunkPalette.chrome-mid : 
                       status == "compiling" ? CyberpunkPalette.warning-orange :
                       status == "success" ? CyberpunkPalette.success-green :
                       status == "error" ? CyberpunkPalette.danger-red :
                       CyberpunkPalette.electric-blue;
            
            // Pulsing animation for active states
            if status == "compiling" || status == "analyzing": NeonGlow {
                glow-color: self.background;
                active: true;
                intensity: 0.8;
                
                animate intensity {
                    duration: CyberpunkAnimations.ultra-slow;
                    iteration-count: -1;
                    easing: ease-in-out;
                }
            }
        }
        
        // Status Text
        Text {
            text: message;
            font-family: CyberpunkTypography.secondary-font;
            font-size:   CyberpunkTypography.text-sm;
            color:       CyberpunkPalette.text-secondary;
            vertical-alignment: center;
        }
    }
}

// File Tree Component
export component FileTreeItem inherits Rectangle {
    in-out property <string> name: "";
    in-out property <string> file-type: "";
    in-out property <bool> expanded: false;
    in-out property <bool> selected: false;
    in-out property <int> depth: 0;
    
    callback clicked;
    callback toggle-expanded;
    
    height: 32px;
    background: selected ? CyberpunkPalette.electric-blue : Colors.transparent;
    
    animate background {
        duration: CyberpunkAnimations.fast;
        easing:   CyberpunkAnimations.cyber-ease;
    }
    
    HorizontalLayout {
        padding-left: 16px + depth * 20px;
        padding-right: 16px;
        spacing: 8px;
        alignment: start;
        
        // Expand/Collapse Icon
        if file-type == "directory": Rectangle {
            width:  16px;
            height: 16px;
            
            Path {
                width:    12px;
                height:   12px;
                fill:     CyberpunkPalette.text-secondary;
                commands: expanded ? "M 2 4 L 6 8 L 10 4" : "M 4 2 L 8 6 L 4 10";
            }
            
            TouchArea {
                clicked => {
                    toggle-expanded();
                }
            }
        }
        
        // File Type Icon
        Rectangle {
            width: 16px;
            height: 16px;
            
            Path {
                width: 14px;
                height: 14px;
                fill: file-type == "rust" ? CyberpunkPalette.warning-orange :
                      file-type == "c" ? CyberpunkPalette.electric-blue :
                      file-type == "cpp" ? CyberpunkPalette.plasma-purple :
                      file-type == "directory" ? CyberpunkPalette.neon-cyan :
                      CyberpunkPalette.text-secondary;
                commands: file-type == "directory" ? "M 2 2 L 6 2 L 7 4 L 12 4 L 12 12 L 2 12 Z" :
                          "M 3 1 L 9 1 L 12 4 L 12 13 L 3 13 Z M 9 1 L 9 4 L 12 4";
            }
        }
        
        // File Name
        Text {
            text: name;
            font-family: CyberpunkTypography.mono-font;
            font-size:   CyberpunkTypography.text-sm;
            color: selected ? CyberpunkPalette.text-primary : CyberpunkPalette.text-secondary;
            vertical-alignment: center;
        }
    }
    
    TouchArea {
        clicked => {
            clicked();
        }
    }
}

// Code Editor Component
export component CodeEditor inherits Rectangle {
    in-out property <string> code: "";
    in-out property <string> language: "rust";
    in-out property <bool> show-line-numbers: true;
    in-out property <int> current-line: 1;
    
    callback code-changed(string);
    
    background: CyberpunkPalette.void-black;
    border-radius: 8px;
    border-width:  1px;
    border-color:  CyberpunkPalette.chrome-shadow;
    
    HorizontalLayout {
        spacing: 0px;
        
        // Line Numbers
        if show-line-numbers: Rectangle {
            width: 60px;
            background: CyberpunkPalette.deep-charcoal;
            // TODO: Add right border when Slint supports individual border sides
            
            // Line number implementation would go here
            Text {
                text: "1\n2\n3\n4\n5";
                font-family: CyberpunkTypography.mono-font;
                font-size: CyberpunkTypography.text-sm;
                color: CyberpunkPalette.text-tertiary;
                vertical-alignment: top;
                horizontal-alignment: right;
                x: 8px;
                y: 8px;
            }
        }
        
        // Editor Area
        ScrollView {
            TextEdit {
                text: root.code;
                font-size: CyberpunkTypography.text-base;

                edited => {
                    root.code = self.text;
                    root.code-changed(self.text);
                }
            }
        }
    }
}

// ================================================================================================
// MAIN APPLICATION WINDOW
// ================================================================================================

export component MainWindow inherits Window {
    // Window Properties
    title: "OmniForge - Advanced Binary Compiler & Analyzer";
    background: CyberpunkPalette.void-black;
    min-width:  1200px;
    min-height: 800px;
    
    // State Properties
    in-out property <string> compilation-status: "Ready";
    in-out property <float> compilation-progress: 0.0;
    in-out property <string> current-file: "";
    in-out property <int> files-processed: 0;
    in-out property <int> total-files: 0;
    in-out property <string> processing-stage: "Ready";
    in-out property <float> cpu-usage: 0.0;
    in-out property <float> memory-usage: 0.0;
    in-out property <float> compilation-speed: 0.0;
    in-out property <float> cache-hit-rate: 0.0;
    in-out property <string> project-name: "";
    in-out property <string> project-language: "";
    in-out property <int> project-files: 0;
    in-out property <[string]> active-files: [];
    in-out property <[string]> analysis-results: [];
    
    // Theme Properties
    in-out property <color> primary-color: CyberpunkPalette.neon-cyan;
    in-out property <color> secondary-color: CyberpunkPalette.electric-blue;
    in-out property <color> accent-color: CyberpunkPalette.plasma-purple;
    in-out property <float> neon-intensity: 0.8;
    in-out property <float> chrome-reflectivity: 0.6;
    in-out property <bool> glitch-effects: true;
    in-out property <float> animation-speed: 1.0;
    in-out property <string> current-theme: "classic";
    
    // Callbacks
    callback create-project(string, string, string);
    callback add-file(string);
    callback add-files([string]);
    callback search-files(string, string);
    callback browse-files();
    callback start-compilation();
    callback start-analysis(string);
    callback switch-theme(string);
    callback update-preferences(string);
    
    // File search and drag-drop state
    in-out property <[string]> search-results: [];
    in-out property <bool> search-in-progress: false;
    in-out property <bool> drag-over-active: false;
    in-out property <bool> drag-files-accepted: true;
    
    // Main Layout
    VerticalLayout {
        spacing: 0px;
        
        // Header Bar
        Rectangle {
            height: 60px;
            background: CyberpunkPalette.deep-charcoal;
            // TODO: Add bottom border when Slint supports individual border sides
            
            // Header glow effect
            NeonGlow {
                glow-color: primary-color;
                active:     true;
                intensity:  0.2;
            }
            
            HorizontalLayout {
                padding:   16px;
                alignment: space-between;
                
                // Logo and Title
                HorizontalLayout {
                    spacing: 16px;
                    alignment: start;
                    
                    // Logo
                    Rectangle {
                        width:         32px;
                        height:        32px;
                        background:    primary-color;
                        border-radius: 16px;
                        
                        NeonGlow {
                            glow-color: primary-color;
                            active: true;
                            intensity: 1.0;
                        }
                    }
                    
                    // Title
                    Text {
                        text: "OmniForge";
                        font-family: CyberpunkTypography.primary-font;
                        font-size: CyberpunkTypography.text-2xl;
                        font-weight: 700;
                        color: primary-color;
                    }
                    
                    // Subtitle
                    Text {
                        text: "Advanced Binary Compiler & Analyzer";
                        font-family: CyberpunkTypography.secondary-font;
                        font-size: CyberpunkTypography.text-base;
                        color: CyberpunkPalette.text-secondary;
                        vertical-alignment: bottom;
                    }
                }
                
                // Status and Controls
                HorizontalLayout {
                    spacing: 16px;
                    alignment: end;
                    
                    // Status Indicator
                    StatusIndicator {
                        status: compilation-progress > 0 ? "compiling" : "idle";
                        message: compilation-status;
                    }
                    
                    // Theme Selector
                    ComboBox {
                        model: ["Classic", "Neon", "Chrome", "Glitch", "Minimal"];
                        current-value: current-theme;
                        selected(value) => {
                            switch-theme(value);
                        }
                    }
                }
            }
        }
        
        // Main Content Area
        HorizontalLayout {
            spacing: 0px;
            
            // Left Sidebar
            Rectangle {
                width: 300px;
                background: CyberpunkPalette.deep-charcoal;
                // TODO: Add right border when Slint supports individual border sides
                
                VerticalLayout {
                    spacing: 0px;
                    
                    // Project Panel
                    CyberPanel {
                        height: 200px;
                        title: "Project";
                        
                        VerticalLayout {
                            padding: 16px;
                            spacing: 12px;
                            
                            // Project Info
                            if project-name != "": VerticalLayout {
                                spacing: 8px;
                                
                                Text {
                                    text: project-name;
                                    font-family: CyberpunkTypography.primary-font;
                                    font-size: CyberpunkTypography.text-lg;
                                    color: CyberpunkPalette.text-primary;
                                }
                                
                                Text {
                                    text: project-language + " • " + project-files + " files";
                                    font-family: CyberpunkTypography.secondary-font;
                                    font-size: CyberpunkTypography.text-sm;
                                    color: CyberpunkPalette.text-secondary;
                                }
                            }
                            
                            // Action Buttons
                            HorizontalLayout {
                                spacing: 8px;
                                
                                CyberButton {
                                    text: "New Project";
                                    primary: true;
                                    clicked => {
                                        create-project("New Project", "/path/to/project", "rust");
                                    }
                                }
                                
                                CyberButton {
                                    text: "Add File";
                                    primary: false;
                                    clicked => {
                                        add-file("/path/to/file.rs");
                                    }
                                }
                            }
                        }
                    }
                    
                    // File Search Panel
                    CyberPanel {
                        height: 280px;
                        title: "File Search";
                        
                        FileSearchPanel {
                            search-results: root.search-results;
                            searching: root.search-in-progress;
                            
                            file-selected(file-path) => {
                                root.add-file(file-path);
                            }
                            
                            search-initiated(query, filter) => {
                                root.search-files(query, filter);
                            }
                        }
                    }
                    
                    // Drag and Drop Zone
                    CyberPanel {
                        height: 200px;
                        title: "Import Files";
                        
                        DragDropZone {
                            drag-active: root.drag-over-active;
                            drag-accepted: root.drag-files-accepted;
                            accepted-extensions: ["rs", "c", "cpp", "cxx", "cc", "go", "zig", "s", "asm", "h", "hpp", "hxx"];
                            
                            files-dropped(file-paths) => {
                                root.add-files(file-paths);
                            }
                            
                            browse-clicked() => {
                                root.browse-files();
                            }
                        }
                    }
                    
                    // File Explorer
                    CyberPanel {
                        title: "Project Files";
                        
                        ScrollView {
                            ListView {
                                for file in active-files: FileTreeItem {
                                    name: file;
                                    file-type: "rust";
                                    clicked => {
                                        // Handle file selection
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // Center Content
            VerticalLayout {
                spacing: 0px;
                
                // Tab Bar
                Rectangle {
                    height: 40px;
                    background: CyberpunkPalette.carbon-steel;
                    // TODO: Add bottom border when Slint supports individual border sides
                    
                    HorizontalLayout {
                        padding: 8px;
                        spacing: 4px;
                        
                        // Tab buttons would go here
                        CyberButton {
                            text: "Editor";
                            primary: true;
                        }
                        
                        CyberButton {
                            text: "Analysis";
                            primary: false;
                        }
                        
                        CyberButton {
                            text: "Output";
                            primary: false;
                        }
                    }
                }
                
                // Main Content
                TabWidget {
                    Tab {
                        title: "Code Editor";
                        
                        CodeEditor {
                            code: "// Welcome to OmniForge\nfn main() {\n    println!(\"Hello, World!\");\n}";
                            language: "rust";
                            show-line-numbers: true;
                        }
                    }
                    
                    Tab {
                        title: "Binary Analysis";
                        
                        ScrollView {
                            VerticalLayout {
                                padding: 16px;
                                spacing: 16px;
                                
                                // Analysis Results
                                CyberPanel {
                                    title: "Analysis Results";
                                    
                                    VerticalLayout {
                                        padding: 16px;
                                        spacing: 12px;
                                        
                                        for result in analysis-results: Text {
                                            text: result;
                                            font-family: CyberpunkTypography.mono-font;
                                            font-size: CyberpunkTypography.text-sm;
                                            color: CyberpunkPalette.text-secondary;
                                        }
                                    }
                                }
                                
                                // Security Analysis
                                CyberPanel {
                                    title: "Security Analysis";
                                    
                                    VerticalLayout {
                                        padding: 16px;
                                        spacing: 12px;
                                        
                                        Text {
                                            text: "Security Score: 85/100";
                                            font-family: CyberpunkTypography.secondary-font;
                                            font-size: CyberpunkTypography.text-lg;
                                            color: CyberpunkPalette.success-green;
                                        }
                                        
                                        Text {
                                            text: "No critical vulnerabilities detected";
                                            font-family: CyberpunkTypography.secondary-font;
                                            font-size: CyberpunkTypography.text-base;
                                            color: CyberpunkPalette.text-secondary;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    Tab {
                        title: "Compilation Output";
                        
                        ScrollView {
                            VerticalLayout {
                                padding: 16px;
                                spacing: 16px;
                                
                                // Compilation Progress
                                CyberPanel {
                                    title: "Compilation Progress";
                                    
                                    VerticalLayout {
                                        padding: 16px;
                                        spacing: 12px;
                                        
                                        CyberProgressBar {
                                            progress: compilation-progress;
                                            label: processing-stage;
                                            bar-color: primary-color;
                                        }
                                        
                                        Text {
                                            text: "Processing: " + current-file;
                                            font-family: CyberpunkTypography.mono-font;
                                            font-size: CyberpunkTypography.text-sm;
                                            color: CyberpunkPalette.text-secondary;
                                        }
                                        
                                        Text {
                                            text: "Files: " + files-processed + " / " + total-files;
                                            font-family: CyberpunkTypography.mono-font;
                                            font-size: CyberpunkTypography.text-sm;
                                            color: CyberpunkPalette.text-secondary;
                                        }
                                    }
                                }
                                
                                // Compilation Controls
                                CyberPanel {
                                    title: "Controls";
                                    
                                    HorizontalLayout {
                                        padding: 16px;
                                        spacing: 12px;
                                        
                                        CyberButton {
                                            text: "Compile";
                                            primary: true;
                                            accent-color: CyberpunkPalette.success-green;
                                            clicked => {
                                                start-compilation();
                                            }
                                        }
                                        
                                        CyberButton {
                                            text: "Analyze";
                                            primary: false;
                                            accent-color: CyberpunkPalette.electric-blue;
                                            clicked => {
                                                start-analysis("binary.exe");
                                            }
                                        }
                                        
                                        CyberButton {
                                            text: "Optimize";
                                            primary: false;
                                            accent-color: CyberpunkPalette.warning-orange;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // Right Sidebar
            Rectangle {
                width: 280px;
                background: CyberpunkPalette.deep-charcoal;
                // TODO: Add left border when Slint supports individual border sides
                
                VerticalLayout {
                    spacing: 0px;
                    
                    // Performance Monitor
                    CyberPanel {
                        height: 200px;
                        title: "Performance";
                        
                        VerticalLayout {
                            padding: 16px;
                            spacing: 12px;
                            
                            // CPU Usage
                            HorizontalLayout {
                                spacing: 8px;
                                
                                Text {
                                    text: "CPU:";
                                    font-family: CyberpunkTypography.secondary-font;
                                    font-size: CyberpunkTypography.text-sm;
                                    color: CyberpunkPalette.text-secondary;
                                }
                                
                                CyberProgressBar {
                                    progress: cpu-usage;
                                    bar-color: cpu-usage > 0.8 ? CyberpunkPalette.danger-red : CyberpunkPalette.success-green;
                                }
                            }
                            
                            // Memory Usage
                            HorizontalLayout {
                                spacing: 8px;
                                
                                Text {
                                    text: "Memory:";
                                    font-family: CyberpunkTypography.secondary-font;
                                    font-size: CyberpunkTypography.text-sm;
                                    color: CyberpunkPalette.text-secondary;
                                }
                                
                                CyberProgressBar {
                                    progress: memory-usage / 1024 / 1024 / 1024; // Convert to GB
                                    bar-color: CyberpunkPalette.electric-blue;
                                }
                            }
                            
                            // Compilation Speed
                            Text {
                                text: "Speed: " + compilation-speed + " files/sec";
                                font-family: CyberpunkTypography.mono-font;
                                font-size: CyberpunkTypography.text-sm;
                                color: CyberpunkPalette.text-secondary;
                            }
                            
                            // Cache Hit Rate
                            Text {
                                text: "Cache: " + cache-hit-rate * 100 + "%";
                                font-family: CyberpunkTypography.mono-font;
                                font-size: CyberpunkTypography.text-sm;
                                color: CyberpunkPalette.text-secondary;
                            }
                        }
                    }
                    
                    // Settings Panel
                    CyberPanel {
                        title: "Settings";
                        
                        ScrollView {
                            VerticalLayout {
                                padding: 16px;
                                spacing: 12px;
                                
                                // Theme Controls
                                Text {
                                    text: "Theme Settings";
                                    font-family: CyberpunkTypography.primary-font;
                                    font-size: CyberpunkTypography.text-base;
                                    color: CyberpunkPalette.text-primary;
                                }
                                
                                HorizontalLayout {
                                    spacing: 8px;
                                    
                                    Text {
                                        text: "Neon:";
                                        font-size: CyberpunkTypography.text-sm;
                                        color: CyberpunkPalette.text-secondary;
                                    }
                                    
                                    Slider {
                                        value: neon-intensity;
                                        minimum: 0.0;
                                        maximum: 2.0;
                                        changed(value) => {
                                            neon-intensity = value;
                                        }
                                    }
                                }
                                
                                HorizontalLayout {
                                    spacing: 8px;
                                    
                                    Text {
                                        text: "Chrome:";
                                        font-size: CyberpunkTypography.text-sm;
                                        color: CyberpunkPalette.text-secondary;
                                    }
                                    
                                    Slider {
                                        value: chrome-reflectivity;
                                        minimum: 0.0;
                                        maximum: 1.0;
                                        changed(value) => {
                                            chrome-reflectivity = value;
                                        }
                                    }
                                }
                                
                                CheckBox {
                                    text: "Glitch Effects";
                                    checked: glitch-effects;
                                    toggled => {
                                        glitch-effects = !glitch-effects;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Bottom Status Bar
        Rectangle {
            height: 32px;
            background: CyberpunkPalette.carbon-steel;
            // TODO: Add top border when Slint supports individual border sides
            
            HorizontalLayout {
                padding: 8px;
                alignment: space-between;
                
                // Left Status
                Text {
                    text: "Ready • " + project-language + " • " + project-files + " files";
                    font-family: CyberpunkTypography.secondary-font;
                    font-size: CyberpunkTypography.text-sm;
                    color: CyberpunkPalette.text-secondary;
                }
                
                // Right Status
                Text {
                    text: "OmniForge v2.0 • ArcMoon Studios";
                    font-family: CyberpunkTypography.secondary-font;
                    font-size: CyberpunkTypography.text-sm;
                    color: CyberpunkPalette.text-tertiary;
                }
            }
        }
    }
}