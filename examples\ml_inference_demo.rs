// examples/ml_inference_demo.rs
//! # ML Inference Demo
//!
//! This example demonstrates how to use the Umlaiie unified ML inference engine
//! to load and run inference on different model formats.
//!
//! ## Usage
//!
//! ```bash
//! cargo run --example ml_inference_demo --features ml-inference
//! ```
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

#[cfg(feature = "ml-inference")]
use omni_forge::models::{
    Umlaiie, LoadOptions, Device, ModelMetadata,
    gguf::GgufModel, safetensors::SafeTensorsModel,
    loader::{get_supported_formats, is_format_supported}
};

#[cfg(feature = "ml-inference")]
use ndarray::ArrayD;

#[cfg(feature = "ml-inference")]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    env_logger::init();
    
    println!("🌙 Umlaiie ML Inference Engine Demo");
    println!("=====================================");
    
    // Show supported formats
    println!("\n📋 Supported Model Formats:");
    for format in get_supported_formats() {
        println!("  • {}", format);
    }
    
    // Test format support checking
    println!("\n🔍 Format Support Check:");
    println!("  • GGUF supported: {}", is_format_supported("gguf"));
    println!("  • SafeTensors supported: {}", is_format_supported("safetensors"));
    println!("  • ONNX supported: {}", is_format_supported("onnx"));
    
    // Create load options
    let options = LoadOptions {
        device: Device::Cpu,
        quantized: None,
    };
    
    println!("\n⚙️  Load Options:");
    println!("  • Device: {:?}", options.device);
    println!("  • Quantization: {:?}", options.quantized);
    
    // Demo GGUF model (simulated)
    println!("\n🔧 GGUF Model Demo:");
    demo_gguf_model(&options)?;
    
    // Demo SafeTensors model (simulated)
    println!("\n🔧 SafeTensors Model Demo:");
    demo_safetensors_model(&options)?;
    
    println!("\n✅ Demo completed successfully!");
    
    Ok(())
}

#[cfg(feature = "ml-inference")]
fn demo_gguf_model(options: &LoadOptions) -> Result<(), Box<dyn std::error::Error>> {
    // Create a dummy GGUF file for demonstration
    let dummy_gguf_data = create_dummy_gguf_file()?;
    let temp_path = std::env::temp_dir().join("demo_model.gguf");
    std::fs::write(&temp_path, dummy_gguf_data)?;
    
    // Load the model
    match GgufModel::load(&temp_path, options.clone()) {
        Ok(model) => {
            println!("  ✅ Successfully loaded GGUF model");
            
            // Get metadata
            if let Ok(metadata) = model.metadata() {
                print_metadata(&metadata);
            }
            
            // Create dummy input tensor
            let input = ArrayD::ones(vec![1, 512]); // Batch size 1, sequence length 512
            println!("  📥 Input tensor shape: {:?}", input.shape());
            
            // Run inference
            match model.infer(&[input]) {
                Ok(outputs) => {
                    println!("  ✅ Inference successful!");
                    println!("  📤 Output tensors: {}", outputs.len());
                    for (i, output) in outputs.iter().enumerate() {
                        println!("    • Output {}: shape {:?}", i, output.shape());
                    }
                },
                Err(e) => println!("  ❌ Inference failed: {}", e),
            }
            
            // Show model utilities
            println!("  📊 Model info:");
            println!("    • Path: {}", model.model_path().display());
            println!("    • Size: {} bytes", model.model_size());
            println!("    • Supports q4_0: {}", model.supports_quantization("q4_0"));
        },
        Err(e) => println!("  ❌ Failed to load GGUF model: {}", e),
    }
    
    // Clean up
    let _ = std::fs::remove_file(&temp_path);
    
    Ok(())
}

#[cfg(feature = "ml-inference")]
fn demo_safetensors_model(options: &LoadOptions) -> Result<(), Box<dyn std::error::Error>> {
    // Create a dummy SafeTensors file for demonstration
    let dummy_safetensors_data = create_dummy_safetensors_file()?;
    let temp_path = std::env::temp_dir().join("demo_model.safetensors");
    std::fs::write(&temp_path, dummy_safetensors_data)?;
    
    // Load the model
    match SafeTensorsModel::load(&temp_path, options.clone()) {
        Ok(model) => {
            println!("  ✅ Successfully loaded SafeTensors model");
            
            // Get metadata
            if let Ok(metadata) = model.metadata() {
                print_metadata(&metadata);
            }
            
            // Create dummy input tensor
            let input = ArrayD::ones(vec![1, 256]); // Batch size 1, feature size 256
            println!("  📥 Input tensor shape: {:?}", input.shape());
            
            // Run inference
            match model.infer(&[input]) {
                Ok(outputs) => {
                    println!("  ✅ Inference successful!");
                    println!("  📤 Output tensors: {}", outputs.len());
                    for (i, output) in outputs.iter().enumerate() {
                        println!("    • Output {}: shape {:?}", i, output.shape());
                    }
                },
                Err(e) => println!("  ❌ Inference failed: {}", e),
            }
            
            // Show model utilities
            println!("  📊 Model info:");
            println!("    • Path: {}", model.model_path().display());
            println!("    • Tensor count: {}", model.tensor_count());
            println!("    • Tensor names: {:?}", model.tensor_names());
        },
        Err(e) => println!("  ❌ Failed to load SafeTensors model: {}", e),
    }
    
    // Clean up
    let _ = std::fs::remove_file(&temp_path);
    
    Ok(())
}

#[cfg(feature = "ml-inference")]
fn print_metadata(metadata: &ModelMetadata) {
    println!("  📋 Model Metadata:");
    println!("    • Name: {}", metadata.name);
    println!("    • Version: {}", metadata.version);
    println!("    • Data type: {}", metadata.dtype);
    println!("    • Input shapes: {:?}", metadata.input_shapes);
    println!("    • Output shapes: {:?}", metadata.output_shapes);
    if !metadata.extra.is_empty() {
        println!("    • Extra info: {} entries", metadata.extra.len());
    }
}

#[cfg(feature = "ml-inference")]
fn create_dummy_gguf_file() -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    // Create a minimal GGUF-like file with magic number
    let mut data = Vec::new();
    data.extend_from_slice(b"GGUF"); // Magic number
    data.extend_from_slice(&[0u8; 12]); // Padding to make it 16 bytes minimum
    data.extend_from_slice(b"dummy model data for testing purposes");
    Ok(data)
}

#[cfg(feature = "ml-inference")]
fn create_dummy_safetensors_file() -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    use safetensors::tensor::{Dtype, TensorView};
    use std::collections::HashMap;
    
    // Create a simple tensor
    let data: Vec<f32> = vec![1.0, 2.0, 3.0, 4.0];
    let tensor_data: &[u8] = unsafe {
        std::slice::from_raw_parts(
            data.as_ptr() as *const u8,
            data.len() * std::mem::size_of::<f32>(),
        )
    };
    
    // Create tensor info
    let mut tensors = HashMap::new();
    tensors.insert(
        "test_tensor".to_string(),
        TensorView::new(Dtype::F32, vec![2, 2], tensor_data)?,
    );
    
    // Serialize to SafeTensors format
    let serialized = safetensors::serialize(&tensors, None)?;
    Ok(serialized)
}

#[cfg(not(feature = "ml-inference"))]
fn main() {
    println!("❌ ML inference feature is not enabled.");
    println!("Run with: cargo run --example ml_inference_demo --features ml-inference");
}
