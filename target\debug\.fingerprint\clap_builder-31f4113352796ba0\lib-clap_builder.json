{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 2963997777145268897, "deps": [[5820056977320921005, "anstream", false, 10532606195261801097], [9394696648929125047, "anstyle", false, 7482209091175108164], [11166530783118767604, "strsim", false, 11054323293240964324], [11649982696571033535, "clap_lex", false, 8396261954033448325]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-31f4113352796ba0\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}