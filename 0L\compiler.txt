
Directory: src\compiler
File: mod.rs
============
// src/compiler/mod.rs
//! Compiler module for the OmniForge framework.
//!
//! This module provides the core functionality for the OmniForge compiler,
//! orchestrating the analysis of binaries, extraction of metadata, generation
//! of code, and creation of OmniCodex dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::{Path, PathBuf};
use std::collections::HashMap;
use rayon::prelude::*;

use crate::error::{OmniError, OmniResult};
use crate::config::CompilerOptions;
use crate::binary_analyzer::BinaryAnalyzer;
use crate::metadata_extractor::{MetadataExtractor, MetadataExtractionOptions, ExtractedMetadata};
use crate::codegen::{Codegen, CodegenOptions};
use crate::ahaw::{self, TaskCharacteristics};

/// OmniCompiler is the main entry point for the OmniForge compiler.
///
/// It orchestrates the analysis of binaries, extraction of metadata,
/// generation of code, and creation of OmniCodex dispatch tables.
pub struct OmniCompiler {
    /// Options for the compiler
    options: CompilerOptions,
    
    /// Binary analyzer (planned for future binary analysis features)
    #[allow(dead_code)]
    binary_analyzer: BinaryAnalyzer,
    
    /// Metadata extractor
    metadata_extractor: MetadataExtractor,
    
    /// Code generator
    codegen: Codegen,
    
    /// Input files
    inputs: Vec<PathBuf>,
    
    /// Extracted metadata
    metadata: HashMap<PathBuf, ExtractedMetadata>,
}

impl OmniCompiler {
    /// Create a new OmniCompiler with the given options.
    pub fn new(options: CompilerOptions) -> Self {
        // Create metadata extraction options from compiler options
        let extraction_options = MetadataExtractionOptions {
            extract_memory_layouts: true,
            extract_launch_parameters: true,
            extract_function_signatures: true,
            extract_type_info: true,
        };
        
        // Create codegen options from compiler options
        let codegen_options = CodegenOptions {
            target_language: options.target_language.clone(),
            generate_docs: true,
            generate_wrappers: true,
            generate_error_handling: true,
            include_file_paths: true,
        };
        
        Self {
            options,
            binary_analyzer: BinaryAnalyzer::new(),
            metadata_extractor: MetadataExtractor::new(extraction_options),
            codegen: Codegen::new(codegen_options),
            inputs: Vec::new(),
            metadata: HashMap::new(),
        }
    }
    
    /// Add an input file to the compiler.
    ///
    /// This will analyze the file and extract metadata.
    pub fn add_input(&mut self, path: &Path) -> OmniResult<()> {
        let path = path.to_path_buf();
        
        // Check if we've already analyzed this file
        if self.inputs.contains(&path) {
            log::info!("Already analyzed {}, skipping", path.display());
            return Ok(());
        }
        
        // Ensure the file exists
        if !path.exists() {
            return Err(OmniError::Io(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("File not found: {}", path.display()),
            )));
        }
        
        // Add the file to the input list
        self.inputs.push(path.clone());
        
        // Analyze the file
        log::info!("Analyzing {}", path.display());
        let metadata = self.metadata_extractor.extract_metadata(&path)?;
        
        // Store the metadata
        self.metadata.insert(path, metadata);
        
        Ok(())
    }
    
    /// Add multiple input files to the compiler.
    ///
    /// This will analyze each file and extract metadata, potentially in parallel.
    pub fn add_inputs<P: AsRef<Path> + Sync>(&mut self, paths: &[P]) -> OmniResult<()> {
        let new_paths: Vec<_> = paths
            .iter()
            .map(|p| p.as_ref())
            .filter(|p| !self.inputs.contains(&p.to_path_buf()))
            .collect();

        // Process new files in parallel using rayon
        let results: Vec<OmniResult<(PathBuf, ExtractedMetadata)>> = new_paths
            .par_iter()
            .map(|path| {
                // Ensure the file exists
                if !path.exists() {
                    return Err(OmniError::Io(std::io::Error::new(
                        std::io::ErrorKind::NotFound,
                        format!("File not found: {}", path.display()),
                    )));
                }
                
                // Analyze the file
                log::info!("Analyzing {}", path.display());
                let metadata = self.metadata_extractor.extract_metadata(path)?;
                
                Ok((path.to_path_buf(), metadata))
            })
            .collect();
        
        // Process results serially to update self
        for result in results {
            match result {
                Ok((path, metadata)) => {
                    self.inputs.push(path.clone());
                    self.metadata.insert(path, metadata);
                }
                Err(err) => {
                    // Log the error and continue, or return immediately depending on desired behavior.
                    // Returning immediately is safer.
                    log::error!("Failed to process input file: {}", err);
                    return Err(err);
                }
            }
        }
        
        Ok(())
    }
    
    /// Clear all inputs and metadata.
    pub fn clear_inputs(&mut self) {
        self.inputs.clear();
        self.metadata.clear();
    }
    
    /// Get the list of input files.
    pub fn get_inputs(&self) -> &[PathBuf] {
        &self.inputs
    }
    
    /// Get the extracted metadata for a file.
    pub fn get_metadata(&self, path: &Path) -> Option<&ExtractedMetadata> {
        self.metadata.get(path)
    }
    
    /// Get all extracted metadata.
    pub fn get_all_metadata(&self) -> Vec<ExtractedMetadata> {
        self.metadata.values().cloned().collect()
    }
    
    /// Generate the OmniCodex dispatch table.
    ///
    /// This will generate a file containing the OmniCodex dispatch table
    /// and any necessary wrapper functions.
    pub fn generate_codex(&self, output_path: &Path) -> OmniResult<()> {
        // Ensure we have metadata to generate code from
        if self.metadata.is_empty() {
            return Err(OmniError::General("No metadata to generate code from".to_string()));
        }
        
        // Get all metadata
        let metadata = self.get_all_metadata();
        
        // Generate codex
        self.codegen.generate_codex(&metadata, output_path)?;
        
        Ok(())
    }
    
    /// Compile the input files and generate the OmniCodex dispatch table.
    ///
    /// This is a convenience method that combines add_inputs and generate_codex.
    pub fn compile<P: AsRef<Path> + Sync>(&mut self, inputs: &[P], output_path: &Path) -> OmniResult<()> {
        // Add inputs
        self.add_inputs(inputs)?;
        
        // Generate codex
        self.generate_codex(output_path)?;
        
        Ok(())
    }
    
    /// Get the compiler options.
    pub fn get_options(&self) -> &CompilerOptions {
        &self.options
    }
    
    /// Set the compiler options.
    ///
    /// This will update the options for the metadata extractor and code generator.
    pub fn set_options(&mut self, options: CompilerOptions) {
        // Update metadata extraction options
        let extraction_options = MetadataExtractionOptions {
            extract_memory_layouts: true,
            extract_launch_parameters: true,
            extract_function_signatures: true,
            extract_type_info: true,
        };
        self.metadata_extractor = MetadataExtractor::new(extraction_options);
        
        // Update codegen options
        let codegen_options = CodegenOptions {
            target_language: options.target_language.clone(),
            generate_docs: true,
            generate_wrappers: true,
            generate_error_handling: true,
            include_file_paths: true,
        };
        self.codegen = Codegen::new(codegen_options);
        
        // Update compiler options
        self.options = options;
    }

    /// Accelerated optimization passes for compilation
    pub fn accelerated_optimization_passes(&mut self, optimization_data: &[f32]) -> OmniResult<Vec<f32>> {
        let mut data = optimization_data.to_vec();

        // Define characteristics for optimization passes
        let characteristics = TaskCharacteristics {
            data_size: data.len(),
            compute_intensity: 0.7,
            parallelizability: 0.85,
            ..Default::default()
        };

        // Use acceleration for optimization passes on large codebases
        if data.len() > 5000 {
            let operation = ahaw::VectorOperation::MatrixMultiply;
            let hint = ahaw::AccelerationHint::Auto;
            match ahaw::compiler::accelerate_optimization_passes(&mut data, operation, &hint, characteristics) {
                Ok(result) => {
                    println!("🚀 Accelerated optimization passes: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                    println!("   Throughput: {:.2} GFLOPS, power: {:.1}W",
                            result.performance_metrics.throughput_gflops,
                            result.performance_metrics.power_consumption_watts);
                },
                Err(e) => {
                    println!("⚠️ Optimization acceleration failed: {}", e);
                }
            }
        }

        Ok(data)
    }

    /// Accelerated parallel compilation with dynamic workload distribution
    pub fn accelerated_parallel_compile<P: AsRef<Path> + Sync>(&mut self, inputs: &[P]) -> OmniResult<()> {
        // Convert inputs to processing vectors for acceleration
        let input_vectors: Vec<f32> = inputs.iter()
            .enumerate()
            .map(|(i, _)| i as f32)
            .collect();

        // Create task characteristics for compilation workload
        let characteristics = TaskCharacteristics {
            data_size: inputs.len(),
            compute_intensity: 0.8,
            parallelizability: 0.95,
            memory_access_pattern: "sequential".to_string(),
            priority: "high".to_string(),
            expected_duration_ms: inputs.len() as f64 * 10.0,
            ..Default::default()
        };

        // Use acceleration for large compilation jobs
        if inputs.len() > 10 {
            let mut data = input_vectors;
            let operation = ahaw::VectorOperation::MatrixMultiply;
            let hint = ahaw::AccelerationHint::PreferGPU;
            match ahaw::compiler::accelerate_optimization_passes(&mut data, operation, &hint, characteristics) {
                Ok(result) => {
                    println!("🚀 Accelerated parallel compilation: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Parallel compilation acceleration failed: {}", e);
                }
            }
        }

        // Perform actual parallel compilation
        self.add_inputs(inputs)?;

        Ok(())
    }
}

impl Default for OmniCompiler {
    fn default() -> Self {
        Self::new(CompilerOptions::default())
    }
}


