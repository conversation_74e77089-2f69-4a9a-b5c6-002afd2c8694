// src/binary_analyzer/mod.rs
//! Binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing binary files and extracting
//! metadata such as exported functions, memory layouts, and other information
//! needed for generating OmniCodex dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::io::Read;
use serde::{Serialize, Deserialize};
use object::{Object, ObjectSymbol};
use crate::error::{OmniError, OmniResult};
use crate::ahaw::{self};

mod pe_analyzer;
mod elf_analyzer;
mod macho_analyzer;
mod ptx_analyzer;

pub use self::pe_analyzer::PEAnalyzer;
pub use self::elf_analyzer::ELFAnalyzer;
pub use self::macho_analyzer::MachOAnalyzer;
pub use self::ptx_analyzer::PTXAnalyzer;

/// Supported binary types
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BinaryType {
    /// Windows PE executable/DLL
    PE,
    /// Linux ELF executable/shared object
    ELF,
    /// macOS Mach-O executable/dylib
    MachO,
    /// NVIDIA PTX file
    PTX,
    /// NVIDIA cubin file
    Cubin,
    /// Object file
    Object,
    /// Unknown binary type
    Unknown,
}

/// Extracted metadata from a binary file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinaryMetadata {
    /// Binary type
    pub binary_type: BinaryType,
    
    /// Path to the binary file
    pub path: String,
    
    /// Exported functions
    pub exports: Vec<ExportedFunction>,
    
    /// Imported functions
    pub imports: Vec<ImportedFunction>,
    
    /// Dependencies
    pub dependencies: Vec<String>,
    
    /// Additional metadata specific to the binary type
    pub additional_metadata: serde_json::Value,
}

/// Information about an exported function
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportedFunction {
    /// Function name
    pub name: String,
    
    /// Function address
    pub address: u64,
    
    /// Function signature (if available)
    pub signature: Option<FunctionSignature>,
    
    /// Calling convention (if available)
    pub calling_convention: Option<CallingConvention>,
    
    /// Additional function metadata
    pub metadata: serde_json::Value,
}

/// Information about an imported function
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportedFunction {
    /// Function name
    pub name: String,
    
    /// Library name
    pub library: String,
    
    /// Function signature (if available)
    pub signature: Option<FunctionSignature>,
}

/// Function signature information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionSignature {
    /// Return type
    pub return_type: TypeInfo,
    
    /// Parameter types
    pub parameter_types: Vec<TypeInfo>,
    
    /// Is variadic
    pub is_variadic: bool,
}

/// Type information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeInfo {
    /// Type name
    pub name: String,
    
    /// Type size in bytes
    pub size: Option<usize>,
    
    /// Type alignment in bytes
    pub alignment: Option<usize>,
    
    /// Is pointer
    pub is_pointer: bool,
    
    /// Is array
    pub is_array: bool,
    
    /// Array dimensions (if is_array is true)
    pub array_dimensions: Vec<usize>,
}

/// Symbol type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SymbolType {
    /// Function symbol
    Function,
    /// Variable symbol
    Variable,
    /// Constant symbol
    Constant,
    /// Import symbol
    Import,
    /// Export symbol
    Export,
}

/// Calling convention
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CallingConvention {
    /// C calling convention
    C,
    /// stdcall calling convention
    Stdcall,
    /// fastcall calling convention
    Fastcall,
    /// thiscall calling convention
    Thiscall,
    /// vectorcall calling convention
    Vectorcall,
    /// CUDA kernel
    CudaKernel,
    /// Unknown calling convention
    Unknown,
}

/// Binary analyzer
pub struct BinaryAnalyzer {
    pe_analyzer: PEAnalyzer,
    elf_analyzer: ELFAnalyzer,
    macho_analyzer: MachOAnalyzer,
    ptx_analyzer: PTXAnalyzer,
}

impl BinaryAnalyzer {
    /// Create a new binary analyzer
    pub fn new() -> Self {
        Self {
            pe_analyzer: PEAnalyzer::new(),
            elf_analyzer: ELFAnalyzer::new(),
            macho_analyzer: MachOAnalyzer::new(),
            ptx_analyzer: PTXAnalyzer::new(),
        }
    }
    
    /// Analyze a binary file and extract metadata
    pub fn analyze_binary(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        // Determine the binary type based on file extension and content
        let binary_type = self.determine_binary_type(path)?;

        // Analyze the binary based on its type
        match binary_type {
            BinaryType::PE => self.pe_analyzer.analyze(path),
            BinaryType::ELF => self.elf_analyzer.analyze(path),
            BinaryType::MachO => self.macho_analyzer.analyze(path),
            BinaryType::PTX | BinaryType::Cubin => self.ptx_analyzer.analyze(path),
            BinaryType::Object => self.analyze_object_file(path),
            BinaryType::Unknown => Err(OmniError::UnsupportedFileType(path.to_path_buf())),
        }
    }

    /// Accelerated pattern matching for binary signatures
    pub fn accelerated_pattern_match(&self, patterns: &[u8], signatures: &[u8]) -> OmniResult<Vec<usize>> {
        // Convert byte patterns to f32 for acceleration
        let mut pattern_data: Vec<f32> = patterns.iter().map(|&b| b as f32).collect();
        let mut signature_data: Vec<f32> = signatures.iter().map(|&b| b as f32).collect();

        // Use acceleration for pattern matching if data size is significant
        if pattern_data.len() > 1000 {
            match ahaw::binary_analyzer::accelerate_pattern_matching(&mut pattern_data, &mut signature_data) {
                Ok(result) => {
                    println!("🚀 Accelerated pattern matching: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Perform actual pattern matching logic (simplified example)
        let mut matches = Vec::new();
        for (i, window) in signatures.windows(patterns.len()).enumerate() {
            if window == patterns {
                matches.push(i);
            }
        }

        Ok(matches)
    }

    /// Accelerated signature validation
    pub fn accelerated_signature_validation(&self, signatures: &[u8]) -> OmniResult<bool> {
        // Convert signatures to f32 for acceleration
        let mut signature_data: Vec<f32> = signatures.iter().map(|&b| b as f32).collect();

        // Use acceleration for signature validation if data size is significant
        if signature_data.len() > 500 {
            match ahaw::binary_analyzer::accelerate_signature_validation(&mut signature_data) {
                Ok(result) => {
                    println!("🚀 Accelerated signature validation: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);

                    // Use the computed norm for validation
                    let norm_threshold = 100.0; // Example threshold
                    return Ok(result.performance_metrics.throughput_gflops > norm_threshold);
                },
                Err(e) => {
                    println!("⚠️ Acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Fallback CPU validation logic
        Ok(!signatures.is_empty() && signatures.len() >= 4)
    }
    
    /// Determine the binary type based on file extension and content
    fn determine_binary_type(&self, path: &Path) -> OmniResult<BinaryType> {
        // First check by extension
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            match extension.to_lowercase().as_str() {
                "exe" | "dll" => return Ok(BinaryType::PE),
                "so" | "elf" => return Ok(BinaryType::ELF),
                "dylib" => return Ok(BinaryType::MachO),
                "ptx" => return Ok(BinaryType::PTX),
                "cubin" => return Ok(BinaryType::Cubin),
                "o" | "obj" => return Ok(BinaryType::Object),
                _ => {} // Continue with content-based detection
            }
        }
        
        // If extension doesn't give a definitive answer, check file content
        let file = std::fs::File::open(path)?;
        let mut buffer = [0; 16];
        // Use a buffered reader to avoid consuming the file handle
        let mut reader = std::io::BufReader::new(&file);

        if reader.read_exact(&mut buffer).is_err() {
            return Ok(BinaryType::Unknown);
        }
        
        // Check for magic numbers
        if buffer.starts_with(b"MZ") {
            Ok(BinaryType::PE)
        } else if buffer.starts_with(&[0x7F, b'E', b'L', b'F']) {
            Ok(BinaryType::ELF)
        } else if buffer.starts_with(&[0xCF, 0xFA, 0xED, 0xFE]) || buffer.starts_with(&[0xCE, 0xFA, 0xED, 0xFE]) {
            Ok(BinaryType::MachO)
        } else if buffer.starts_with(b".version") || buffer.starts_with(b"//") {
            // PTX files often start with .version or //
            Ok(BinaryType::PTX)
        } else {
            // Try to parse as an object file using the same file handle
            let map = unsafe { memmap2::MmapOptions::new().map(&file)? };
            match object::File::parse(&*map) {
                Ok(_) => Ok(BinaryType::Object),
                Err(_) => Ok(BinaryType::Unknown),
            }
        }
    }    
    /// Analyze an object file
    fn analyze_object_file(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        let file = std::fs::File::open(path)?;
        let map = unsafe { memmap2::MmapOptions::new().map(&file)? };
        
        let obj_file = object::File::parse(&*map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse object file: {e}")))?;
        
        let mut exports = Vec::new();
        let mut imports = Vec::new();
        
        // Extract symbols
        for symbol in obj_file.symbols() {
            let name = symbol.name()
                .map_err(|e| OmniError::BinaryFormat(format!("Failed to get symbol name: {e}")))?
                .to_string();
            
            let address = symbol.address();
            
            if symbol.is_definition() {
                exports.push(ExportedFunction {
                    name,
                    address,
                    signature: None, // Cannot determine signature from object file alone
                    calling_convention: None,
                    metadata: serde_json::Value::Null,
                });
            } else {
                imports.push(ImportedFunction {
                    name,
                    library: String::new(), // Cannot determine library from object file alone
                    signature: None,
                });
            }
        }
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::Object,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies: Vec::new(),
            additional_metadata: serde_json::Value::Null,
        })
    }
}

impl Default for BinaryAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}
