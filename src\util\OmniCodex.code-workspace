{"folders": [{"path": "../.."}], "settings": {"terminal.integrated.env.windows": {"VS2022INSTALLDIR": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools", "ONEAPI_ROOT": "C:\\Program Files (x86)\\Intel\\oneAPI", "PATH": "${env:PATH};C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin"}, "rust-analyzer.cargo.buildScripts.enable": true, "rust-analyzer.cargo.buildScripts.overrideCommand": ["cargo", "check", "--message-format=json"], "files.associations": {"vector": "cpp", "chrono": "cpp", "random": "cpp", "cstdlib": "cpp", "iostream": "cpp", "cmath": "cpp", "iomanip": "cpp", "thread": "cpp", "algorithm": "cpp", "atomic": "cpp", "cstring": "cpp", "cstdint": "cpp", "cfloat": "cpp", "future": "cpp", "memory": "cpp", "string": "cpp", "functional": "cpp", "cstdio": "cpp", "sstream": "cpp", "ostream": "cpp"}, "makefile.configureOnOpen": false}}