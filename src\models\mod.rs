﻿// src/models/mod.rs
#![warn(missing_docs)]
//! # Universal Machine Learning Model Framework
//!
//! This module provides unified interfaces for loading and running inference on various
//! machine learning model formats with hardware acceleration through AHAW integration.
//!
//! ## Core Architecture
//!
//! The [`XynKore`] trait provides the primary interface for all model types:
//! - [`load`](XynKore::load): Load a model from a file path with specified options
//! - [`infer`](XynKore::infer): Run accelerated inference on input tensors
//! - [`write`](XynKore::write): Export model to different formats
//! - [`metadata`](XynKore::metadata): Extract model metadata and information
//!
//! ## Supported Formats
//!
//! - **ONNX**: Open Neural Network Exchange format (.onnx)
//! - **PyTorch**: TorchScript models (.pt/.pth)
//! - **SafeTensors**: Safe tensor format (.safetensors)
//! - **TensorFlow**: SavedModel format (.pb + assets)
//! - **TensorFlow Lite**: Optimized mobile format (.tflite)
//! - **Keras**: HDF5 format (.h5)
//! - **Pickle/Joblib**: Python serialization (.pkl/.joblib)
//! - **Core ML**: Apple's format (.mlmodel)
//! - **OpenVINO**: Intel's IR format (.xml + .bin)
//! - **TensorRT**: NVIDIA's engine format (.engine)
//! - **GGUF**: Quantized models from llama.cpp ecosystem
//!
//! ## Usage
//!
//! ```rust,no_run
//! use omni_forge::models::{load_model, LoadOptions, Device};
//! use std::path::Path;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! let options = LoadOptions {
//!     device: Device::Auto,
//!     quantized: None,
//! };
//!
//! let model = load_model(Path::new("model.onnx"), options)?;
//! let outputs = model.infer(&input_tensors)?;
//! # Ok(())
//! # }
//! ```
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;

#[cfg(feature = "ml-inference")]
use ndarray::ArrayD;

use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};

/// Re-export the loader function for convenience
#[cfg(feature = "ml-inference")]
pub use loader::load_model;

// Module declarations for all supported ML formats
#[cfg(feature = "ml-inference")]
pub mod loader;

#[cfg(feature = "ml-inference")]
pub mod onnx;

#[cfg(feature = "ml-inference")]
pub mod pytorch;

#[cfg(feature = "ml-inference")]
pub mod safetensors;

#[cfg(feature = "ml-inference")]
pub mod tensorflow;

#[cfg(feature = "ml-inference")]
pub mod tensorflow_lite;

#[cfg(feature = "ml-inference")]
pub mod keras;

#[cfg(feature = "ml-inference")]
pub mod pickle;

#[cfg(feature = "ml-inference")]
pub mod coreml;

#[cfg(feature = "ml-inference")]
pub mod openvino;

#[cfg(feature = "ml-inference")]
pub mod tensorrt;

#[cfg(feature = "ml-inference")]
pub mod gguf;

/// **XynKore**: Universal Machine Learning Model Interface
///
/// Advanced trait providing unified access to all supported ML formats with
/// hardware acceleration, dynamic loading, and comprehensive model operations.
/// This is the primary interface for all ML model implementations.
#[cfg(feature = "ml-inference")]
pub trait XynKore: Send + Sync {
    /// Load a model from the specified path with comprehensive options
    ///
    /// # Arguments
    ///
    /// * `path` - Path to the model file or directory
    /// * `options` - Loading options including device and quantization settings
    ///
    /// # Returns
    ///
    /// Returns `Ok(Self)` if the model loads successfully, or an error if loading fails.
    fn load<P: AsRef<Path>>(path: P, options: LoadOptions) -> anyhow::Result<Self>
    where
        Self: Sized;

    /// Run accelerated inference with AHAW optimization
    ///
    /// # Arguments
    ///
    /// * `inputs` - Slice of input tensors as n-dimensional arrays
    ///
    /// # Returns
    ///
    /// Returns a vector of output tensors, or an error if inference fails.
    fn infer(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<Vec<ArrayD<f32>>>;

    /// Write/export model to specified format and path
    ///
    /// # Arguments
    ///
    /// * `path` - Output path for the exported model
    /// * `format` - Optional target format (if different from source)
    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {
        Err(anyhow::anyhow!("Write operation not supported for this model type"))
    }

    /// Read additional model data (for multi-file formats like TensorFlow SavedModel)
    ///
    /// # Arguments
    ///
    /// * `data` - Additional binary data to incorporate into the model
    fn read_additional(&mut self, data: &[u8]) -> anyhow::Result<()> {
        Err(anyhow::anyhow!("Additional data reading not supported for this model type"))
    }

    /// Extract comprehensive model metadata
    ///
    /// # Returns
    ///
    /// Returns model metadata including input/output shapes, data types, etc.
    fn metadata(&self) -> anyhow::Result<ModelMetadata>;

    /// Get model format identifier
    ///
    /// # Returns
    ///
    /// Returns a string identifier for the model format (e.g., "onnx", "pytorch", etc.)
    fn format(&self) -> &'static str;

    /// Get supported operations for this model type
    ///
    /// # Returns
    ///
    /// Returns a vector of supported operation names
    fn supported_operations(&self) -> Vec<String> {
        vec!["inference".to_string()]
    }

    /// Optimize model for specific hardware backend
    ///
    /// # Arguments
    ///
    /// * `device` - Target device for optimization
    fn optimize_for_device(&mut self, device: &Device) -> anyhow::Result<()> {
        Ok(()) // Default: no optimization needed
    }

    /// Get model memory footprint in bytes
    ///
    /// # Returns
    ///
    /// Returns the approximate memory usage of the loaded model
    fn memory_footprint(&self) -> usize {
        0 // Default: unknown
    }

    /// Check if model supports streaming inference
    ///
    /// # Returns
    ///
    /// Returns true if the model supports streaming/incremental inference
    fn supports_streaming(&self) -> bool {
        false // Default: no streaming support
    }

    /// Validate input tensors against model requirements
    ///
    /// # Arguments
    ///
    /// * `inputs` - Input tensors to validate
    ///
    /// # Returns
    ///
    /// Returns Ok(()) if inputs are valid, or an error describing the issue
    fn validate_inputs(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<()> {
        if inputs.is_empty() {
            return Err(anyhow::anyhow!("No input tensors provided"));
        }
        Ok(())
    }
}

/// **Legacy Umlaiie trait** - maintained for backward compatibility
///
/// Use XynKore for new implementations as it provides enhanced capabilities.
#[cfg(feature = "ml-inference")]
pub trait Umlaiie: Send + Sync {
    /// Load a model from the specified path with given options
    fn load<P: AsRef<Path>>(path: P, options: LoadOptions) -> anyhow::Result<Self>
    where
        Self: Sized;

    /// Run inference on the provided input tensors
    fn infer(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<Vec<ArrayD<f32>>>;

    /// Extract metadata about the loaded model
    fn metadata(&self) -> anyhow::Result<ModelMetadata> {
        Err(anyhow::anyhow!("Metadata not available for this model type"))
    }
}

/// Configuration options for loading models
#[cfg(feature = "ml-inference")]
#[derive(Debug, Clone)]
pub struct LoadOptions {
    /// Target device for model execution
    pub device: Device,
    /// Optional quantization configuration
    pub quantized: Option<QuantConfig>,
}

#[cfg(feature = "ml-inference")]
impl Default for LoadOptions {
    fn default() -> Self {
        Self {
            device: Device::Auto,
            quantized: None,
        }
    }
}

/// Target device for model execution
#[cfg(feature = "ml-inference")]
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Device {
    /// CPU execution
    Cpu,
    /// CUDA GPU execution with device index
    Cuda(usize),
    /// Vulkan GPU execution
    Vulkan,
    /// WebGPU execution
    WebGpu,
    /// GPU execution (auto-detect best GPU backend)
    Gpu,
    /// Automatic device selection based on availability and model requirements
    Auto,
}

/// Quantization configuration for model optimization
#[cfg(feature = "ml-inference")]
#[derive(Debug, Clone)]
pub struct QuantConfig {
    /// Number of bits for quantization (e.g., 8, 16)
    pub bits: u8,
    /// Quantization backend to use
    pub backend: QuantBackend,
}

/// Available quantization backends
#[cfg(feature = "ml-inference")]
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum QuantBackend {
    /// No quantization
    None,
    /// 16-bit floating point
    Fp16,
    /// 8-bit integer quantization
    Int8,
    /// Custom quantization backend
    Custom(String),
}

/// Comprehensive metadata extracted from a loaded model
#[cfg(feature = "ml-inference")]
#[derive(Debug, Clone)]
pub struct ModelMetadata {
    /// Model name
    pub name: String,
    /// Model version
    pub version: String,
    /// Input tensor shapes
    pub input_shapes: Vec<Vec<usize>>,
    /// Output tensor shapes
    pub output_shapes: Vec<Vec<usize>>,
    /// Data type of tensors
    pub dtype: String,
    /// Model format identifier
    pub format: String,
    /// Additional model-specific metadata
    pub extra: std::collections::HashMap<String, String>,
}

#[cfg(feature = "ml-inference")]
impl Default for ModelMetadata {
    fn default() -> Self {
        Self {
            name: "Unknown".to_string(),
            version: "0.0.0".to_string(),
            input_shapes: vec![],
            output_shapes: vec![],
            dtype: "f32".to_string(),
            format: "unknown".to_string(),
            extra: std::collections::HashMap::new(),
        }
    }
}

/// Error types for the ML inference engine
#[cfg(feature = "ml-inference")]
#[derive(Debug, thiserror::Error)]
pub enum UmlaiieError {
    /// Model loading failed
    #[error("Failed to load model: {0}")]
    LoadError(String),

    /// Inference execution failed
    #[error("Inference failed: {0}")]
    InferenceError(String),

    /// Invalid input tensor shape or format
    #[error("Invalid input: {0}")]
    InvalidInput(String),

    /// Device not available or unsupported
    #[error("Device error: {0}")]
    DeviceError(String),

    /// Quantization error
    #[error("Quantization error: {0}")]
    QuantizationError(String),

    /// Model format not supported
    #[error("Format error: {0}")]
    FormatError(String),
}

/// Result type for ML inference operations
#[cfg(feature = "ml-inference")]
pub type UmlaiieResult<T> = Result<T, UmlaiieError>;

/// Accelerated tensor operations with AHAW integration
#[cfg(feature = "ml-inference")]
pub fn accelerated_tensor_multiply(tensor_a: &mut [f32], tensor_b: &[f32]) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
    let tensor_b_copy = tensor_b.to_vec();

    // Use AHAW acceleration for large tensor operations
    if tensor_a.len() > 10000 {
        let operation = VectorOperation::MatrixMultiply;
        let hint = AccelerationHint::Auto;
        let characteristics = TaskCharacteristics {
            data_size: tensor_a.len(),
            compute_intensity: 0.9,
            parallelizability: 0.95,
            ..Default::default()
        };
        match ahaw::models::accelerate_tensor_operations(tensor_a, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 AHAW tensor acceleration: {} ms, backend: {}",
                        result.execution_time_ms, result.backend_path);
            },
            Err(e) => {
                println!("⚠️ AHAW acceleration failed, using CPU fallback: {}", e);
            }
        }
    }

    // Element-wise multiplication result
    let result: Vec<f32> = tensor_a.iter()
        .zip(tensor_b_copy.iter().cycle())
        .map(|(a, b)| a * b)
        .collect();

    Ok(result)
}

/// Accelerated model inference with dynamic backend selection via AHAW
#[cfg(feature = "ml-inference")]
pub fn accelerated_inference(model_data: &mut [f32], input_data: &[f32], device: Device) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
    let input_copy = input_data.to_vec();

    // Choose AHAW acceleration strategy based on device preference
    let hint = match device {
        Device::Gpu | Device::Cuda(_) => AccelerationHint::PreferGPU,
        Device::Cpu => AccelerationHint::PreferCPU,
        Device::Auto => AccelerationHint::Auto,
        Device::Vulkan | Device::WebGpu => AccelerationHint::PreferGPU,
    };

    // Create task characteristics optimized for ML inference
    let characteristics = TaskCharacteristics {
        data_size: model_data.len(),
        compute_intensity: 0.95,
        parallelizability: 0.98,
        memory_access_pattern: "random".to_string(),
        priority: "high".to_string(),
        expected_duration_ms: 50.0,
        ..Default::default()
    };

    // Use AHAW acceleration for significant workloads
    if model_data.len() > 5000 {
        match ahaw::models::accelerate_inference(model_data, &input_copy, hint, characteristics) {
            Ok(result) => {
                println!("🚀 AHAW ML inference: {} ms, backend: {}",
                        result.execution_time_ms, result.backend_path);
                println!("   Performance: {:.2} GFLOPS, efficiency: {:.1}%",
                        result.performance_metrics.throughput_gflops,
                        result.performance_metrics.vectorization_efficiency * 100.0);
            },
            Err(e) => {
                println!("⚠️ AHAW ML inference failed: {}", e);
            }
        }
    }

    // Simplified inference computation (matrix multiplication approximation)
    let output_size = std::cmp::min(model_data.len(), input_copy.len());
    let result: Vec<f32> = (0..output_size)
        .map(|i| model_data[i] * input_copy[i % input_copy.len()])
        .collect();

    Ok(result)
}

/// Dynamic model format detection from file extension
#[cfg(feature = "ml-inference")]
pub fn detect_format(path: &Path) -> Option<&'static str> {
    path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| match ext.to_lowercase().as_str() {
            "onnx" => "onnx",
            "pt" | "pth" => "pytorch",
            "safetensors" => "safetensors",
            "pb" => "tensorflow",
            "tflite" => "tensorflow_lite",
            "h5" | "hdf5" => "keras",
            "pkl" | "pickle" | "joblib" => "pickle",
            "mlmodel" => "coreml",
            "xml" => "openvino",
            "engine" | "trt" => "tensorrt",
            "gguf" => "gguf",
            _ => "unknown",
        })
}

/// Get list of all supported model formats
#[cfg(feature = "ml-inference")]
pub fn supported_formats() -> Vec<&'static str> {
    vec![
        "onnx",
        "pytorch",
        "safetensors",
        "gguf",
        "tensorflow",
        "tensorflow_lite",
        "keras",
        "pickle",
        "coreml",
        "openvino",
        "tensorrt"
    ]
}
