cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="objidlbase"
cargo:rustc-cfg=feature="wincodec"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="d3dcommon"
cargo:rustc-cfg=feature="reason"
cargo:rustc-cfg=feature="windef"
cargo:rustc-cfg=feature="d2d1_1"
cargo:rustc-cfg=feature="dxgiformat"
cargo:rustc-cfg=feature="wtypes"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="dcommon"
cargo:rustc-cfg=feature="wtypesbase"
cargo:rustc-cfg=feature="ocidl"
cargo:rustc-cfg=feature="cfgmgr32"
cargo:rustc-cfg=feature="d2d1"
cargo:rustc-cfg=feature="d2dbasetypes"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="winreg"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="wingdi"
cargo:rustc-cfg=feature="d2d1effectauthor"
cargo:rustc-cfg=feature="propidl"
cargo:rustc-cfg=feature="devpropdef"
cargo:rustc-cfg=feature="dxgi"
cargo:rustc-cfg=feature="oaidl"
cargo:rustc-cfg=feature="wincontypes"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="dwrite_2"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="dxgitype"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="cfg"
cargo:rustc-cfg=feature="documenttarget"
cargo:rustc-cfg=feature="winerror"
cargo:rustc-cfg=feature="d3d9types"
cargo:rustc-link-lib=dylib=advapi32
cargo:rustc-link-lib=dylib=cfgmgr32
cargo:rustc-link-lib=dylib=d2d1
cargo:rustc-link-lib=dylib=dwrite
cargo:rustc-link-lib=dylib=dxgi
cargo:rustc-link-lib=dylib=gdi32
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=msimg32
cargo:rustc-link-lib=dylib=ole32
cargo:rustc-link-lib=dylib=opengl32
cargo:rustc-link-lib=dylib=user32
cargo:rustc-link-lib=dylib=windowscodecs
cargo:rustc-link-lib=dylib=winspool
