{"rustc": 1842507548689473721, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 13378455292704437807, "deps": [[1457576002496728321, "clap_derive", false, 14620185135486539698], [7361794428713524931, "clap_builder", false, 6215320655765928821]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-0f7ada0e5a29a4db\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}