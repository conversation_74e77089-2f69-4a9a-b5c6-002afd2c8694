{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9241925498456048256, "build_script_build", false, 488460589488164813]], "local": [{"RerunIfChanged": {"output": "debug\\build\\blake3-3fc5085aef421564\\output", "paths": ["c\\.gitignore", "c\\blake3-config.cmake.in", "c\\blake3.c", "c\\blake3.h", "c\\blake3_avx2.c", "c\\blake3_avx2_x86-64_unix.S", "c\\blake3_avx2_x86-64_windows_gnu.S", "c\\blake3_avx2_x86-64_windows_msvc.asm", "c\\blake3_avx512.c", "c\\blake3_avx512_x86-64_unix.S", "c\\blake3_avx512_x86-64_windows_gnu.S", "c\\blake3_avx512_x86-64_windows_msvc.asm", "c\\blake3_dispatch.c", "c\\blake3_impl.h", "c\\blake3_neon.c", "c\\blake3_portable.c", "c\\blake3_sse2.c", "c\\blake3_sse2_x86-64_unix.S", "c\\blake3_sse2_x86-64_windows_gnu.S", "c\\blake3_sse2_x86-64_windows_msvc.asm", "c\\blake3_sse41.c", "c\\blake3_sse41_x86-64_unix.S", "c\\blake3_sse41_x86-64_windows_gnu.S", "c\\blake3_sse41_x86-64_windows_msvc.asm", "c\\blake3_tbb.cpp", "c\\cmake", "c\\CMakeLists.txt", "c\\CMakePresets.json", "c\\dependencies", "c\\example.c", "c\\example_tbb.c", "c\\libblake3.pc.in", "c\\main.c", "c\\Makefile.testing", "c\\README.md", "c\\test.py"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}