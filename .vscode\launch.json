{"version": "0.2.0", "configurations": [{"name": "Debug HAAL AVX2 (MinGW)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/haal/haal-avx2.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/haal", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:/msys64/ucrt64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "Build HAAL AVX2 (MinGW)"}, {"name": "Debug HAAL AVX2 (MSVC)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/haal/haal-avx2-msvc.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/haal", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build HAAL AVX2 (MSVC)"}, {"name": "Debug HAAL CUDA", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/haal/haal-cuda.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/haal", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:/msys64/ucrt64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Build HAAL CUDA"}]}