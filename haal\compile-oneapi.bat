@echo off
REM 🔷 Intel OneAPI Compilation Script for Ultra-Optimized AVX2 Benchmark (Windows)
REM ===============================================================================

echo 🔷 Intel OneAPI AVX2 Ultra-Optimization Compilation Script (Windows)
echo =================================================================

REM Check if Intel OneAPI is available
where icpx >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Intel OneAPI C++ Compiler (icpx) found
    set COMPILER=icpx
    goto :intel_compile
)

where icl >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Intel C++ Compiler (icl) found
    set COMPILER=icl
    goto :intel_compile
)

where cl >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  Intel OneAPI not found. Using MSVC.
    set COMPILER=cl
    goto :msvc_compile
)

where g++ >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  Intel OneAPI not found. Using GCC.
    set COMPILER=g++
    goto :gcc_compile
)

echo ❌ No suitable compiler found!
pause
exit /b 1

:intel_compile
echo.
echo 🔷 Using Intel OneAPI optimizations:
echo    - Inter-procedural optimization (Qipo)
echo    - Host-specific optimizations (QxHost)
echo    - Fast floating-point model
echo    - Advanced vectorization

if "%COMPILER%" == "icpx" (
    icpx haal-avx2-2.cpp -o haal-avx2-2-oneapi.exe -mavx2 -mfma -O3 -march=native -qopenmp -Qipo -QxHost -fp:fast
) else (
    icl haal-avx2-2.cpp /Fe:haal-avx2-2-oneapi.exe /arch:AVX2 /O3 /Qipo /QxHost /fp:fast /Qopenmp
)

if %errorlevel% == 0 (
    echo ✅ Intel OneAPI compilation successful!
    goto :execution_info
) else (
    echo ❌ Intel OneAPI compilation failed!
    pause
    exit /b 1
)

:msvc_compile
echo.
echo 🔧 Using MSVC with optimizations:
cl haal-avx2-2.cpp /Fe:haal-avx2-2-msvc.exe /arch:AVX2 /O2 /openmp

if %errorlevel% == 0 (
    echo ✅ MSVC compilation successful!
    goto :execution_info
) else (
    echo ❌ MSVC compilation failed!
    pause
    exit /b 1
)

:gcc_compile
echo.
echo 🔧 Using GCC with OpenMP optimizations:
g++ haal-avx2-2.cpp -o haal-avx2-2-gcc.exe -mavx2 -mfma -O3 -march=native -fopenmp -pthread

if %errorlevel% == 0 (
    echo ✅ GCC compilation successful!
    goto :execution_info
) else (
    echo ❌ GCC compilation failed!
    pause
    exit /b 1
)

:execution_info
echo.
echo 🎯 EXECUTION RECOMMENDATIONS:
echo ============================================
echo 🚀 Basic execution:
if exist haal-avx2-2-oneapi.exe (
    echo    haal-avx2-2-oneapi.exe
) else if exist haal-avx2-2-msvc.exe (
    echo    haal-avx2-2-msvc.exe
) else if exist haal-avx2-2-gcc.exe (
    echo    haal-avx2-2-gcc.exe
)

echo.
echo 📊 For Intel VTune profiling (if available):
echo    vtune -collect hotspots haal-avx2-2-oneapi.exe

echo.
echo 🔷 Intel OneAPI AVX2 Ultra-Optimization Complete!
pause
