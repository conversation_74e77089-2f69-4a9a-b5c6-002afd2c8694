// src/util/mod.rs
//! Utility functions and modules for the OmniForge compiler.
//!
//! This module provides common utilities shared across the OmniCodex framework,
//! including file handling, type conversions, logging utilities, and platform
//! detection capabilities.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::{Path, PathBuf};
use std::fs;
use std::io;
use std::env;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

use crate::error::{OmniError, OmniResult};
use crate::ahaw::{self, VectorOperation, AccelerationHint, TaskCharacteristics};

mod file_utils;
mod type_conversion;
mod platform;
mod logging;
mod hash;

pub use self::file_utils::{ensure_directory_exists, ensure_parent_directory_exists, read_file_to_string, write_string_to_file};
pub use self::type_conversion::{convert_to_c_type, convert_to_rust_type, convert_to_cpp_type, convert_to_python_type, convert_to_ts_type};
pub use self::platform::{detect_platform, Platform, Architecture, OperatingSystem};
pub use self::logging::{log_timing, log_with_timing, benchmark_fn};
pub use self::hash::{compute_file_hash, compute_string_hash, HashAlgorithm};

/// Cache implementation with a generic key and value
#[derive(Debug, Clone)]
pub struct Cache<K, V>
where
    K: std::hash::Hash + Eq + Clone,
    V: Clone,
{
    inner: Arc<Mutex<HashMap<K, CacheEntry<V>>>>,
    max_size: usize,
    ttl: Option<Duration>,
}

/// Cache entry with value and timestamp
#[derive(Debug, Clone)]
struct CacheEntry<V> {
    value: V,
    timestamp: Instant,
}

impl<K, V> Cache<K, V>
where
    K: std::hash::Hash + Eq + Clone,
    V: Clone,
{
    /// Create a new cache with the given maximum size
    pub fn new(max_size: usize) -> Self {
        Self {
            inner: Arc::new(Mutex::new(HashMap::new())),
            max_size,
            ttl: None,
        }
    }
    
    /// Create a new cache with the given maximum size and time-to-live
    pub fn with_ttl(max_size: usize, ttl: Duration) -> Self {
        Self {
            inner: Arc::new(Mutex::new(HashMap::new())),
            max_size,
            ttl: Some(ttl),
        }
    }
    
    /// Get a value from the cache
    pub fn get(&self, key: &K) -> Option<V> {
        let mut cache = self.inner.lock().unwrap();
        
        if let Some(entry) = cache.get(key) {
            // Check if entry has expired
            if let Some(ttl) = self.ttl {
                if entry.timestamp.elapsed() > ttl {
                    // Entry has expired, remove it
                    cache.remove(key);
                    return None;
                }
            }
            
            return Some(entry.value.clone());
        }
        
        None
    }
    
    /// Insert a value into the cache
    pub fn insert(&self, key: K, value: V) {
        let mut cache = self.inner.lock().unwrap();
        
        // Check if cache is full
        if cache.len() >= self.max_size && !cache.contains_key(&key) {
            // Remove oldest entry
            if let Some(ttl) = self.ttl {
                // Remove expired entries first
                cache.retain(|_, entry| entry.timestamp.elapsed() <= ttl);
            }
            
            // If still full, remove oldest entry
            if cache.len() >= self.max_size {
                let oldest_key = cache
                    .iter()
                    .min_by_key(|(_, entry)| entry.timestamp)
                    .map(|(k, _)| k.clone());
                
                if let Some(oldest_key) = oldest_key {
                    cache.remove(&oldest_key);
                }
            }
        }
        
        // Insert new entry
        cache.insert(key, CacheEntry {
            value,
            timestamp: Instant::now(),
        });
    }
    
    /// Remove a value from the cache
    pub fn remove(&self, key: &K) -> Option<V> {
        let mut cache = match self.inner.lock() {
            Ok(guard) => guard,
            Err(_) => return None,
        };
        cache.remove(key).map(|entry| entry.value)
    }
    
    /// Clear the cache
    pub fn clear(&self) {
        if let Ok(mut cache) = self.inner.lock() {
            cache.clear();
        }
    }
    
    /// Get the number of entries in the cache
    pub fn len(&self) -> usize {
        match self.inner.lock() {
            Ok(cache) => cache.len(),
            Err(_) => 0,
        }
    }
    
    /// Check if the cache is empty
    pub fn is_empty(&self) -> bool {
        match self.inner.lock() {
            Ok(cache) => cache.is_empty(),
            Err(_) => true,
        }
    }
    
    /// Remove expired entries from the cache
    pub fn remove_expired(&self) -> usize {
        if let Some(ttl) = self.ttl {
            if let Ok(mut cache) = self.inner.lock() {
                let initial_size = cache.len();
                cache.retain(|_, entry| entry.timestamp.elapsed() <= ttl);
                return initial_size - cache.len();
            }
        }
        0
    }
}

/// Execute a shell command and return the output
pub fn execute_command(command: &str, args: &[&str]) -> io::Result<String> {
    let output = std::process::Command::new(command)
        .args(args)
        .output()?;
    
    if output.status.success() {
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    } else {
        Err(io::Error::other(
            format!(
                "Command '{}' failed with exit code {}: {}",
                command,
                output.status.code().unwrap_or(-1),
                String::from_utf8_lossy(&output.stderr)
            ),
        ))
    }
}

/// Find files matching a pattern
pub fn find_files<P: AsRef<Path>>(dir: P, pattern: &str) -> io::Result<Vec<PathBuf>> {
    let mut files = Vec::new();
    let pattern_regex = regex::Regex::new(pattern)
        .map_err(|e| io::Error::new(io::ErrorKind::InvalidInput, e))?;
    
    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(filename) = path.file_name().and_then(|f| f.to_str()) {
                if pattern_regex.is_match(filename) {
                    files.push(path);
                }
            }
        } else if path.is_dir() {
            // Recursive search
            let mut sub_files = find_files(&path, pattern)?;
            files.append(&mut sub_files);
        }
    }
    
    Ok(files)
}

/// Find a command in the PATH
pub fn find_command(command: &str) -> Option<PathBuf> {
    let path_var = env::var("PATH").unwrap_or_default();
    let paths: Vec<&str> = if cfg!(windows) {
        path_var.split(';').collect()
    } else {
        path_var.split(':').collect()
    };
    
    // Add common extensions for Windows
    let extensions = if cfg!(windows) {
        vec![".exe", ".cmd", ".bat", ""]
    } else {
        vec![""]
    };
    
    for path in paths {
        for ext in &extensions {
            let cmd_path = PathBuf::from(path).join(format!("{command}{ext}"));
            if cmd_path.exists() {
                return Some(cmd_path);
            }
        }
    }
    
    None
}

/// Check if a command exists in the PATH
pub fn command_exists(command: &str) -> bool {
    find_command(command).is_some()
}

/// Get the current executable path
pub fn get_executable_path() -> io::Result<PathBuf> {
    env::current_exe()
}

/// Get a temporary file path
pub fn get_temp_file_path(prefix: &str, extension: &str) -> PathBuf {
    let temp_dir = env::temp_dir();
    let mut file_name = prefix.to_string();
    
    // Add timestamp
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    file_name.push_str(&format!("_{timestamp}"));
    
    // Add random suffix
    use rand::Rng;
    let mut rng = rand::rng();
    let random_suffix: u32 = rng.random();
    file_name.push_str(&format!("_{random_suffix:x}"));
    
    // Add extension
    if !extension.is_empty() {
        if !extension.starts_with('.') {
            file_name.push('.');
        }
        file_name.push_str(extension);
    }
    
    temp_dir.join(file_name)
}

/// Check if a file is newer than another file
pub fn is_file_newer<P: AsRef<Path>, Q: AsRef<Path>>(file: P, reference: Q) -> io::Result<bool> {
    let file_metadata = fs::metadata(file)?;
    let reference_metadata = fs::metadata(reference)?;
    
    let file_modified = file_metadata.modified()?;
    let reference_modified = reference_metadata.modified()?;
    
    Ok(file_modified > reference_modified)
}

/// Split a string into lines, handling different line endings
pub fn split_lines(text: &str) -> Vec<String> {
    text.lines().map(|line| line.to_string()).collect()
}

/// Join lines with the platform-specific line ending
pub fn join_lines(lines: &[String]) -> String {
    let line_ending = if cfg!(windows) { "\r\n" } else { "\n" };
    lines.join(line_ending)
}

/// Parse a version string
pub fn parse_version(version: &str) -> OmniResult<(u32, u32, u32)> {
    let parts: Vec<&str> = version.split('.').collect();
    
    if parts.len() < 2 || parts.len() > 3 {
        return Err(OmniError::General(format!(
            "Invalid version format: {version}"
        )));
    }
    
    let major = parts[0].parse::<u32>()
        .map_err(|_| OmniError::General(format!("Invalid major version: {}", parts[0])))?;
    
    let minor = parts[1].parse::<u32>()
        .map_err(|_| OmniError::General(format!("Invalid minor version: {}", parts[1])))?;
    
    let patch = if parts.len() == 3 {
        parts[2].parse::<u32>()
            .map_err(|_| OmniError::General(format!("Invalid patch version: {}", parts[2])))?
    } else {
        0
    };
    
    Ok((major, minor, patch))
}

/// Compare two version strings
pub fn compare_versions(v1: &str, v2: &str) -> OmniResult<std::cmp::Ordering> {
    let v1 = parse_version(v1)?;
    let v2 = parse_version(v2)?;
    
    Ok(v1.cmp(&v2))
}

/// Format a size in bytes to a human-readable string
pub fn format_size(size: u64) -> String {
    const KB: u64 = 1024;
    const MB: u64 = KB * 1024;
    const GB: u64 = MB * 1024;
    const TB: u64 = GB * 1024;
    
    if size < KB {
        format!("{size} B")
    } else if size < MB {
        format!("{:.2} KB", size as f64 / KB as f64)
    } else if size < GB {
        format!("{:.2} MB", size as f64 / MB as f64)
    } else if size < TB {
        format!("{:.2} GB", size as f64 / GB as f64)
    } else {
        format!("{:.2} TB", size as f64 / TB as f64)
    }
}

/// Format a duration in milliseconds to a human-readable string
pub fn format_duration(duration_ms: u64) -> String {
    const SECOND: u64 = 1000;
    const MINUTE: u64 = SECOND * 60;
    const HOUR: u64 = MINUTE * 60;

    if duration_ms < SECOND {
        format!("{duration_ms} ms")
    } else if duration_ms < MINUTE {
        format!("{:.2} s", duration_ms as f64 / SECOND as f64)
    } else if duration_ms < HOUR {
        format!("{:.2} min", duration_ms as f64 / MINUTE as f64)
    } else {
        format!("{:.2} h", duration_ms as f64 / HOUR as f64)
    }
}

/// Accelerated mathematical operations with dynamic backend selection
pub fn accelerated_math_operation(data: &mut [f32], operation: VectorOperation) -> OmniResult<Vec<f32>> {
    // Use acceleration for mathematical operations on large datasets
    if data.len() > 1000 {
        let hint = AccelerationHint::Auto;
        let characteristics = TaskCharacteristics {
            data_size: data.len(),
            compute_intensity: 0.7,
            parallelizability: 0.85,
            ..Default::default()
        };
        match ahaw::util::accelerate_mathematical_operations(data, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated math operation ({:?}): {} ms, backend: {}",
                        operation, result.execution_time_ms, result.backend_path);
                println!("   Performance: {:.2} GFLOPS, efficiency: {:.1}%",
                        result.performance_metrics.throughput_gflops,
                        result.performance_metrics.vectorization_efficiency * 100.0);
            },
            Err(e) => {
                println!("⚠️ Math operation acceleration failed: {}", e);
            }
        }
    }

    Ok(data.to_vec())
}

/// Accelerated data processing with automatic workload characterization
pub fn accelerated_data_processing(data: &mut [f32], hint: AccelerationHint) -> OmniResult<Vec<f32>> {
    // Characterize the workload automatically
    let characteristics = TaskCharacteristics {
        data_size: data.len(),
        compute_intensity: match data.len() {
            0..=1000 => 0.3,
            1001..=10000 => 0.6,
            _ => 0.9,
        },
        parallelizability: 0.85,
        memory_access_pattern: "sequential".to_string(),
        cache_locality_index: 0.8,
        expected_duration_ms: (data.len() as f64 * 0.001).max(1.0),
        priority: "normal".to_string(),
    };

    // Use acceleration for data processing
    if data.len() > 500 {
        let operation = VectorOperation::Add; // Default operation for data processing
        match ahaw::util::accelerate_data_processing(data, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated data processing: {} ms, backend: {}",
                        result.execution_time_ms, result.backend_path);

                // Log performance characteristics
                if result.performance_metrics.thermal_throttling {
                    println!("⚠️ Thermal throttling detected during processing");
                }
            },
            Err(e) => {
                println!("⚠️ Data processing acceleration failed: {}", e);
            }
        }
    }

    Ok(data.to_vec())
}

/// Accelerated vector operations with performance monitoring
pub fn accelerated_vector_ops(a: &mut [f32], b: &[f32], operation: VectorOperation) -> OmniResult<Vec<f32>> {
    let b_copy = b.to_vec();

    // Create task characteristics based on operation type
    let characteristics = TaskCharacteristics {
        data_size: a.len(),
        compute_intensity: match operation {
            VectorOperation::Add | VectorOperation::Multiply => 0.4,
            VectorOperation::DotProduct | VectorOperation::Norm => 0.6,
            VectorOperation::MatrixMultiply | VectorOperation::Convolution => 0.9,
            VectorOperation::FourierTransform | VectorOperation::QuantumEvolution => 0.95,
            _ => 0.7,
        },
        parallelizability: match operation {
            VectorOperation::Add | VectorOperation::Multiply => 0.95,
            VectorOperation::MatrixMultiply => 0.90,
            VectorOperation::FourierTransform => 0.85,
            _ => 0.80,
        },
        memory_access_pattern: match operation {
            VectorOperation::Add | VectorOperation::Multiply => "sequential".to_string(),
            VectorOperation::MatrixMultiply => "strided".to_string(),
            _ => "random".to_string(),
        },
        priority: "normal".to_string(),
        ..Default::default()
    };

    // Use acceleration for vector operations
    if a.len() > 100 {
        let hint = AccelerationHint::Auto;
        match ahaw::util::accelerate_mathematical_operations(a, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated vector operation ({:?}): {} ms, backend: {}",
                        operation, result.execution_time_ms, result.backend_path);
            },
            Err(e) => {
                println!("⚠️ Vector operation acceleration failed: {}", e);
            }
        }
    }

    // Perform the actual operation (simplified)
    let result = match operation {
        VectorOperation::Add => a.iter().zip(b_copy.iter().cycle()).map(|(x, y)| x + y).collect(),
        VectorOperation::Multiply => a.iter().zip(b_copy.iter().cycle()).map(|(x, y)| x * y).collect(),
        _ => a.to_vec(),
    };

    Ok(result)
}
