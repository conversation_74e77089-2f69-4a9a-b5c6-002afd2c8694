{"$message_type":"diagnostic","message":"unused variable: `network_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":37593,"byte_end":37608,"line_start":1035,"line_end":1035,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"        let network_manager = self.network_manager.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":37593,"byte_end":37608,"line_start":1035,"line_end":1035,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"        let network_manager = self.network_manager.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":"_network_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `network_manager`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:1035:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1035\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let network_manager = self.network_manager.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_network_manager`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a struct","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":2037,"byte_end":2076,"line_start":37,"line_end":37,"column_start":25,"column_end":64,"is_primary":true,"text":[{"text":"     # [allow (unused)] pub struct r#CyberpunkAnimations < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkAnimations >>) ;","highlight_start":25,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the lint level is defined here","code":null,"level":"note","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":27,"byte_end":39,"line_start":2,"line_end":2,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"#![warn(missing_docs)]","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a struct\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:37:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m     # [allow (unused)] pub struct r#CyberpunkAnimations < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkAnimations >>) ;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the lint level is defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#![warn(missing_docs)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":2228,"byte_end":2259,"line_start":39,"line_end":39,"column_start":32,"column_end":63,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_fast (& self) -> i64 {","highlight_start":32,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:39:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_fast (& self) -> i64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":2560,"byte_end":2598,"line_start":42,"line_end":42,"column_start":32,"column_end":70,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_fast (& self , value : i64) {","highlight_start":32,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:42:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_fast (& self , value : i64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":2909,"byte_end":2942,"line_start":45,"line_end":45,"column_start":32,"column_end":65,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_medium (& self) -> i64 {","highlight_start":32,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:45:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_medium (& self) -> i64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":3245,"byte_end":3285,"line_start":48,"line_end":48,"column_start":32,"column_end":72,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_medium (& self , value : i64) {","highlight_start":32,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:48:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_medium (& self , value : i64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":3598,"byte_end":3629,"line_start":51,"line_end":51,"column_start":32,"column_end":63,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_slow (& self) -> i64 {","highlight_start":32,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:51:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_slow (& self) -> i64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":3930,"byte_end":3968,"line_start":54,"line_end":54,"column_start":32,"column_end":70,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_slow (& self , value : i64) {","highlight_start":32,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:54:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_slow (& self , value : i64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4279,"byte_end":4316,"line_start":57,"line_end":57,"column_start":32,"column_end":69,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_ultra_slow (& self) -> i64 {","highlight_start":32,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_ultra_slow (& self) -> i64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4623,"byte_end":4667,"line_start":60,"line_end":60,"column_start":32,"column_end":76,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_ultra_slow (& self , value : i64) {","highlight_start":32,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:60:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_ultra_slow (& self , value : i64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a struct","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":14554,"byte_end":14590,"line_start":227,"line_end":227,"column_start":25,"column_end":61,"is_primary":true,"text":[{"text":"     # [allow (unused)] pub struct r#CyberpunkPalette < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkPalette >>) ;","highlight_start":25,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a struct\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:227:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m     # [allow (unused)] pub struct r#CyberpunkPalette < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkPalette >>) ;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":14736,"byte_end":14783,"line_start":229,"line_end":229,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_carbon_steel (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:229:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_carbon_steel (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":15086,"byte_end":15140,"line_start":232,"line_end":232,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_carbon_steel (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:232:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_carbon_steel (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":15453,"byte_end":15499,"line_start":235,"line_end":235,"column_start":32,"column_end":78,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_chrome_dark (& self) -> sp :: Color {","highlight_start":32,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:235:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m235\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_chrome_dark (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":15801,"byte_end":15854,"line_start":238,"line_end":238,"column_start":32,"column_end":85,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_chrome_dark (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:238:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_chrome_dark (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":16166,"byte_end":16217,"line_start":241,"line_end":241,"column_start":32,"column_end":83,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_chrome_highlight (& self) -> sp :: Color {","highlight_start":32,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:241:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_chrome_highlight (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":16524,"byte_end":16582,"line_start":244,"line_end":244,"column_start":32,"column_end":90,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_chrome_highlight (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:244:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m244\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_chrome_highlight (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":16899,"byte_end":16944,"line_start":247,"line_end":247,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_chrome_mid (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:247:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_chrome_mid (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":17245,"byte_end":17297,"line_start":250,"line_end":250,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_chrome_mid (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:250:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m250\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_chrome_mid (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":17608,"byte_end":17656,"line_start":253,"line_end":253,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_chrome_shadow (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:253:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_chrome_shadow (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":17960,"byte_end":18015,"line_start":256,"line_end":256,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_chrome_shadow (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:256:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_chrome_shadow (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":18329,"byte_end":18374,"line_start":259,"line_end":259,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_danger_red (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:259:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_danger_red (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":18675,"byte_end":18727,"line_start":262,"line_end":262,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_danger_red (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:262:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_danger_red (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":19038,"byte_end":19086,"line_start":265,"line_end":265,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_deep_charcoal (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:265:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_deep_charcoal (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":19390,"byte_end":19445,"line_start":268,"line_end":268,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_deep_charcoal (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:268:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_deep_charcoal (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":19759,"byte_end":19807,"line_start":271,"line_end":271,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_electric_blue (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:271:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_electric_blue (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":20111,"byte_end":20166,"line_start":274,"line_end":274,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_electric_blue (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:274:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_electric_blue (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":20480,"byte_end":20527,"line_start":277,"line_end":277,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_glass_accent (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:277:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_glass_accent (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":20830,"byte_end":20884,"line_start":280,"line_end":280,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_glass_accent (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:280:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_glass_accent (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":21197,"byte_end":21242,"line_start":283,"line_end":283,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_glass_dark (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:283:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_glass_dark (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":21543,"byte_end":21595,"line_start":286,"line_end":286,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_glass_dark (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:286:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_glass_dark (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":21906,"byte_end":21952,"line_start":289,"line_end":289,"column_start":32,"column_end":78,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_glass_light (& self) -> sp :: Color {","highlight_start":32,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:289:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_glass_light (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":22254,"byte_end":22307,"line_start":292,"line_end":292,"column_start":32,"column_end":85,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_glass_light (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:292:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_glass_light (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":22619,"byte_end":22669,"line_start":295,"line_end":295,"column_start":32,"column_end":82,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_graphite_shadow (& self) -> sp :: Color {","highlight_start":32,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:295:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_graphite_shadow (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":22975,"byte_end":23032,"line_start":298,"line_end":298,"column_start":32,"column_end":89,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_graphite_shadow (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:298:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_graphite_shadow (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":23348,"byte_end":23395,"line_start":301,"line_end":301,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_matrix_green (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:301:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_matrix_green (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":23698,"byte_end":23752,"line_start":304,"line_end":304,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_matrix_green (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:304:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_matrix_green (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":24065,"byte_end":24109,"line_start":307,"line_end":307,"column_start":32,"column_end":76,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_neon_cyan (& self) -> sp :: Color {","highlight_start":32,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:307:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_neon_cyan (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":24409,"byte_end":24460,"line_start":310,"line_end":310,"column_start":32,"column_end":83,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_neon_cyan (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:310:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_neon_cyan (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":24770,"byte_end":24818,"line_start":313,"line_end":313,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_plasma_purple (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:313:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m313\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_plasma_purple (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":25122,"byte_end":25177,"line_start":316,"line_end":316,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_plasma_purple (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:316:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m316\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_plasma_purple (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":25491,"byte_end":25536,"line_start":319,"line_end":319,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_slate_gray (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:319:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_slate_gray (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":25837,"byte_end":25889,"line_start":322,"line_end":322,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_slate_gray (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:322:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_slate_gray (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":26200,"byte_end":26248,"line_start":325,"line_end":325,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_success_green (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:325:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_success_green (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":26552,"byte_end":26607,"line_start":328,"line_end":328,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_success_green (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:328:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_success_green (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":26921,"byte_end":26967,"line_start":331,"line_end":331,"column_start":32,"column_end":78,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_accent (& self) -> sp :: Color {","highlight_start":32,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:331:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_accent (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":27269,"byte_end":27322,"line_start":334,"line_end":334,"column_start":32,"column_end":85,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_accent (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:334:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_accent (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":27634,"byte_end":27679,"line_start":337,"line_end":337,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_error (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:337:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_error (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":27980,"byte_end":28032,"line_start":340,"line_end":340,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_error (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:340:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_error (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":28343,"byte_end":28390,"line_start":343,"line_end":343,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_primary (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:343:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_primary (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":28693,"byte_end":28747,"line_start":346,"line_end":346,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_primary (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:346:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m346\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_primary (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":29060,"byte_end":29109,"line_start":349,"line_end":349,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_secondary (& self) -> sp :: Color {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:349:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m349\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_secondary (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":29414,"byte_end":29470,"line_start":352,"line_end":352,"column_start":32,"column_end":88,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_secondary (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:352:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m352\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_secondary (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":29785,"byte_end":29832,"line_start":355,"line_end":355,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_success (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:355:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m355\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_success (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":30135,"byte_end":30189,"line_start":358,"line_end":358,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_success (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:358:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_success (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":30502,"byte_end":30550,"line_start":361,"line_end":361,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_tertiary (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:361:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_tertiary (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":30854,"byte_end":30909,"line_start":364,"line_end":364,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_tertiary (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:364:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_tertiary (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":31223,"byte_end":31270,"line_start":367,"line_end":367,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_warning (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:367:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m367\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_warning (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":31573,"byte_end":31627,"line_start":370,"line_end":370,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_warning (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:370:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_warning (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":31940,"byte_end":31985,"line_start":373,"line_end":373,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_void_black (& self) -> sp :: Color {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:373:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_void_black (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":32286,"byte_end":32338,"line_start":376,"line_end":376,"column_start":32,"column_end":84,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_void_black (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:376:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m376\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_void_black (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":32649,"byte_end":32698,"line_start":379,"line_end":379,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_warning_orange (& self) -> sp :: Color {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:379:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m379\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_warning_orange (& self) -> sp :: Color {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":33003,"byte_end":33059,"line_start":382,"line_end":382,"column_start":32,"column_end":88,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_warning_orange (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:382:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_warning_orange (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a struct","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":37659,"byte_end":37698,"line_start":456,"line_end":456,"column_start":25,"column_end":64,"is_primary":true,"text":[{"text":"     # [allow (unused)] pub struct r#CyberpunkTypography < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkTypography >>) ;","highlight_start":25,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a struct\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:456:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m456\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m     # [allow (unused)] pub struct r#CyberpunkTypography < 'a > (& 'a :: core :: pin :: Pin < sp :: Rc < InnerCyberpunkTypography >>) ;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":37850,"byte_end":37901,"line_start":458,"line_end":458,"column_start":32,"column_end":83,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_mono_font (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:458:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m458\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_mono_font (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":38207,"byte_end":38265,"line_start":461,"line_end":461,"column_start":32,"column_end":90,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_mono_font (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:461:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_mono_font (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":38581,"byte_end":38635,"line_start":464,"line_end":464,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_primary_font (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:464:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_primary_font (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":38944,"byte_end":39005,"line_start":467,"line_end":467,"column_start":32,"column_end":93,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_primary_font (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:467:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_primary_font (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":39324,"byte_end":39380,"line_start":470,"line_end":470,"column_start":32,"column_end":88,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_secondary_font (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:470:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m470\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_secondary_font (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":39691,"byte_end":39754,"line_start":473,"line_end":473,"column_start":32,"column_end":95,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_secondary_font (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:473:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m473\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_secondary_font (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":40075,"byte_end":40118,"line_start":476,"line_end":476,"column_start":32,"column_end":75,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_2xl (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:476:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m476\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_2xl (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":40432,"byte_end":40482,"line_start":479,"line_end":479,"column_start":32,"column_end":82,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_2xl (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:479:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m479\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_2xl (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":40841,"byte_end":40884,"line_start":482,"line_end":482,"column_start":32,"column_end":75,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_3xl (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:482:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m482\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_3xl (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":41198,"byte_end":41248,"line_start":485,"line_end":485,"column_start":32,"column_end":82,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_3xl (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:485:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m485\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_3xl (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":41607,"byte_end":41650,"line_start":488,"line_end":488,"column_start":32,"column_end":75,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_4xl (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:488:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m488\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_4xl (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":41964,"byte_end":42014,"line_start":491,"line_end":491,"column_start":32,"column_end":82,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_4xl (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:491:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m491\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_4xl (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":42373,"byte_end":42417,"line_start":494,"line_end":494,"column_start":32,"column_end":76,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_base (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:494:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m494\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_base (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":42732,"byte_end":42783,"line_start":497,"line_end":497,"column_start":32,"column_end":83,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_base (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:497:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m497\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_base (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":43143,"byte_end":43185,"line_start":500,"line_end":500,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_lg (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:500:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m500\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_lg (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":43498,"byte_end":43547,"line_start":503,"line_end":503,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_lg (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:503:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m503\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_lg (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":43905,"byte_end":43947,"line_start":506,"line_end":506,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_sm (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:506:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m506\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_sm (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":44260,"byte_end":44309,"line_start":509,"line_end":509,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_sm (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:509:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m509\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_sm (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":44667,"byte_end":44709,"line_start":512,"line_end":512,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_xl (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:512:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m512\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_xl (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":45022,"byte_end":45071,"line_start":515,"line_end":515,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_xl (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:515:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m515\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_xl (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":45429,"byte_end":45471,"line_start":518,"line_end":518,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_text_xs (& self) -> sp :: Coord {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:518:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_text_xs (& self) -> sp :: Coord {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":45784,"byte_end":45833,"line_start":521,"line_end":521,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_text_xs (& self , value : sp :: Coord) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:521:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m521\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_text_xs (& self , value : sp :: Coord) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a struct","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4151925,"byte_end":4151948,"line_start":57506,"line_end":57506,"column_start":6,"column_end":29,"is_primary":true,"text":[{"text":"     pub struct r#MainWindow (sp :: VRc < sp :: ItemTreeVTable , InnerMainWindow >) ;","highlight_start":6,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a struct\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57506:6\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57506\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m     pub struct r#MainWindow (sp :: VRc < sp :: ItemTreeVTable , InnerMainWindow >) ;\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for an associated function","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4152040,"byte_end":4152115,"line_start":57508,"line_end":57508,"column_start":10,"column_end":85,"is_primary":true,"text":[{"text":"         pub fn new () -> core :: result :: Result < Self , slint :: PlatformError > {","highlight_start":10,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for an associated function\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57508:10\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57508\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         pub fn new () -> core :: result :: Result < Self , slint :: PlatformError > {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4152435,"byte_end":4152482,"line_start":57513,"line_end":57513,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_accent_color (& self) -> sp :: Color {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57513:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57513\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_accent_color (& self) -> sp :: Color {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4152750,"byte_end":4152804,"line_start":57518,"line_end":57518,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_accent_color (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57518:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_accent_color (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4153082,"byte_end":4153154,"line_start":57523,"line_end":57523,"column_start":32,"column_end":104,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_active_files (& self) -> sp :: ModelRc < sp :: SharedString > {","highlight_start":32,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57523:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57523\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_active_files (& self) -> sp :: ModelRc < sp :: SharedString > {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4153422,"byte_end":4153501,"line_start":57528,"line_end":57528,"column_start":32,"column_end":111,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_active_files (& self , value : sp :: ModelRc < sp :: SharedString >) {","highlight_start":32,"highlight_end":111}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57528:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57528\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_active_files (& self , value : sp :: ModelRc < sp :: SharedString >) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4153779,"byte_end":4153847,"line_start":57533,"line_end":57533,"column_start":32,"column_end":100,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_add_file (& self , arg_0 : sp :: SharedString ,) -> () {","highlight_start":32,"highlight_end":100}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57533:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57533\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn invoke_add_file (& self , arg_0 : sp :: SharedString ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4154096,"byte_end":4154181,"line_start":57538,"line_end":57538,"column_start":32,"column_end":117,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_add_file (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {","highlight_start":32,"highlight_end":117}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57538:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57538\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn on_add_file (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4154482,"byte_end":4154558,"line_start":57543,"line_end":57543,"column_start":32,"column_end":108,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_analysis_results (& self) -> sp :: ModelRc < sp :: SharedString > {","highlight_start":32,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57543:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57543\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_analysis_results (& self) -> sp :: ModelRc < sp :: SharedString > {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4154830,"byte_end":4154913,"line_start":57548,"line_end":57548,"column_start":32,"column_end":115,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_analysis_results (& self , value : sp :: ModelRc < sp :: SharedString >) {","highlight_start":32,"highlight_end":115}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57548:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_analysis_results (& self , value : sp :: ModelRc < sp :: SharedString >) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4155195,"byte_end":4155237,"line_start":57553,"line_end":57553,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_animation_speed (& self) -> f32 {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57553:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57553\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_animation_speed (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4155508,"byte_end":4155557,"line_start":57558,"line_end":57558,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_animation_speed (& self , value : f32) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57558:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57558\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_animation_speed (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4155838,"byte_end":4155879,"line_start":57563,"line_end":57563,"column_start":32,"column_end":73,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_cache_hit_rate (& self) -> f32 {","highlight_start":32,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57563:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57563\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_cache_hit_rate (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4156149,"byte_end":4156197,"line_start":57568,"line_end":57568,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_cache_hit_rate (& self , value : f32) {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57568:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57568\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_cache_hit_rate (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4156477,"byte_end":4156523,"line_start":57573,"line_end":57573,"column_start":32,"column_end":78,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_chrome_reflectivity (& self) -> f32 {","highlight_start":32,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57573:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57573\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_chrome_reflectivity (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4156798,"byte_end":4156851,"line_start":57578,"line_end":57578,"column_start":32,"column_end":85,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_chrome_reflectivity (& self , value : f32) {","highlight_start":32,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57578:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57578\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_chrome_reflectivity (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4157136,"byte_end":4157183,"line_start":57583,"line_end":57583,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_compilation_progress (& self) -> f32 {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57583:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57583\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_compilation_progress (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4157459,"byte_end":4157513,"line_start":57588,"line_end":57588,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_compilation_progress (& self , value : f32) {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57588:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57588\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_compilation_progress (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4157799,"byte_end":4157843,"line_start":57593,"line_end":57593,"column_start":32,"column_end":76,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_compilation_speed (& self) -> f32 {","highlight_start":32,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57593:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57593\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_compilation_speed (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4158116,"byte_end":4158167,"line_start":57598,"line_end":57598,"column_start":32,"column_end":83,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_compilation_speed (& self , value : f32) {","highlight_start":32,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57598:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57598\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_compilation_speed (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4158450,"byte_end":4158510,"line_start":57603,"line_end":57603,"column_start":32,"column_end":92,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_compilation_status (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":92}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57603:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57603\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_compilation_status (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4158784,"byte_end":4158851,"line_start":57608,"line_end":57608,"column_start":32,"column_end":99,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_compilation_status (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57608:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57608\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_compilation_status (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4159135,"byte_end":4159171,"line_start":57613,"line_end":57613,"column_start":32,"column_end":68,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_cpu_usage (& self) -> f32 {","highlight_start":32,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57613:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57613\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_cpu_usage (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4159436,"byte_end":4159479,"line_start":57618,"line_end":57618,"column_start":32,"column_end":75,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_cpu_usage (& self , value : f32) {","highlight_start":32,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57618:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57618\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_cpu_usage (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4159754,"byte_end":4159886,"line_start":57623,"line_end":57623,"column_start":32,"column_end":164,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_create_project (& self , arg_0 : sp :: SharedString , arg_1 : sp :: SharedString , arg_2 : sp :: SharedString ,) -> () {","highlight_start":32,"highlight_end":164}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57623:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57623\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m)] pub fn invoke_create_project (& self , arg_0 : sp :: SharedString , arg_1 : sp :: SharedString , arg_2 : sp :: SharedString ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4160157,"byte_end":4160290,"line_start":57628,"line_end":57628,"column_start":32,"column_end":165,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_create_project (& self , mut f : impl FnMut (sp :: SharedString , sp :: SharedString , sp :: SharedString) -> () + 'static) {","highlight_start":32,"highlight_end":165}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57628:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57628\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m)] pub fn on_create_project (& self , mut f : impl FnMut (sp :: SharedString , sp :: SharedString , sp :: SharedString) -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4160641,"byte_end":4160695,"line_start":57633,"line_end":57633,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_current_file (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57633:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57633\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_current_file (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4160963,"byte_end":4161024,"line_start":57638,"line_end":57638,"column_start":32,"column_end":93,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_current_file (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57638:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57638\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_current_file (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4161302,"byte_end":4161357,"line_start":57643,"line_end":57643,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_current_theme (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57643:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57643\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_current_theme (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4161626,"byte_end":4161688,"line_start":57648,"line_end":57648,"column_start":32,"column_end":94,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_current_theme (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":94}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57648:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57648\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_current_theme (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4161967,"byte_end":4162009,"line_start":57653,"line_end":57653,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_files_processed (& self) -> i32 {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57653:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57653\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_files_processed (& self) -> i32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4162280,"byte_end":4162329,"line_start":57658,"line_end":57658,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_files_processed (& self , value : i32) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57658:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57658\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_files_processed (& self , value : i32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4162610,"byte_end":4162652,"line_start":57663,"line_end":57663,"column_start":32,"column_end":74,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_glitch_effects (& self) -> bool {","highlight_start":32,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57663:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57663\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_glitch_effects (& self) -> bool {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4162922,"byte_end":4162971,"line_start":57668,"line_end":57668,"column_start":32,"column_end":81,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_glitch_effects (& self , value : bool) {","highlight_start":32,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57668:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57668\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_glitch_effects (& self , value : bool) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4163251,"byte_end":4163290,"line_start":57673,"line_end":57673,"column_start":32,"column_end":71,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_memory_usage (& self) -> f32 {","highlight_start":32,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57673:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57673\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_memory_usage (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4163558,"byte_end":4163604,"line_start":57678,"line_end":57678,"column_start":32,"column_end":78,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_memory_usage (& self , value : f32) {","highlight_start":32,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57678:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57678\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_memory_usage (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4163882,"byte_end":4163923,"line_start":57683,"line_end":57683,"column_start":32,"column_end":73,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_neon_intensity (& self) -> f32 {","highlight_start":32,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57683:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57683\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_neon_intensity (& self) -> f32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4164193,"byte_end":4164241,"line_start":57688,"line_end":57688,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_neon_intensity (& self , value : f32) {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57688:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57688\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_neon_intensity (& self , value : f32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4164521,"byte_end":4164569,"line_start":57693,"line_end":57693,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_primary_color (& self) -> sp :: Color {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57693:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57693\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_primary_color (& self) -> sp :: Color {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4164838,"byte_end":4164893,"line_start":57698,"line_end":57698,"column_start":32,"column_end":87,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_primary_color (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57698:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57698\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_primary_color (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4165172,"byte_end":4165230,"line_start":57703,"line_end":57703,"column_start":32,"column_end":90,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_processing_stage (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57703:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57703\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_processing_stage (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4165502,"byte_end":4165567,"line_start":57708,"line_end":57708,"column_start":32,"column_end":97,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_processing_stage (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":97}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57708:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57708\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_processing_stage (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4165849,"byte_end":4165889,"line_start":57713,"line_end":57713,"column_start":32,"column_end":72,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_project_files (& self) -> i32 {","highlight_start":32,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57713:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57713\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_project_files (& self) -> i32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4166158,"byte_end":4166205,"line_start":57718,"line_end":57718,"column_start":32,"column_end":79,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_project_files (& self , value : i32) {","highlight_start":32,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57718:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57718\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_project_files (& self , value : i32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4166484,"byte_end":4166542,"line_start":57723,"line_end":57723,"column_start":32,"column_end":90,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_project_language (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57723:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57723\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_project_language (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4166814,"byte_end":4166879,"line_start":57728,"line_end":57728,"column_start":32,"column_end":97,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_project_language (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":97}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57728:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57728\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_project_language (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4167161,"byte_end":4167215,"line_start":57733,"line_end":57733,"column_start":32,"column_end":86,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_project_name (& self) -> sp :: SharedString {","highlight_start":32,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57733:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57733\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_project_name (& self) -> sp :: SharedString {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4167483,"byte_end":4167544,"line_start":57738,"line_end":57738,"column_start":32,"column_end":93,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_project_name (& self , value : sp :: SharedString) {","highlight_start":32,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57738:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57738\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_project_name (& self , value : sp :: SharedString) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4167822,"byte_end":4167872,"line_start":57743,"line_end":57743,"column_start":32,"column_end":82,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_secondary_color (& self) -> sp :: Color {","highlight_start":32,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57743:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57743\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_secondary_color (& self) -> sp :: Color {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4168143,"byte_end":4168200,"line_start":57748,"line_end":57748,"column_start":32,"column_end":89,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_secondary_color (& self , value : sp :: Color) {","highlight_start":32,"highlight_end":89}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57748:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57748\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_secondary_color (& self , value : sp :: Color) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4168481,"byte_end":4168555,"line_start":57753,"line_end":57753,"column_start":32,"column_end":106,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_start_analysis (& self , arg_0 : sp :: SharedString ,) -> () {","highlight_start":32,"highlight_end":106}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57753:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57753\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn invoke_start_analysis (& self , arg_0 : sp :: SharedString ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4168810,"byte_end":4168901,"line_start":57758,"line_end":57758,"column_start":32,"column_end":123,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_start_analysis (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {","highlight_start":32,"highlight_end":123}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57758:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57758\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn on_start_analysis (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4169208,"byte_end":4169256,"line_start":57763,"line_end":57763,"column_start":32,"column_end":80,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_start_compilation (& self ,) -> () {","highlight_start":32,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57763:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57763\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn invoke_start_compilation (& self ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4169507,"byte_end":4169583,"line_start":57768,"line_end":57768,"column_start":32,"column_end":108,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_start_compilation (& self , mut f : impl FnMut () -> () + 'static) {","highlight_start":32,"highlight_end":108}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57768:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57768\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn on_start_compilation (& self , mut f : impl FnMut () -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4169874,"byte_end":4169946,"line_start":57773,"line_end":57773,"column_start":32,"column_end":104,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_switch_theme (& self , arg_0 : sp :: SharedString ,) -> () {","highlight_start":32,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57773:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57773\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn invoke_switch_theme (& self , arg_0 : sp :: SharedString ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4170199,"byte_end":4170288,"line_start":57778,"line_end":57778,"column_start":32,"column_end":121,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_switch_theme (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {","highlight_start":32,"highlight_end":121}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57778:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57778\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn on_switch_theme (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4170593,"byte_end":4170631,"line_start":57783,"line_end":57783,"column_start":32,"column_end":70,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn get_total_files (& self) -> i32 {","highlight_start":32,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57783:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57783\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn get_total_files (& self) -> i32 {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4170898,"byte_end":4170943,"line_start":57788,"line_end":57788,"column_start":32,"column_end":77,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn set_total_files (& self , value : i32) {","highlight_start":32,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57788:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57788\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn set_total_files (& self , value : i32) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4171220,"byte_end":4171298,"line_start":57793,"line_end":57793,"column_start":32,"column_end":110,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn invoke_update_preferences (& self , arg_0 : sp :: SharedString ,) -> () {","highlight_start":32,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57793:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57793\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn invoke_update_preferences (& self , arg_0 : sp :: SharedString ,) -> () {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for a method","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs","byte_start":4171557,"byte_end":4171652,"line_start":57798,"line_end":57798,"column_start":32,"column_end":127,"is_primary":true,"text":[{"text":"         # [allow (dead_code)] pub fn on_update_preferences (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {","highlight_start":32,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing documentation for a method\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mc:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs:57798:32\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57798\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m         # [allow (dead_code)] pub fn on_update_preferences (& self , mut f : impl FnMut (sp :: SharedString) -> () + 'static) {\u001b[0m\n\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"linking with `link.exe` failed: exit code: 1120","code":null,"level":"error","spans":[],"children":[{"message":"\"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\bin\\\\HostX64\\\\x64\\\\link.exe\" \"/NOLOGO\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\symbols.o\" \"<221 object files omitted>\" \"c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\\\\haal_cuda.lib\" \"c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\\\\haal.lib\" \"C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\\\\cudart.lib\" \"C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\\\\cuda.lib\" \"C:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps/{libblake3-63e81ceadb5bdda0.rlib,libconstant_time_eq-3264114b2f1db27d.rlib,libmd5-d84797870cd32df5.rlib,libreqwest-8ae0fcc1ef0ffccc.rlib,libserde_urlencoded-78f4dfc3bde0afe3.rlib,librustls_pki_types-60acb31f7a26a7c7.rlib,libzeroize-681184fc434f5fa1.rlib,libhyper_tls-3fd5f599be280b3d.rlib,libtokio_native_tls-b4db960c36b92a90.rlib,libmime-7ad6c1913bc944e2.rlib,libencoding_rs-06b222bc54cdd3ae.rlib,libtower_http-ae3583323e132b29.rlib,libiri_string-ff759171b6adb881.rlib,libtower-828b6493c25ce8df.rlib,libtower_layer-562bf17403e4f363.rlib,libnative_tls-21998f3280eed649.rlib,libschannel-8a3a2c3e827765de.rlib,libhyper_util-f0e510abab9d856b.rlib,libwindows_registry-bd72af1786f54ce4.rlib,libipnet-7123ce2a3794d8e3.rlib,libtower_service-c25440accab56b57.rlib,libhyper-66ac704246ccf337.rlib,libwant-8806602f42f40509.rlib,libtry_lock-34576e8b240dd8ad.rlib,libhttparse-7eebae40f3537ccd.rlib,libh2-7cb7a93e0e07c0a6.rlib,libindexmap-75bf3104ecab3055.rlib,libatomic_waker-733411725d00cdd1.rlib,libtokio_util-e686e46ddcb76818.rlib,libfutures_sink-a6d190d3a8fcb8b8.rlib,libfutures_channel-e3d3683f3718bdf5.rlib,libfutures_util-86853a2efbd1a964.rlib,libfutures_task-a81ede2821d2f383.rlib,libpin_utils-cdf15da9de6652f6.rlib,libhttp_body_util-c1a0329cd73bb4c4.rlib,libhttp_body-1241c5258b3dc20b.rlib,liburl-f952211e398dee82.rlib,libidna-139fa9d1a5d9fd8a.rlib,libutf8_iter-0f75330c9d9edd8c.rlib,libidna_adapter-13336d22f9dd1d7e.rlib,libicu_properties-ff92a75f43125732.rlib,libicu_properties_data-a178669f4a90f9f5.rlib,libicu_normalizer-5ec807db437d361d.rlib,libicu_normalizer_data-8a4820850a0d897f.rlib,libicu_collections-8e90605843d91d22.rlib,libpotential_utf-01c16c4b32bff03d.rlib,libicu_provider-dc84ec8e05f5af72.rlib,libicu_locale_core-1d240ea678877d65.rlib,libtinystr-f2492b5f22e5db63.rlib,liblitemap-b3222eb183f20132.rlib,libwriteable-b15b96739e441219.rlib,libzerovec-fb7e7a0ebfdf4fcb.rlib,libzerotrie-b3063ba871702bc4.rlib,libyoke-a3257775e9dbe0b5.rlib,libzerofrom-8fb7419433bce79a.rlib,libform_urlencoded-f7412314c0eb5b1b.rlib,libpercent_encoding-7b1b5cbfc31ea8e2.rlib,libhttp-fe373aff1fe7a821.rlib,libsync_wrapper-14e1086386d8af9a.rlib,libfutures_core-1081b66f01af0139.rlib,libnum_cpus-88a84070ce6200d7.rlib,libanyhow-fd81d349c638a011.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libtest-*,libgetopts-*,libunicode_width-*,librustc_std_workspace_std-*}.rlib\" \"C:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps/{libsafetensors-fd2ddc16df4986ef.rlib,libserde_json-107cbdfffa65550a.rlib,libitoa-6368bc7d7734ae28.rlib,libryu-37203d2c467f8da8.rlib,libndarray-9f8432dc05e8b26e.rlib,libmatrixmultiply-8f774a3e59fbf9cf.rlib,libnum_complex-1b75b6bf02379615.rlib,libnum_integer-09bad948a2e5b802.rlib,librawpointer-c135951b6568fc03.rlib,libblake2-401b792d1b0075b8.rlib,libsha2-a73e7119b4dab8fc.rlib,libsha1-43be964f00bea6f7.rlib,libcpufeatures-a8f41485e3a6e52b.rlib,libdigest-6ba9ff22989e81ac.rlib,libsubtle-c12f9f250859eba5.rlib,libblock_buffer-1f8e4cd1fee1c6f4.rlib,libcrypto_common-fd351a98660716a0.rlib,libgeneric_array-155988e0fd227abd.rlib,libtypenum-870e3f25c28016dd.rlib,libtokio-4aed559d84cfee88.rlib,libsocket2-be0a3f858f91494e.rlib,libbytes-441da4d76b13cbcb.rlib,libmio-ce8c2b09dcc03128.rlib,libparking_lot-17976544b3d36956.rlib,libparking_lot_core-607802e7c51bef2a.rlib,liblock_api-1fa8d661bc033702.rlib,libslint-b2853a4d13393c6e.rlib,libi_slint_backend_selector-9394a048989fc80c.rlib,libi_slint_backend_qt-3515a1ecaf0f8a63.rlib,libi_slint_backend_winit-209937881dd39349.rlib,libsoftbuffer-1f2355087263af53.rlib,libglutin_winit-134c157c223e6048.rlib,libaccesskit_winit-3d96ec0488e090d0.rlib,libaccesskit_windows-d50ad04896611bcf.rlib,libstatic_assertions-da84d677a2cbb685.rlib,libwindows-c2e744b4a9bbf690.rlib,libwindows_core-937b2b20fa935ee7.rlib,libwindows_strings-fb86e9028cf0e64a.rlib,libwindows_result-1fc2a3f698787973.rlib,libaccesskit_consumer-1554010bdd565fb1.rlib,libmuda-0ecb3b8d911dc5d4.rlib,libwindows_sys-56fc0990e02e453f.rlib,libkeyboard_types-fc80ba9de73cf77d.rlib,libcrossbeam_channel-ac802e3a247db03e.rlib,libaccesskit-c74e3852d16abc9a.rlib,libglutin-7e5ddc7d9f506986.rlib,libglutin_wgl_sys-20d46127755d6c7a.rlib,liblibloading-a0c05abea211f972.rlib,libwindows_targets-d29c871916be2113.rlib,libglutin_egl_sys-5c15c7dbfa4ec774.rlib,libi_slint_renderer_femtovg-4b579362beb6116a.rlib,libdwrote-0dcb28ed0ec36e85.rlib,libwio-bec023c66e7094a8.rlib,libwinapi-da7931955f41af73.rlib,liblibc-1387bd7560c56224.rlib,liblazy_static-4b97267ccf00dd5f.rlib,libfemtovg-661a118fbb19cedf.rlib,libglow-8f2eef39fdb125a9.rlib,libimgref-683c76423bfee7b1.rlib,liblru-373aa59d959ec331.rlib,libfnv-a847c0352ba4ddbf.rlib,libwinit-fbe13c8488048057.rlib,libtracing-de8a97f23dbd99a1.rlib,libpin_project_lite-c2856b841f2f0624.rlib,libtracing_core-6fdb1e790f3c982e.rlib,libwindows_sys-c4e7dd6dda70ea71.rlib,libwindows_targets-e7bdccf469b35d45.rlib,libsmol_str-cc438a088963ba4b.rlib,libcursor_icon-fff2368e94a6b8ee.rlib,libdpi-eab133ea060eeb4b.rlib,libraw_window_handle-717186cde8cf3c17.rlib,libcopypasta-108be8ef247d724f.rlib,libclipboard_win-1bbfdfcb67d9f965.rlib,liberror_code-56fe339badc0ee22.rlib,libi_slint_core-e2b1f3973e3fa6d4.rlib,libsys_locale-943b678deede86ad.rlib,libfontdue-8f670d66ecec430f.rlib,libhashbrown-33aac186b642ee74.rlib,libfoldhash-c7e1bc489b06a6d3.rlib,libequivalent-43bf456d570bccb8.rlib,liballocator_api2-fadbfa903004c4ba.rlib,libttf_parser-5ba582169e9f65f2.rlib,libpin_weak-50cd977bf378d6d7.rlib,libslab-c7f7dfa5f20b7f04.rlib,libimage-52d46ca55b62b5c1.rlib,libclru-2f1ffa109d2f0aac.rlib,liblyon_extra-047d052854242122.rlib,libthiserror-cf670e6bce2f6104.rlib,liblyon_algorithms-92ead121095579f8.rlib,libscopeguard-2e8788bf023e5fec.rlib,libscoped_tls_hkt-e1ad5ba82193d274.rlib,libstrum-38a5bc82fafe84f5.rlib,libunicode_linebreak-4a86d534fe38d6d7.rlib,libinteger_sqrt-e4c910c7f9dcfd5e.rlib,libpin_project-a8b5d63b1b72a9cc.rlib,libunicode_segmentation-0b8b78906ba6799f.rlib,libonce_cell-953124f5d8680522.rlib,libi_slint_common-c1bb89d3130f4a0b.rlib,libderive_more-d75c00d539a570fc.rlib,libresvg-68caf9a7e31f3929.rlib,libimage_webp-908eadc195e3373d.rlib,libquick_error-2830729fc32cb5b5.rlib,libbyteorder_lite-0d4c65e2185bf985.rlib,libgif-ab3afdf43f671e03.rlib,libcolor_quant-9621b24b5df41469.rlib,libweezl-0241878353d49911.rlib,libzune_jpeg-7ff82a9fc21a8553.rlib,libzune_core-202e9d2649b9f0b2.rlib,librgb-f5c60f9ee2cf2e07.rlib,libusvg-d7d1ed2032b8df1b.rlib,libbase64-0b360acb9dc29ba9.rlib,libunicode_bidi-dd57cdf9a12388dc.rlib,libunicode_vo-3b067c95cf233815.rlib,libimagesize-9395471f3b99ebfc.rlib,libdata_url-02442b37644cfc7c.rlib,libxmlwriter-dd694a8caf230493.rlib,librustybuzz-b5cfd9f5ebc3d0be.rlib,libunicode_bidi_mirroring-e2a40a7016c14b1e.rlib,libunicode_script-b81b60a8891c52b3.rlib,libunicode_properties-e462e786e06a1e5b.rlib,libunicode_ccc-2d2b18129b525065.rlib,libsimplecss-2989dfa749e2025a.rlib,libroxmltree-ce8ecf9a60e2df71.rlib,libsvgtypes-4474a338e799aeb5.rlib,libkurbo-c0d5e10e69e37ec9.rlib,libsmallvec-058dce38b90e64ce.rlib,libsiphasher-42fc3a30d859435d.rlib,libfontdb-8f0f3250ccd695b9.rlib,libtinyvec-0052af770ded6c9b.rlib,libtinyvec_macros-97bd24982df1741e.rlib,libslotmap-17efe8d1736c7384.rlib,libttf_parser-cadecfb1793f085f.rlib,libtiny_skia-9af49fcfcdff265f.rlib,libpng-f9c83985a2e4b145.rlib,libbitflags-505cc21d5fbc6b17.rlib,libfdeflate-f85a65c94cbb79c4.rlib,libtiny_skia_path-93f9a73bbd8e42d7.rlib,libbytemuck-3bde64f0c230cae0.rlib,libstrict_num-5a1476a79a38273f.rlib,libfloat_cmp-8a86a94467814896.rlib,libarrayref-52c49de161ce2263.rlib,liblyon_path-30b0233800d802ea.rlib,liblyon_geom-21990d4488ab1efd.rlib,libarrayvec-d50e6e157ab9318a.rlib,libchrono-7a497d66b394f2cd.rlib,libwindows_link-71c4caf817584dd3.rlib,libeuclid-7f3ca35d8fab7bc0.rlib,libnum_traits-c91e2111e0c5fbf7.rlib,libvtable-eac4476f3e3320f3.rlib,libstable_deref_trait-86709a420244e07a.rlib,libportable_atomic-e2a6761bf3da69d0.rlib,libconst_field_offset-e907ffc8ac546a9b.rlib,libfield_offset-86c1c96021a9bae2.rlib,libmemoffset-e4e8a3c8ef4355a5.rlib,libbitflags-1bb05d8130012bba.rlib,libthiserror-aa7c20d33bc0fbc4.rlib,librayon-cc76c4a6a289fa89.rlib,librayon_core-cd657a5ae62b542d.rlib,libcrossbeam_deque-ca3e19ed2f176f12.rlib,libcrossbeam_epoch-df2119fda5b472da.rlib,libcrossbeam_utils-36121936532ee47c.rlib,libeither-8f58f7199ad728f9.rlib,libregex-ed33caa655a42e5d.rlib,libregex_automata-9b12262d9d48d003.rlib,libaho_corasick-5879e06a309fe4bf.rlib,libregex_syntax-ada627dd6af84b16.rlib,libgoblin-cd6b845b9e6357cf.rlib,libplain-dae4b83da353669b.rlib,liblog-e462dc2b9ca24910.rlib,libscroll-b0538f0b74f9976d.rlib,libmemmap2-7a79167e9edf8762.rlib,libobject-99d929d954fd53e2.rlib,libruzstd-b9316ca62f857b1f.rlib,libtwox_hash-acdacd443cda2a1a.rlib,librand-9287c0f8ec50aed4.rlib,librand_chacha-0ebd5ebe6a758449.rlib,libppv_lite86-cb392fc530abf352.rlib,libzerocopy-e1dffdfb07107702.rlib,librand_core-c7b89b56df34ab9d.rlib,libgetrandom-8bb760dd905928cf.rlib,libflate2-4fe658a9a3da2f72.rlib,libminiz_oxide-7b9df2584c2484db.rlib,libsimd_adler32-a9b7124fecbe25fa.rlib,libcrc32fast-f8630a6ffc87d747.rlib,libcfg_if-3d39ba26ec35ebac.rlib,libmemchr-b6ed0b4c508ed0f6.rlib,libserde-6995877d184982c4.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libstd-*,libpanic_unwind-*,libwindows_targets-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libunwind-*,libcfg_if-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"kernel32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"opengl32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.53.0\\\\lib\\\\windows.0.53.0.lib\" \"advapi32.lib\" \"cfgmgr32.lib\" \"d2d1.lib\" \"dwrite.lib\" \"dxgi.lib\" \"gdi32.lib\" \"kernel32.lib\" \"msimg32.lib\" \"ole32.lib\" \"opengl32.lib\" \"user32.lib\" \"windowscodecs.lib\" \"winspool.lib\" \"legacy_stdio_definitions.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"kernel32.lib\" \"user32.lib\" \"shell32.lib\" \"gdi32.lib\" \"advapi32.lib\" \"kernel32.lib\" \"kernel32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"dbghelp.lib\" \"/defaultlib:msvcrt\" \"/NXCOMPAT\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\blake3-18acd635bd91018c\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\blake3-18acd635bd91018c\\\\out\" \"/LIBPATH:C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.53.0\\\\lib\" \"/OUT:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps\\\\omni_forge-2f88790c22589e0e.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/PDBALTPATH:%_PDB%\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-1.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-2.natvis\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"omni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol create_haal_orchestrator referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator3new17hb5813b20816c0b64E\r\nomni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_initialize referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator10initialize17h424f20d026434db1E\r\nomni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_execute_computation referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator28execute_with_characteristics17h00362bed5b0f7f93E\r\nomni_forge-2f88790c22589e0e.ah1bg55e2qpq6j5wzw4t2tx20.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_cleanup referenced in function _ZN80_$LT$omni_forge..ahaw..OmniForgeAccelerator$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd2be32d5cc585424E\r\nomni_forge-2f88790c22589e0e.ah1bg55e2qpq6j5wzw4t2tx20.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol destroy_haal_orchestrator referenced in function _ZN80_$LT$omni_forge..ahaw..OmniForgeAccelerator$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd2be32d5cc585424E\r\nc:\\_Repos\\OmniCodex\\target\\debug\\deps\\omni_forge-2f88790c22589e0e.exe : fatal error LNK1120: 5 unresolved externals\r\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: linking with `link.exe` failed: exit code: 1120\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: \"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\bin\\\\HostX64\\\\x64\\\\link.exe\" \"/NOLOGO\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\symbols.o\" \"<221 object files omitted>\" \"c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\\\\haal_cuda.lib\" \"c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\\\\haal.lib\" \"C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\\\\cudart.lib\" \"C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\\\\cuda.lib\" \"C:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps/{libblake3-63e81ceadb5bdda0.rlib,libconstant_time_eq-3264114b2f1db27d.rlib,libmd5-d84797870cd32df5.rlib,libreqwest-8ae0fcc1ef0ffccc.rlib,libserde_urlencoded-78f4dfc3bde0afe3.rlib,librustls_pki_types-60acb31f7a26a7c7.rlib,libzeroize-681184fc434f5fa1.rlib,libhyper_tls-3fd5f599be280b3d.rlib,libtokio_native_tls-b4db960c36b92a90.rlib,libmime-7ad6c1913bc944e2.rlib,libencoding_rs-06b222bc54cdd3ae.rlib,libtower_http-ae3583323e132b29.rlib,libiri_string-ff759171b6adb881.rlib,libtower-828b6493c25ce8df.rlib,libtower_layer-562bf17403e4f363.rlib,libnative_tls-21998f3280eed649.rlib,libschannel-8a3a2c3e827765de.rlib,libhyper_util-f0e510abab9d856b.rlib,libwindows_registry-bd72af1786f54ce4.rlib,libipnet-7123ce2a3794d8e3.rlib,libtower_service-c25440accab56b57.rlib,libhyper-66ac704246ccf337.rlib,libwant-8806602f42f40509.rlib,libtry_lock-34576e8b240dd8ad.rlib,libhttparse-7eebae40f3537ccd.rlib,libh2-7cb7a93e0e07c0a6.rlib,libindexmap-75bf3104ecab3055.rlib,libatomic_waker-733411725d00cdd1.rlib,libtokio_util-e686e46ddcb76818.rlib,libfutures_sink-a6d190d3a8fcb8b8.rlib,libfutures_channel-e3d3683f3718bdf5.rlib,libfutures_util-86853a2efbd1a964.rlib,libfutures_task-a81ede2821d2f383.rlib,libpin_utils-cdf15da9de6652f6.rlib,libhttp_body_util-c1a0329cd73bb4c4.rlib,libhttp_body-1241c5258b3dc20b.rlib,liburl-f952211e398dee82.rlib,libidna-139fa9d1a5d9fd8a.rlib,libutf8_iter-0f75330c9d9edd8c.rlib,libidna_adapter-13336d22f9dd1d7e.rlib,libicu_properties-ff92a75f43125732.rlib,libicu_properties_data-a178669f4a90f9f5.rlib,libicu_normalizer-5ec807db437d361d.rlib,libicu_normalizer_data-8a4820850a0d897f.rlib,libicu_collections-8e90605843d91d22.rlib,libpotential_utf-01c16c4b32bff03d.rlib,libicu_provider-dc84ec8e05f5af72.rlib,libicu_locale_core-1d240ea678877d65.rlib,libtinystr-f2492b5f22e5db63.rlib,liblitemap-b3222eb183f20132.rlib,libwriteable-b15b96739e441219.rlib,libzerovec-fb7e7a0ebfdf4fcb.rlib,libzerotrie-b3063ba871702bc4.rlib,libyoke-a3257775e9dbe0b5.rlib,libzerofrom-8fb7419433bce79a.rlib,libform_urlencoded-f7412314c0eb5b1b.rlib,libpercent_encoding-7b1b5cbfc31ea8e2.rlib,libhttp-fe373aff1fe7a821.rlib,libsync_wrapper-14e1086386d8af9a.rlib,libfutures_core-1081b66f01af0139.rlib,libnum_cpus-88a84070ce6200d7.rlib,libanyhow-fd81d349c638a011.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libtest-*,libgetopts-*,libunicode_width-*,librustc_std_workspace_std-*}.rlib\" \"C:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps/{libsafetensors-fd2ddc16df4986ef.rlib,libserde_json-107cbdfffa65550a.rlib,libitoa-6368bc7d7734ae28.rlib,libryu-37203d2c467f8da8.rlib,libndarray-9f8432dc05e8b26e.rlib,libmatrixmultiply-8f774a3e59fbf9cf.rlib,libnum_complex-1b75b6bf02379615.rlib,libnum_integer-09bad948a2e5b802.rlib,librawpointer-c135951b6568fc03.rlib,libblake2-401b792d1b0075b8.rlib,libsha2-a73e7119b4dab8fc.rlib,libsha1-43be964f00bea6f7.rlib,libcpufeatures-a8f41485e3a6e52b.rlib,libdigest-6ba9ff22989e81ac.rlib,libsubtle-c12f9f250859eba5.rlib,libblock_buffer-1f8e4cd1fee1c6f4.rlib,libcrypto_common-fd351a98660716a0.rlib,libgeneric_array-155988e0fd227abd.rlib,libtypenum-870e3f25c28016dd.rlib,libtokio-4aed559d84cfee88.rlib,libsocket2-be0a3f858f91494e.rlib,libbytes-441da4d76b13cbcb.rlib,libmio-ce8c2b09dcc03128.rlib,libparking_lot-17976544b3d36956.rlib,libparking_lot_core-607802e7c51bef2a.rlib,liblock_api-1fa8d661bc033702.rlib,libslint-b2853a4d13393c6e.rlib,libi_slint_backend_selector-9394a048989fc80c.rlib,libi_slint_backend_qt-3515a1ecaf0f8a63.rlib,libi_slint_backend_winit-209937881dd39349.rlib,libsoftbuffer-1f2355087263af53.rlib,libglutin_winit-134c157c223e6048.rlib,libaccesskit_winit-3d96ec0488e090d0.rlib,libaccesskit_windows-d50ad04896611bcf.rlib,libstatic_assertions-da84d677a2cbb685.rlib,libwindows-c2e744b4a9bbf690.rlib,libwindows_core-937b2b20fa935ee7.rlib,libwindows_strings-fb86e9028cf0e64a.rlib,libwindows_result-1fc2a3f698787973.rlib,libaccesskit_consumer-1554010bdd565fb1.rlib,libmuda-0ecb3b8d911dc5d4.rlib,libwindows_sys-56fc0990e02e453f.rlib,libkeyboard_types-fc80ba9de73cf77d.rlib,libcrossbeam_channel-ac802e3a247db03e.rlib,libaccesskit-c74e3852d16abc9a.rlib,libglutin-7e5ddc7d9f506986.rlib,libglutin_wgl_sys-20d46127755d6c7a.rlib,liblibloading-a0c05abea211f972.rlib,libwindows_targets-d29c871916be2113.rlib,libglutin_egl_sys-5c15c7dbfa4ec774.rlib,libi_slint_renderer_femtovg-4b579362beb6116a.rlib,libdwrote-0dcb28ed0ec36e85.rlib,libwio-bec023c66e7094a8.rlib,libwinapi-da7931955f41af73.rlib,liblibc-1387bd7560c56224.rlib,liblazy_static-4b97267ccf00dd5f.rlib,libfemtovg-661a118fbb19cedf.rlib,libglow-8f2eef39fdb125a9.rlib,libimgref-683c76423bfee7b1.rlib,liblru-373aa59d959ec331.rlib,libfnv-a847c0352ba4ddbf.rlib,libwinit-fbe13c8488048057.rlib,libtracing-de8a97f23dbd99a1.rlib,libpin_project_lite-c2856b841f2f0624.rlib,libtracing_core-6fdb1e790f3c982e.rlib,libwindows_sys-c4e7dd6dda70ea71.rlib,libwindows_targets-e7bdccf469b35d45.rlib,libsmol_str-cc438a088963ba4b.rlib,libcursor_icon-fff2368e94a6b8ee.rlib,libdpi-eab133ea060eeb4b.rlib,libraw_window_handle-717186cde8cf3c17.rlib,libcopypasta-108be8ef247d724f.rlib,libclipboard_win-1bbfdfcb67d9f965.rlib,liberror_code-56fe339badc0ee22.rlib,libi_slint_core-e2b1f3973e3fa6d4.rlib,libsys_locale-943b678deede86ad.rlib,libfontdue-8f670d66ecec430f.rlib,libhashbrown-33aac186b642ee74.rlib,libfoldhash-c7e1bc489b06a6d3.rlib,libequivalent-43bf456d570bccb8.rlib,liballocator_api2-fadbfa903004c4ba.rlib,libttf_parser-5ba582169e9f65f2.rlib,libpin_weak-50cd977bf378d6d7.rlib,libslab-c7f7dfa5f20b7f04.rlib,libimage-52d46ca55b62b5c1.rlib,libclru-2f1ffa109d2f0aac.rlib,liblyon_extra-047d052854242122.rlib,libthiserror-cf670e6bce2f6104.rlib,liblyon_algorithms-92ead121095579f8.rlib,libscopeguard-2e8788bf023e5fec.rlib,libscoped_tls_hkt-e1ad5ba82193d274.rlib,libstrum-38a5bc82fafe84f5.rlib,libunicode_linebreak-4a86d534fe38d6d7.rlib,libinteger_sqrt-e4c910c7f9dcfd5e.rlib,libpin_project-a8b5d63b1b72a9cc.rlib,libunicode_segmentation-0b8b78906ba6799f.rlib,libonce_cell-953124f5d8680522.rlib,libi_slint_common-c1bb89d3130f4a0b.rlib,libderive_more-d75c00d539a570fc.rlib,libresvg-68caf9a7e31f3929.rlib,libimage_webp-908eadc195e3373d.rlib,libquick_error-2830729fc32cb5b5.rlib,libbyteorder_lite-0d4c65e2185bf985.rlib,libgif-ab3afdf43f671e03.rlib,libcolor_quant-9621b24b5df41469.rlib,libweezl-0241878353d49911.rlib,libzune_jpeg-7ff82a9fc21a8553.rlib,libzune_core-202e9d2649b9f0b2.rlib,librgb-f5c60f9ee2cf2e07.rlib,libusvg-d7d1ed2032b8df1b.rlib,libbase64-0b360acb9dc29ba9.rlib,libunicode_bidi-dd57cdf9a12388dc.rlib,libunicode_vo-3b067c95cf233815.rlib,libimagesize-9395471f3b99ebfc.rlib,libdata_url-02442b37644cfc7c.rlib,libxmlwriter-dd694a8caf230493.rlib,librustybuzz-b5cfd9f5ebc3d0be.rlib,libunicode_bidi_mirroring-e2a40a7016c14b1e.rlib,libunicode_script-b81b60a8891c52b3.rlib,libunicode_properties-e462e786e06a1e5b.rlib,libunicode_ccc-2d2b18129b525065.rlib,libsimplecss-2989dfa749e2025a.rlib,libroxmltree-ce8ecf9a60e2df71.rlib,libsvgtypes-4474a338e799aeb5.rlib,libkurbo-c0d5e10e69e37ec9.rlib,libsmallvec-058dce38b90e64ce.rlib,libsiphasher-42fc3a30d859435d.rlib,libfontdb-8f0f3250ccd695b9.rlib,libtinyvec-0052af770ded6c9b.rlib,libtinyvec_macros-97bd24982df1741e.rlib,libslotmap-17efe8d1736c7384.rlib,libttf_parser-cadecfb1793f085f.rlib,libtiny_skia-9af49fcfcdff265f.rlib,libpng-f9c83985a2e4b145.rlib,libbitflags-505cc21d5fbc6b17.rlib,libfdeflate-f85a65c94cbb79c4.rlib,libtiny_skia_path-93f9a73bbd8e42d7.rlib,libbytemuck-3bde64f0c230cae0.rlib,libstrict_num-5a1476a79a38273f.rlib,libfloat_cmp-8a86a94467814896.rlib,libarrayref-52c49de161ce2263.rlib,liblyon_path-30b0233800d802ea.rlib,liblyon_geom-21990d4488ab1efd.rlib,libarrayvec-d50e6e157ab9318a.rlib,libchrono-7a497d66b394f2cd.rlib,libwindows_link-71c4caf817584dd3.rlib,libeuclid-7f3ca35d8fab7bc0.rlib,libnum_traits-c91e2111e0c5fbf7.rlib,libvtable-eac4476f3e3320f3.rlib,libstable_deref_trait-86709a420244e07a.rlib,libportable_atomic-e2a6761bf3da69d0.rlib,libconst_field_offset-e907ffc8ac546a9b.rlib,libfield_offset-86c1c96021a9bae2.rlib,libmemoffset-e4e8a3c8ef4355a5.rlib,libbitflags-1bb05d8130012bba.rlib,libthiserror-aa7c20d33bc0fbc4.rlib,librayon-cc76c4a6a289fa89.rlib,librayon_core-cd657a5ae62b542d.rlib,libcrossbeam_deque-ca3e19ed2f176f12.rlib,libcrossbeam_epoch-df2119fda5b472da.rlib,libcrossbeam_utils-36121936532ee47c.rlib,libeither-8f58f7199ad728f9.rlib,libregex-ed33caa655a42e5d.rlib,libregex_automata-9b12262d9d48d003.rlib,libaho_corasick-5879e06a309fe4bf.rlib,libregex_syntax-ada627dd6af84b16.rlib,libgoblin-cd6b845b9e6357cf.rlib,libplain-dae4b83da353669b.rlib,liblog-e462dc2b9ca24910.rlib,libscroll-b0538f0b74f9976d.rlib,libmemmap2-7a79167e9edf8762.rlib,libobject-99d929d954fd53e2.rlib,libruzstd-b9316ca62f857b1f.rlib,libtwox_hash-acdacd443cda2a1a.rlib,librand-9287c0f8ec50aed4.rlib,librand_chacha-0ebd5ebe6a758449.rlib,libppv_lite86-cb392fc530abf352.rlib,libzerocopy-e1dffdfb07107702.rlib,librand_core-c7b89b56df34ab9d.rlib,libgetrandom-8bb760dd905928cf.rlib,libflate2-4fe658a9a3da2f72.rlib,libminiz_oxide-7b9df2584c2484db.rlib,libsimd_adler32-a9b7124fecbe25fa.rlib,libcrc32fast-f8630a6ffc87d747.rlib,libcfg_if-3d39ba26ec35ebac.rlib,libmemchr-b6ed0b4c508ed0f6.rlib,libserde-6995877d184982c4.rlib}.rlib\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libstd-*,libpanic_unwind-*,libwindows_targets-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libunwind-*,libcfg_if-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"kernel32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"opengl32.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.53.0\\\\lib\\\\windows.0.53.0.lib\" \"advapi32.lib\" \"cfgmgr32.lib\" \"d2d1.lib\" \"dwrite.lib\" \"dxgi.lib\" \"gdi32.lib\" \"kernel32.lib\" \"msimg32.lib\" \"ole32.lib\" \"opengl32.lib\" \"user32.lib\" \"windowscodecs.lib\" \"winspool.lib\" \"legacy_stdio_definitions.lib\" \"C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\\\\windows.0.52.0.lib\" \"kernel32.lib\" \"user32.lib\" \"shell32.lib\" \"gdi32.lib\" \"advapi32.lib\" \"kernel32.lib\" \"kernel32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"dbghelp.lib\" \"/defaultlib:msvcrt\" \"/NXCOMPAT\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\omni_forge-ffeb5028db28ed9f\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\blake3-18acd635bd91018c\\\\out\" \"/LIBPATH:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\build\\\\blake3-18acd635bd91018c\\\\out\" \"/LIBPATH:C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files\\\\NVIDIA GPU Computing Toolkit\\\\CUDA\\\\v12.8\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2022\\\\BuildTools\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x64\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1949cf8c6b5b557f\\\\windows_x86_64_msvc-0.53.0\\\\lib\" \"/OUT:c:\\\\_Repos\\\\OmniCodex\\\\target\\\\debug\\\\deps\\\\omni_forge-2f88790c22589e0e.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/PDBALTPATH:%_PDB%\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-1.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustc4eb4QS\\\\omni_forge-2.natvis\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: omni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol create_haal_orchestrator referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator3new17hb5813b20816c0b64E␍\u001b[0m\n\u001b[0m          omni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_initialize referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator10initialize17h424f20d026434db1E␍\u001b[0m\n\u001b[0m          omni_forge-2f88790c22589e0e.4t29fhqve0d0iso7bzvkhrd8l.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_execute_computation referenced in function _ZN10omni_forge4ahaw20OmniForgeAccelerator28execute_with_characteristics17h00362bed5b0f7f93E␍\u001b[0m\n\u001b[0m          omni_forge-2f88790c22589e0e.ah1bg55e2qpq6j5wzw4t2tx20.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol haal_cleanup referenced in function _ZN80_$LT$omni_forge..ahaw..OmniForgeAccelerator$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd2be32d5cc585424E␍\u001b[0m\n\u001b[0m          omni_forge-2f88790c22589e0e.ah1bg55e2qpq6j5wzw4t2tx20.0kq7g4d.rcgu.o : error LNK2019: unresolved external symbol destroy_haal_orchestrator referenced in function _ZN80_$LT$omni_forge..ahaw..OmniForgeAccelerator$u20$as$u20$core..ops..drop..Drop$GT$4drop17hd2be32d5cc585424E␍\u001b[0m\n\u001b[0m          c:\\_Repos\\OmniCodex\\target\\debug\\deps\\omni_forge-2f88790c22589e0e.exe : fatal error LNK1120: 5 unresolved externals␍\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 146 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 146 warnings emitted\u001b[0m\n\n"}
