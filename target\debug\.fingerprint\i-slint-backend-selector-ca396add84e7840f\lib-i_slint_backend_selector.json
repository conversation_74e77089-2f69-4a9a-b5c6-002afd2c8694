{"rustc": 1842507548689473721, "features": "[\"accessibility\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-femtovg-wgpu\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\", \"unstable-wgpu-24\", \"unstable-winit-030\"]", "target": 17512649618174676683, "profile": 2241668132362809309, "path": 5909146337057074783, "deps": [[2828590642173593838, "cfg_if", false, 309483949851362009], [6025808826613480944, "i_slint_backend_qt", false, 17693210448389591355], [7011550512183068068, "build_script_build", false, 7802416190119395257], [13170356948319896346, "i_slint_backend_winit", false, 17289992457275963165], [15764579764801749776, "i_slint_core_macros", false, 18325846628304979310], [18113059991484317634, "i_slint_core", false, 15710703813073385180]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-ca396add84e7840f\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}