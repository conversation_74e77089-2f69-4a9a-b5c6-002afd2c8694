cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=haal/
cargo:rerun-if-changed=ui/
cargo:rerun-if-changed=src/gui/
cargo:warning=Building HAAL (Hardware Acceleration Abstraction Layer)
cargo:rustc-link-search=native=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\lib\x64
cargo:warning=CUDA detected - enabling GPU acceleration
cargo:warning=OneAPI not found, using MSVC compiler
cargo:warning=Compiling with Windows AVX2 optimization (/arch:AVX2)
cargo:rerun-if-changed=c:\_Repos\OmniCodex\haal\haal-orc.cpp
cargo:warning=HAAL source file not found: c:\_Repos\OmniCodex\haal\haal-avx2.cu
cargo:warning=HAAL source file not found: c:\_Repos\OmniCodex\haal\haal-c-wrapper.cpp
haal-cuda.cu
tmpxft_000083a4_00000000-10_haal-cuda.cudafe1.cpp
cargo:warning=Compiled CUDA source: haal-cuda.cu
cargo:warning=CUDA source file not found: c:\_Repos\OmniCodex\haal\cuda-wrappers.cu
cargo:warning=Using Windows archiver: lib.exe
Microsoft (R) Library Manager Version 14.44.35213.0
Copyright (C) Microsoft Corporation.  All rights reserved.

cargo:rustc-link-lib=static=haal_cuda
cargo:rustc-link-search=native=c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out
cargo:warning=CUDA static library created successfully
OUT_DIR = Some(c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(c:\_Repos\OmniCodex\target\debug\deps;c:\_Repos\OmniCodex\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\_Repos\._0Scripts;C:\_Repos\;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
haal-orc.cpp
c:\_Repos\OmniCodex\haal\haal-orc.cpp(245): warning C4100: 'dataSize': unreferenced parameter
c:\_Repos\OmniCodex\haal\haal-orc.cpp(245): warning C4100: 'operation': unreferenced parameter
c:\_Repos\OmniCodex\haal\haal-orc.cpp(463): warning C4100: 'dataSize': unreferenced parameter
c:\_Repos\OmniCodex\haal\haal-orc.cpp(463): warning C4100: 'operation': unreferenced parameter
c:\_Repos\OmniCodex\haal\haal-orc.cpp(548): warning C4100: 'operation': unreferenced parameter
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(715): warning C4505: '__low2float': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(727): warning C4505: '__high2float': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(964): warning C4505: 'make_half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(971): warning C4505: '__float22half2_rn': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(976): warning C4505: '__half22float2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1029): warning C4505: '__int2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1040): warning C4505: '__int2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1051): warning C4505: '__int2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1093): warning C4505: '__short2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1104): warning C4505: '__short2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1115): warning C4505: '__short2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1161): warning C4505: '__uint2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1172): warning C4505: '__uint2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1183): warning C4505: '__uint2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1225): warning C4505: '__ushort2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1236): warning C4505: '__ushort2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1247): warning C4505: '__ushort2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1293): warning C4505: '__ull2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1304): warning C4505: '__ull2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1315): warning C4505: '__ull2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1361): warning C4505: '__ll2half_rz': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1372): warning C4505: '__ll2half_rd': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1383): warning C4505: '__ll2half_ru': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1461): warning C4505: '__lows2half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1475): warning C4505: '__highs2half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1501): warning C4505: '__hisinf': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1514): warning C4505: '__low2half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1527): warning C4505: '__high2half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1564): warning C4505: '__half2half2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1576): warning C4505: '__lowhigh2highlow': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1589): warning C4505: '__half_as_short': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1597): warning C4505: '__half_as_ushort': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(1605): warning C4505: '__short_as_half': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2007): warning C4505: '__heq2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2018): warning C4505: '__hne2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2029): warning C4505: '__hle2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2040): warning C4505: '__hge2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2051): warning C4505: '__hlt2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2062): warning C4505: '__hgt2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2073): warning C4505: '__hequ2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2084): warning C4505: '__hneu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2095): warning C4505: '__hleu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2106): warning C4505: '__hgeu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2117): warning C4505: '__hltu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2128): warning C4505: '__hgtu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2312): warning C4505: '__hbne2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2337): warning C4505: '__hbequ2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2347): warning C4505: '__hbleu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2352): warning C4505: '__hbgeu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2357): warning C4505: '__hbltu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2362): warning C4505: '__hbgtu2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2535): warning C4505: '__hadd2_sat': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2546): warning C4505: '__hsub2_sat': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2557): warning C4505: '__hmul2_sat': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2568): warning C4505: '__hadd2_rn': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2579): warning C4505: '__hsub2_rn': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(2590): warning C4505: '__hmul2_rn': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3229): warning C4505: '__hisnan2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3279): warning C4505: '__habs2': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3324): warning C4505: '__hmax_nan': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3341): warning C4505: '__hmin_nan': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3370): warning C4505: '__hmax2_nan': unreferenced function with internal linkage has been removed
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\include\cuda_fp16.hpp(3387): warning C4505: '__hmin2_nan': unreferenced function with internal linkage has been removed
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=haal
cargo:rustc-link-search=native=c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:warning=HAAL C++ library compiled successfully
cargo:rustc-link-search=native=c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out
cargo:rustc-link-lib=cudart
cargo:rustc-link-lib=cuda
cargo:warning=HAAL library linking configured
cargo:warning=Compiling Slint UI files
cargo:warning=Exported component 'CodeEditor' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:519:18
cargo:warning=    |
cargo:warning=519 | export component CodeEditor inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberButton' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:108:18
cargo:warning=    |
cargo:warning=108 | export component CyberButton inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberInputField' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:344:18
cargo:warning=    |
cargo:warning=344 | export component CyberInputField inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberPanel' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:201:18
cargo:warning=    |
cargo:warning=201 | export component CyberPanel inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberProgressBar' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:285:18
cargo:warning=    |
cargo:warning=285 | export component CyberProgressBar inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'FileTreeItem' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:440:18
cargo:warning=    |
cargo:warning=440 | export component FileTreeItem inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'StatusIndicator' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:387:18
cargo:warning=    |
cargo:warning=387 | export component StatusIndicator inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:82:22
cargo:warning=    |
cargo:warning=82  |     in-out property <easing> chrome-smooth: cubic-bezier(0.23, 1, 0.32, 1);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:79:22
cargo:warning=    |
cargo:warning=79  |     in-out property <easing> cyber-ease:    cubic-bezier(0.25, 0.1, 0.25, 1);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:81:22
cargo:warning=    |
cargo:warning=81  |     in-out property <easing> glitch-snap:   cubic-bezier(0.68, -0.55, 0.265, 1.55);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> c:\_Repos\OmniCodex\ui\omniforge.slint:80:22
cargo:warning=    |
cargo:warning=80  |     in-out property <easing> neon-pulse:    cubic-bezier(0.4, 0, 0.6, 1);
cargo:warning=    |                      ^
cargo:rerun-if-changed=c:\_Repos\OmniCodex\target\debug\build\SLINT_DEFAULT_STYLE.txt
cargo:rerun-if-changed=c:\_Repos\OmniCodex\ui/omniforge.slint
cargo:rerun-if-env-changed=SLINT_STYLE
cargo:rerun-if-env-changed=SLINT_FONT_SIZES
cargo:rerun-if-env-changed=SLINT_SCALE_FACTOR
cargo:rerun-if-env-changed=SLINT_ASSET_SECTION
cargo:rerun-if-env-changed=SLINT_EMBED_RESOURCES
cargo:rerun-if-env-changed=SLINT_EMIT_DEBUG_INFO
cargo:rustc-env=SLINT_INCLUDE_GENERATED=c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out\omniforge.rs
cargo:warning=Slint UI compilation completed successfully
cargo:rustc-env=HAAL_DEBUG=1
cargo:rustc-link-search=native=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\lib\x64
cargo:rustc-env=HAAL_CUDA_AVAILABLE=1
cargo:rustc-env=HAAL_AVX2_AVAILABLE=1
