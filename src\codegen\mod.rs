// src/codegen/mod.rs
//! Code generation for the OmniForge compiler.
//!
//! This module provides functionality for generating code for the OmniCodex
//! dispatch tables and wrapper functions.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use serde::{Serialize, Deserialize};

use crate::error::{OmniError, OmniResult};
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction, FunctionType};
use crate::ahaw::{self, AccelerationHint, TaskCharacteristics};


mod c_codegen;
mod cpp_codegen;
mod cu_codegen;
mod rs_codegen;
mod py_codegen;
mod ts_codegen;

pub use self::c_codegen::CCodeGenerator;
pub use self::cpp_codegen::CppCodeGenerator;
pub use self::cu_codegen::CudaCodeGenerator;
pub use self::rs_codegen::RustCodeGenerator;
pub use self::py_codegen::PythonCodeGenerator;
pub use self::ts_codegen::TypeScriptCodeGenerator;

/// Code generation options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodegenOptions {
    /// Target language
    pub target_language: String,
    
    /// Generate documentation comments
    pub generate_docs: bool,
    
    /// Generate type-safe wrappers
    pub generate_wrappers: bool,
    
    /// Generate error handling code
    pub generate_error_handling: bool,
    
    /// Include file paths in generated code
    pub include_file_paths: bool,
}

impl Default for CodegenOptions {
    fn default() -> Self {
        Self {
            target_language: "rust".to_string(),
            generate_docs: true,
            generate_wrappers: true,
            generate_error_handling: true,
            include_file_paths: true,
        }
    }
}

/// CodexEntry represents an entry in the OmniCodex dispatch table
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodexEntry {
    /// Function name
    pub name: String,
    
    /// Target type (CPU, GPU, etc.)
    pub target_type: TargetType,
    
    /// Function pointer
    pub function_pointer: String,
    
    /// Metadata for the function
    pub metadata: FunctionMetadata,
}

/// Target type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TargetType {
    /// CPU (default)
    CPU,
    
    /// GPU (CUDA)
    GPU,
    
    /// CPU with SIMD
    CPUSIMD,
    
    /// TPU
    TPU,
    
    /// FPGA
    FPGA,
    
    /// Other
    Other,
}

impl std::fmt::Display for TargetType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TargetType::CPU => write!(f, "CPU"),
            TargetType::GPU => write!(f, "GPU"),
            TargetType::CPUSIMD => write!(f, "CPUSIMD"),
            TargetType::TPU => write!(f, "TPU"),
            TargetType::FPGA => write!(f, "FPGA"),
            TargetType::Other => write!(f, "Other"),
        }
    }
}

/// Function metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionMetadata {
    /// Argument layout
    pub arg_layout: Vec<ArgType>,
    
    /// Return type
    pub return_type: ArgType,
    
    /// Grid size (for CUDA kernels)
    pub grid_size: Option<[u32; 3]>,
    
    /// Block size (for CUDA kernels)
    pub block_size: Option<[u32; 3]>,
    
    /// Shared memory size (for CUDA kernels)
    pub shared_memory: Option<usize>,
    
    /// Is the function a kernel
    pub is_kernel: bool,
}

/// Argument type
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ArgType {
    /// Void
    Void,
    
    /// 8-bit integer
    I8,
    
    /// 16-bit integer
    I16,
    
    /// 32-bit integer
    I32,
    
    /// 64-bit integer
    I64,
    
    /// 8-bit unsigned integer
    U8,
    
    /// 16-bit unsigned integer
    U16,
    
    /// 32-bit unsigned integer
    U32,
    
    /// 64-bit unsigned integer
    U64,
    
    /// 32-bit float
    F32,
    
    /// 64-bit float
    F64,
    
    /// Boolean
    Bool,
    
    /// Pointer to 8-bit integer
    I8Ptr,
    
    /// Pointer to 16-bit integer
    I16Ptr,
    
    /// Pointer to 32-bit integer
    I32Ptr,
    
    /// Pointer to 64-bit integer
    I64Ptr,
    
    /// Pointer to 8-bit unsigned integer
    U8Ptr,
    
    /// Pointer to 16-bit unsigned integer
    U16Ptr,
    
    /// Pointer to 32-bit unsigned integer
    U32Ptr,
    
    /// Pointer to 64-bit unsigned integer
    U64Ptr,
    
    /// Pointer to 32-bit float
    F32Ptr,
    
    /// Pointer to 64-bit float
    F64Ptr,
    
    /// Pointer to boolean
    BoolPtr,
    
    /// Pointer to void
    VoidPtr,
    
    /// Custom type
    Custom(String),
}

impl std::fmt::Display for ArgType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ArgType::Void => write!(f, "Void"),
            ArgType::I8 => write!(f, "I8"),
            ArgType::I16 => write!(f, "I16"),
            ArgType::I32 => write!(f, "I32"),
            ArgType::I64 => write!(f, "I64"),
            ArgType::U8 => write!(f, "U8"),
            ArgType::U16 => write!(f, "U16"),
            ArgType::U32 => write!(f, "U32"),
            ArgType::U64 => write!(f, "U64"),
            ArgType::F32 => write!(f, "F32"),
            ArgType::F64 => write!(f, "F64"),
            ArgType::Bool => write!(f, "Bool"),
            ArgType::I8Ptr => write!(f, "I8Ptr"),
            ArgType::I16Ptr => write!(f, "I16Ptr"),
            ArgType::I32Ptr => write!(f, "I32Ptr"),
            ArgType::I64Ptr => write!(f, "I64Ptr"),
            ArgType::U8Ptr => write!(f, "U8Ptr"),
            ArgType::U16Ptr => write!(f, "U16Ptr"),
            ArgType::U32Ptr => write!(f, "U32Ptr"),
            ArgType::U64Ptr => write!(f, "U64Ptr"),
            ArgType::F32Ptr => write!(f, "F32Ptr"),
            ArgType::F64Ptr => write!(f, "F64Ptr"),
            ArgType::BoolPtr => write!(f, "BoolPtr"),
            ArgType::VoidPtr => write!(f, "VoidPtr"),
            ArgType::Custom(name) => write!(f, "Custom({name})"),
        }
    }
}

/// Generated codex
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratedCodex {
    /// Table name
    pub table_name: String,
    
    /// Entries
    pub entries: Vec<CodexEntry>,
    
    /// Generated code
    pub code: String,
    
    /// Generated wrapper code
    pub wrapper_code: Option<String>,
    
    /// Header code (for C/C++)
    pub header_code: Option<String>,
}

/// Code generator trait
pub trait CodeGenerator {
    /// Generate OmniCodex dispatch table
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex>;
    
    /// Generate wrapper functions
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<String>;
}

/// Code generator factory
pub struct CodeGeneratorFactory;

impl CodeGeneratorFactory {
    /// Create a code generator for the specified language
    pub fn create_generator(language: &str) -> OmniResult<Box<dyn CodeGenerator>> {
        match language.to_lowercase().as_str() {
            "c" => Ok(Box::new(CCodeGenerator::new())),
            "cpp" | "c++" => Ok(Box::new(CppCodeGenerator::new())),
            "cuda" | "cu" => Ok(Box::new(CudaCodeGenerator::new())),
            "python" | "py" => Ok(Box::new(PythonCodeGenerator::new())),  
            "rust" | "rs" => Ok(Box::new(RustCodeGenerator::new())),
            "typescript" | "ts" => Ok(Box::new(TypeScriptCodeGenerator::new())),        
            _ => Err(OmniError::UnsupportedLanguage(language.to_string())),
        }
    }
}

/// Code generator
pub struct Codegen {
    /// Codegen options
    options: CodegenOptions,
}

impl Codegen {
    /// Create a new code generator
    pub fn new(options: CodegenOptions) -> Self {
        Self { options }
    }
    
    /// Generate OmniCodex dispatch table
    pub fn generate_codex(&self, metadata: &[ExtractedMetadata], output_path: &Path) -> OmniResult<()> {
        log::info!("Generating OmniCodex dispatch table to {}", output_path.display());
        
        // Create code generator for the target language
        let generator = CodeGeneratorFactory::create_generator(&self.options.target_language)?;
        
        // Generate codex
        let codex = generator.generate_codex(metadata, &self.options)?;
        
        // Write codex to file
        std::fs::write(output_path, &codex.code)?;
        
        // If wrappers are enabled and available, write them to a separate file
        if self.options.generate_wrappers && codex.wrapper_code.is_some() {
            let wrapper_path = output_path.with_file_name(format!("{}_wrappers.{}", 
                output_path.file_stem().unwrap().to_string_lossy(),
                output_path.extension().unwrap_or_default().to_string_lossy()));
            
            std::fs::write(&wrapper_path, codex.wrapper_code.unwrap())?;
            log::info!("Wrote wrapper code to {}", wrapper_path.display());
        }
        
        // If header code is available, write it to a separate file
        if let Some(header_code) = codex.header_code {
            let header_path = output_path.with_extension("h");
            std::fs::write(&header_path, header_code)?;
            log::info!("Wrote header code to {}", header_path.display());
        }
        
        log::info!("Generated OmniCodex dispatch table with {} entries", codex.entries.len());
        
        Ok(())
    }
    
    /// Map function type to target type
    pub fn map_function_type_to_target(function_type: FunctionType) -> TargetType {
        match function_type {
            FunctionType::Kernel => TargetType::GPU,
            FunctionType::Device => TargetType::GPU,
            FunctionType::Host => TargetType::CPU,
        }
    }
    
    /// Map extracted function to codex entry
    pub fn map_function_to_codex_entry(function: &ExtractedFunction, _binary_path: &str) -> OmniResult<CodexEntry> {
        // Determine target type
        let target_type = Self::map_function_type_to_target(function.function_type);
        
        // Generate function pointer name
        let function_pointer = match target_type {
            TargetType::GPU => format!("launch_{}", function.name),
            _ => function.name.clone(),
        };
        
        // Extract argument layout
        let arg_layout = if let Some(signature) = &function.signature {
            signature.parameter_types.iter()
                .map(|param| Self::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer))
                .collect()
        } else {
            Vec::new()
        };
        
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            Self::map_type_to_arg_type(&signature.return_type.name, signature.return_type.is_pointer)
        } else {
            ArgType::Void
        };
        
        // Extract grid and block size for CUDA kernels
        let (grid_size, block_size, shared_memory) = if let Some(launch_params) = &function.launch_params {
            (
                Some(launch_params.grid_dim),
                Some(launch_params.block_dim),
                Some(launch_params.shared_mem_bytes),
            )
        } else {
            (None, None, None)
        };
        
        Ok(CodexEntry {
            name: function.name.clone(),
            target_type,
            function_pointer,
            metadata: FunctionMetadata {
                arg_layout,
                return_type,
                grid_size,
                block_size,
                shared_memory,
                is_kernel: function.function_type == FunctionType::Kernel,
            },
        })
    }

    /// Accelerated template processing for code generation
    pub fn accelerated_template_processing(&self, template_data: &[u8]) -> OmniResult<Vec<u8>> {
        // Convert template data to f32 for acceleration
        let mut template_floats: Vec<f32> = template_data.iter().map(|&b| b as f32).collect();

        // Use acceleration for large templates
        if template_floats.len() > 2000 {
            match ahaw::codegen::accelerate_template_processing(&mut template_floats) {
                Ok(result) => {
                    println!("🚀 Accelerated template processing: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Template acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Convert back to bytes (simplified processing)
        let processed_data: Vec<u8> = template_floats.iter().map(|&f| f as u8).collect();
        Ok(processed_data)
    }

    /// Accelerated code synthesis with dynamic backend selection
    pub fn accelerated_code_synthesis(&self, code_vectors: &[f32], target: TargetType) -> OmniResult<String> {
        let mut data = code_vectors.to_vec();

        // Choose acceleration hint based on target type
        let hint = match target {
            TargetType::GPU => AccelerationHint::PreferGPU,
            TargetType::CPUSIMD => AccelerationHint::PreferCPU,
            _ => AccelerationHint::Auto,
        };

        // Create task characteristics based on workload
        let characteristics = TaskCharacteristics {
            data_size: data.len(),
            compute_intensity: match target {
                TargetType::GPU => 0.9,
                TargetType::CPUSIMD => 0.7,
                _ => 0.5,
            },
            parallelizability: 0.85,
            priority: "normal".to_string(),
            ..Default::default()
        };

        // Use acceleration for code synthesis
        if data.len() > 1000 {
            match ahaw::codegen::accelerate_template_processing_with_params(&mut data, ahaw::VectorOperation::FractalIteration, &hint, characteristics) {
                Ok(result) => {
                    println!("🚀 Accelerated code synthesis: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Code synthesis acceleration failed: {}", e);
                }
            }
        }

        // Generate code based on processed vectors (simplified)
        Ok(format!("// Generated code for {} target\n// Processed {} elements\n", target, data.len()))
    }

    /// Map type name to ArgType
    fn map_type_to_arg_type(type_name: &str, is_pointer: bool) -> ArgType {
        match (type_name, is_pointer) {
            ("void", false) => ArgType::Void,
            ("void", true) => ArgType::VoidPtr,
            (t, false) if t.contains("i8") || t.contains("s8") || t.contains("char") => ArgType::I8,
            (t, false) if t.contains("i16") || t.contains("s16") || t.contains("short") => ArgType::I16,
            (t, false) if t.contains("i32") || t.contains("s32") || t.contains("int") => ArgType::I32,
            (t, false) if t.contains("i64") || t.contains("s64") || t.contains("long") => ArgType::I64,
            (t, false) if t.contains("u8") || t.contains("uchar") => ArgType::U8,
            (t, false) if t.contains("u16") || t.contains("ushort") => ArgType::U16,
            (t, false) if t.contains("u32") || t.contains("uint") => ArgType::U32,
            (t, false) if t.contains("u64") || t.contains("ulong") => ArgType::U64,
            (t, false) if t.contains("f32") || t.contains("float") => ArgType::F32,
            (t, false) if t.contains("f64") || t.contains("double") => ArgType::F64,
            (t, false) if t.contains("bool") => ArgType::Bool,
            (t, true) if t.contains("i8") || t.contains("s8") || t.contains("char") => ArgType::I8Ptr,
            (t, true) if t.contains("i16") || t.contains("s16") || t.contains("short") => ArgType::I16Ptr,
            (t, true) if t.contains("i32") || t.contains("s32") || t.contains("int") => ArgType::I32Ptr,
            (t, true) if t.contains("i64") || t.contains("s64") || t.contains("long") => ArgType::I64Ptr,
            (t, true) if t.contains("u8") || t.contains("uchar") => ArgType::U8Ptr,
            (t, true) if t.contains("u16") || t.contains("ushort") => ArgType::U16Ptr,
            (t, true) if t.contains("u32") || t.contains("uint") => ArgType::U32Ptr,
            (t, true) if t.contains("u64") || t.contains("ulong") => ArgType::U64Ptr,
            (t, true) if t.contains("f32") || t.contains("float") => ArgType::F32Ptr,
            (t, true) if t.contains("f64") || t.contains("double") => ArgType::F64Ptr,
            (t, true) if t.contains("bool") => ArgType::BoolPtr,
            _ => ArgType::Custom(type_name.to_string()),
        }
    }
}

impl Default for Codegen {
    fn default() -> Self {
        Self::new(CodegenOptions::default())
    }
}
