#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>

const size_t N = 1024 * 1024;

void aggressive_sse_kernel(float* a, float* b, float* c, size_t n) {
    for (size_t i = 0; i < n; i += 32) {
        // 8x unrolled SSE operations
        __m128 va1 = _mm_load_ps(&a[i]);
        __m128 vb1 = _mm_load_ps(&b[i]);
        __m128 vc1 = _mm_load_ps(&c[i]);
        
        __m128 va2 = _mm_load_ps(&a[i + 4]);
        __m128 vb2 = _mm_load_ps(&b[i + 4]);
        __m128 vc2 = _mm_load_ps(&c[i + 4]);
        
        __m128 va3 = _mm_load_ps(&a[i + 8]);
        __m128 vb3 = _mm_load_ps(&b[i + 8]);
        __m128 vc3 = _mm_load_ps(&c[i + 8]);
        
        __m128 va4 = _mm_load_ps(&a[i + 12]);
        __m128 vb4 = _mm_load_ps(&b[i + 12]);
        __m128 vc4 = _mm_load_ps(&c[i + 12]);
        
        __m128 va5 = _mm_load_ps(&a[i + 16]);
        __m128 vb5 = _mm_load_ps(&b[i + 16]);
        __m128 vc5 = _mm_load_ps(&c[i + 16]);
        
        __m128 va6 = _mm_load_ps(&a[i + 20]);
        __m128 vb6 = _mm_load_ps(&b[i + 20]);
        __m128 vc6 = _mm_load_ps(&c[i + 20]);
        
        __m128 va7 = _mm_load_ps(&a[i + 24]);
        __m128 vb7 = _mm_load_ps(&b[i + 24]);
        __m128 vc7 = _mm_load_ps(&c[i + 24]);
        
        __m128 va8 = _mm_load_ps(&a[i + 28]);
        __m128 vb8 = _mm_load_ps(&b[i + 28]);
        __m128 vc8 = _mm_load_ps(&c[i + 28]);
        
        // Multiple computational chains
        for (int iter = 0; iter < 50; ++iter) {
            va1 = _mm_add_ps(_mm_mul_ps(va1, vb1), vc1);
            va2 = _mm_add_ps(_mm_mul_ps(va2, vb2), vc2);
            va3 = _mm_add_ps(_mm_mul_ps(va3, vb3), vc3);
            va4 = _mm_add_ps(_mm_mul_ps(va4, vb4), vc4);
            va5 = _mm_add_ps(_mm_mul_ps(va5, vb5), vc5);
            va6 = _mm_add_ps(_mm_mul_ps(va6, vb6), vc6);
            va7 = _mm_add_ps(_mm_mul_ps(va7, vb7), vc7);
            va8 = _mm_add_ps(_mm_mul_ps(va8, vb8), vc8);
        }
        
        _mm_store_ps(&a[i], va1);
        _mm_store_ps(&a[i + 4], va2);
        _mm_store_ps(&a[i + 8], va3);
        _mm_store_ps(&a[i + 12], va4);
        _mm_store_ps(&a[i + 16], va5);
        _mm_store_ps(&a[i + 20], va6);
        _mm_store_ps(&a[i + 24], va7);
        _mm_store_ps(&a[i + 28], va8);
    }
}

int main() {
    alignas(16) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f; 
        c[i] = i * 0.3f;
    }

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 2000; ++iter) {
        aggressive_sse_kernel(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    // 50 iterations * 2 ops per iteration = 100 FLOPs per element per outer iteration
    double ops = 2000.0 * N * 100.0;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "SSE 4.2 AGGRESSIVE: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
