c:\_Repos\OmniCodex\target\debug\deps\omni_forge-965f31d0ccedc949.d: src\lib.rs src\ahaw\mod.rs src\binary_analyzer\mod.rs src\binary_analyzer\pe_analyzer.rs src\binary_analyzer\elf_analyzer.rs src\binary_analyzer\macho_analyzer.rs src\binary_analyzer\ptx_analyzer.rs src\codegen\mod.rs src\codegen\c_codegen.rs src\codegen\cpp_codegen.rs src\codegen\cu_codegen.rs src\codegen\rs_codegen.rs src\codegen\py_codegen.rs src\codegen\ts_codegen.rs src\compiler\mod.rs src\config\mod.rs src\error\mod.rs src\gui\mod.rs src\metadata_extractor\mod.rs src\metadata_extractor\ptx_metadata.rs src\metadata_extractor\signature_validator.rs src\util\mod.rs src\util\file_utils.rs src\util\type_conversion.rs src\util\platform.rs src\util\logging.rs src\util\hash.rs src\models\mod.rs src\models\loader.rs src\models\gguf.rs src\models\safetensors.rs c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out\omniforge.rs

c:\_Repos\OmniCodex\target\debug\deps\libomni_forge-965f31d0ccedc949.rlib: src\lib.rs src\ahaw\mod.rs src\binary_analyzer\mod.rs src\binary_analyzer\pe_analyzer.rs src\binary_analyzer\elf_analyzer.rs src\binary_analyzer\macho_analyzer.rs src\binary_analyzer\ptx_analyzer.rs src\codegen\mod.rs src\codegen\c_codegen.rs src\codegen\cpp_codegen.rs src\codegen\cu_codegen.rs src\codegen\rs_codegen.rs src\codegen\py_codegen.rs src\codegen\ts_codegen.rs src\compiler\mod.rs src\config\mod.rs src\error\mod.rs src\gui\mod.rs src\metadata_extractor\mod.rs src\metadata_extractor\ptx_metadata.rs src\metadata_extractor\signature_validator.rs src\util\mod.rs src\util\file_utils.rs src\util\type_conversion.rs src\util\platform.rs src\util\logging.rs src\util\hash.rs src\models\mod.rs src\models\loader.rs src\models\gguf.rs src\models\safetensors.rs c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out\omniforge.rs

c:\_Repos\OmniCodex\target\debug\deps\libomni_forge-965f31d0ccedc949.rmeta: src\lib.rs src\ahaw\mod.rs src\binary_analyzer\mod.rs src\binary_analyzer\pe_analyzer.rs src\binary_analyzer\elf_analyzer.rs src\binary_analyzer\macho_analyzer.rs src\binary_analyzer\ptx_analyzer.rs src\codegen\mod.rs src\codegen\c_codegen.rs src\codegen\cpp_codegen.rs src\codegen\cu_codegen.rs src\codegen\rs_codegen.rs src\codegen\py_codegen.rs src\codegen\ts_codegen.rs src\compiler\mod.rs src\config\mod.rs src\error\mod.rs src\gui\mod.rs src\metadata_extractor\mod.rs src\metadata_extractor\ptx_metadata.rs src\metadata_extractor\signature_validator.rs src\util\mod.rs src\util\file_utils.rs src\util\type_conversion.rs src\util\platform.rs src\util\logging.rs src\util\hash.rs src\models\mod.rs src\models\loader.rs src\models\gguf.rs src\models\safetensors.rs c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out\omniforge.rs

src\lib.rs:
src\ahaw\mod.rs:
src\binary_analyzer\mod.rs:
src\binary_analyzer\pe_analyzer.rs:
src\binary_analyzer\elf_analyzer.rs:
src\binary_analyzer\macho_analyzer.rs:
src\binary_analyzer\ptx_analyzer.rs:
src\codegen\mod.rs:
src\codegen\c_codegen.rs:
src\codegen\cpp_codegen.rs:
src\codegen\cu_codegen.rs:
src\codegen\rs_codegen.rs:
src\codegen\py_codegen.rs:
src\codegen\ts_codegen.rs:
src\compiler\mod.rs:
src\config\mod.rs:
src\error\mod.rs:
src\gui\mod.rs:
src\metadata_extractor\mod.rs:
src\metadata_extractor\ptx_metadata.rs:
src\metadata_extractor\signature_validator.rs:
src\util\mod.rs:
src\util\file_utils.rs:
src\util\type_conversion.rs:
src\util\platform.rs:
src\util\logging.rs:
src\util\hash.rs:
src\models\mod.rs:
src\models\loader.rs:
src\models\gguf.rs:
src\models\safetensors.rs:
c:\_Repos\OmniCodex\target\debug\build\omni_forge-ffeb5028db28ed9f\out\omniforge.rs:

# env-dep:SLINT_INCLUDE_GENERATED=c:\\_Repos\\OmniCodex\\target\\debug\\build\\omni_forge-ffeb5028db28ed9f\\out\\omniforge.rs
