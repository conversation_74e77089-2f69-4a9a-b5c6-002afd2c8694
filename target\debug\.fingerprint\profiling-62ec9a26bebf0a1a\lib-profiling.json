{"rustc": 1842507548689473721, "features": "[\"default\", \"procmacros\", \"profiling-procmacros\"]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 2225463790103693989, "path": 9283203148220139356, "deps": [[1066480795608666094, "profiling_procmacros", false, 6421819392425485788]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\profiling-62ec9a26bebf0a1a\\dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}