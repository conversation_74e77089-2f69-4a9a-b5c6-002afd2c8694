// src/binary_analyzer/pe_analyzer.rs
//! PE binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing Windows PE (Portable Executable)
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::pe::{PE, export::Export, import::Import};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// PE binary analyzer
pub struct PEAnalyzer {
    // Configuration options can be added here
}

impl Default for PEAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl PEAnalyzer {
    /// Create a new PE analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a PE file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing PE file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the PE file
        let pe = PE::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse PE file: {e}")))?;
        
        // Extract exports
        let exports = self.extract_exports(&pe)?;
        
        // Extract imports
        let imports = self.extract_imports(&pe)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(&pe)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(&pe)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::PE,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the PE file
    fn extract_exports(&self, pe: &PE) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for export in &pe.exports {
            // Note: Forwarded export checking removed due to API changes

            exports.push(self.convert_export(export)?);
        }
        
        Ok(exports)
    }
    
    /// Convert a PE export to an ExportedFunction
    fn convert_export(&self, export: &Export) -> OmniResult<ExportedFunction> {
        // Try to determine calling convention based on name prefixes/suffixes
        let name_str = export.name.unwrap_or("unknown");
        let (name, calling_convention) = self.determine_calling_convention(name_str);
        
        Ok(ExportedFunction {
            name,
            address: export.rva as u64,
            signature: None, // Cannot determine signature from PE alone
            calling_convention: Some(calling_convention),
            metadata: serde_json::json!({
                "rva": export.rva,
            }),
        })
    }
    
    /// Determine calling convention based on function name
    fn determine_calling_convention(&self, name: &str) -> (String, CallingConvention) {
        let name = name.to_string();
        
        // Check for common calling convention indicators in function names
        if name.starts_with("_stdcall@") || name.contains("@") && name.matches("@").count() == 1 {
            // _stdcall functions often have @N suffix where N is the byte count of parameters
            return (name.clone(), CallingConvention::Stdcall);
        } else if name.starts_with("@fastcall@") {
            return (name.clone(), CallingConvention::Fastcall);
        } else if name.starts_with("_thiscall@") {
            return (name.clone(), CallingConvention::Thiscall);
        } else if name.starts_with("_vectorcall@") {
            return (name.clone(), CallingConvention::Vectorcall);
        }
        
        // Default to C calling convention
        (name, CallingConvention::C)
    }
    
    /// Extract imported functions from the PE file
    fn extract_imports(&self, pe: &PE) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for import in &pe.imports {
            imports.push(self.convert_import(import)?);
        }
        
        Ok(imports)
    }
    
    /// Convert a PE import to an ImportedFunction
    fn convert_import(&self, import: &Import) -> OmniResult<ImportedFunction> {
        Ok(ImportedFunction {
            name: import.name.to_string(),
            library: import.dll.to_string(),
            signature: None, // Cannot determine signature from PE alone
        })
    }
    
    /// Extract dependencies from the PE file
    fn extract_dependencies(&self, pe: &PE) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        // Get unique DLL names
        for import in &pe.imports {
            let dll_name = import.dll.to_string();
            if !dependencies.contains(&dll_name) {
                dependencies.push(dll_name);
            }
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the PE file
    fn extract_additional_metadata(&self, pe: &PE) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "machine": pe.header.coff_header.machine,
            "characteristics": pe.header.coff_header.characteristics,
            "is_dll": pe.is_lib,
            "is_executable": !pe.is_lib,
            "is_32_bit": !pe.is_64,
            "entry_point": pe.entry,
            "image_base": pe.image_base,
            "number_of_sections": pe.header.coff_header.number_of_sections,
        }))
    }
}
