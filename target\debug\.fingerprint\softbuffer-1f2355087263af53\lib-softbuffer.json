{"rustc": 1842507548689473721, "features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"fastrand\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 12734930653945463777, "deps": [[376837177317575824, "build_script_build", false, 5621506271792915505], [4143744114649553716, "raw_window_handle", false, 17974037465910822180], [5986029879202738730, "log", false, 5542591549586353644], [10281541584571964250, "windows_sys", false, 14902551932008253955]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-1f2355087263af53\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}