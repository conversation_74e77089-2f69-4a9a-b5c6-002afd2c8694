[package]
name = "omni_forge"
version = "0.1.0"
edition = "2021"
authors = ["ArcMoon Studios"]
description = "A revolutionary compiler framework for heterogeneous computing with zero-cost abstractions"
license = "MIT OR Apache-2.0"
repository = "https://github.com/arcmoonstudios/OmniForge"
readme = "README.md"
keywords = ["compiler", "heterogeneous", "zero-cost", "cuda", "meta-compiler"]
categories = ["compilers", "development-tools"]

[dependencies]
# Core functionality
clap = { version = "4.4", features = ["derive"] }       # Command line argument parsing
anyhow = "1.0"                                          # Error handling
thiserror = "2.0.12"                                    # Error definitions
log = "0.4"                                             # Logging
env_logger = "0.11.8"                                   # Logging implementation
serde = { version = "1.0", features = ["derive"] }      # Serialization
serde_json = "1.0.141"                                  # JSON serialization
rayon = "1.8"                                           # Parallel processing
indicatif = "0.18"                                      # Progress reporting
colored = "3.0"                                         # Colored terminal output
tokio = { version = "1.28", features = ["full"] }       # Asynchronous programming
slint = { version = "1.5", features = ["backend-qt", "renderer-femtovg"] } # GUI framework
chrono = { version = "0.4", features = ["serde"] }      # Date and time handling
atty = "0.2"                                            # Terminal detection
reqwest = { version = "0.12.22", features = ["json"] }  # HTTP client
dirs = "6.0"                                            # Directory utilities
uuid = { version = "1.0", features = ["v4"] }           # UUID generation

# FFI and Hardware Acceleration
libc = { version = "0.2", optional = true }             # C FFI bindings
bindgen = { version = "0.72", optional = true }         # C binding generation

# Binary analysis
goblin = "0.10"                                         # Binary parsing (ELF, PE, Mach-O)
object = "0.37.1"                                       # Object file parsing
memmap2 = "0.9.7"                                       # Memory mapping for efficient file access
pdb = "0.8"                                             # PDB debug information parsing

# Code generation
proc-macro2 = "1.0"                                     # Procedural macros
quote = "1.0"                                           # Rust code generation
syn = { version = "2.0", features = ["full"] }          # Rust syntax parsing

# CUDA/PTX specific
regex = "1.9"                                           # Regular expressions for PTX parsing

# Utility
md5 = "0.8.0"                                           # MD5 hash calculation
sha1 = "0.10"                                           # SHA-1 hash calculation
sha2 = "0.10"                                           # SHA-2 hash calculation
blake2 = "0.10"                                         # Blake2 hash calculation
blake3 = "1.5"                                          # Blake3 hash calculation
twox-hash = "2.1.1"                                     # xxHash calculation
rand = "0.9.1"                                          # Random number generation
num_cpus = "1.17.0"

# Machine Learning & AI Inference
ndarray = { version = "0.16.1", optional = true }       # N-dimensional arrays for tensor operations
once_cell = { version = "1.19", optional = true }       # Thread-safe lazy static initialization
safetensors = { version = "0.6", optional = true }      # SafeTensors format support
tch = { version = "0.18", optional = true }             # PyTorch bindings for Rust
 
[dev-dependencies]
tempfile = "3.8"                                        # Temporary files for tests
criterion = "0.5"                                       # Benchmarking

[build-dependencies]
cc = "1.2.30"                                           # Build script for C/C++ code
slint-build = "1.5"                                     # Slint UI compilation
bindgen = { version = "0.72", optional = true }         # C binding generation

[lib]
name = "omni_forge"
path = "src/lib.rs"

[[bin]]
name = "OmniForge"
path = "src/main.rs"

[features]
default = ["ml-inference", "hardware-acceleration"]
ml-inference = ["ndarray", "once_cell", "safetensors", "tch"]
hardware-acceleration = ["libc"]
bindings-generation = ["bindgen"]
cuda = []
