{"rustc": 1842507548689473721, "features": "[\"add\", \"add_assign\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"into\", \"mul\", \"not\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 5030970170741855347, "deps": [[3060637413840920116, "proc_macro2", false, 16584593718369059566], [4974441333307933176, "syn", false, 12478268728860239467], [16126285161989458480, "unicode_xid", false, 7688372910946092475], [17990358020177143287, "quote", false, 9129625013016073763]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-b183a4cc81e36764\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}