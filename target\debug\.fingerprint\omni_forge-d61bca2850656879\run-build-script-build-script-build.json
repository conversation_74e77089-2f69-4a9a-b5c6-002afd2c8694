{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6192306049807094004, "build_script_build", false, 10853478934062036673]], "local": [{"RerunIfChanged": {"output": "debug\\build\\omni_forge-d61bca2850656879\\output", "paths": ["build.rs", "haal/", "ui/", "src/gui/", "target\\debug\\build\\SLINT_DEFAULT_STYLE.txt", "ui/omniforge.slint"]}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_STYLE", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_FONT_SIZES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_SCALE_FACTOR", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_ASSET_SECTION", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMBED_RESOURCES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMIT_DEBUG_INFO", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}