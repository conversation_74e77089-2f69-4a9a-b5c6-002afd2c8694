#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>
#include <omp.h>
#include <thread>
#include <algorithm>

const size_t N = 4 * 1024 * 1024; // 16 MB allocation

// AGGRESSIVE KERNEL 1: Memory bandwidth crusher
void aggressive_memory_bandwidth_kernel(float* __restrict__ a, 
                                       const float* __restrict__ b, 
                                       const float* __restrict__ c, 
                                       size_t n) {
    #pragma omp parallel for schedule(static) num_threads(20)
    for (size_t i = 0; i < n; i += 32) {
        // Prefetch aggressively
        _mm_prefetch((char*)&a[i + 128], _MM_HINT_T0);
        _mm_prefetch((char*)&b[i + 128], _MM_HINT_T0);
        _mm_prefetch((char*)&c[i + 128], _MM_HINT_T0);
        
        // 4x unrolled AVX2 operations for maximum throughput
        __m256 va1 = _mm256_load_ps(&a[i]);
        __m256 vb1 = _mm256_load_ps(&b[i]);
        __m256 vc1 = _mm256_load_ps(&c[i]);
        
        __m256 va2 = _mm256_load_ps(&a[i + 8]);
        __m256 vb2 = _mm256_load_ps(&b[i + 8]);
        __m256 vc2 = _mm256_load_ps(&c[i + 8]);
        
        __m256 va3 = _mm256_load_ps(&a[i + 16]);
        __m256 vb3 = _mm256_load_ps(&b[i + 16]);
        __m256 vc3 = _mm256_load_ps(&c[i + 16]);
        
        __m256 va4 = _mm256_load_ps(&a[i + 24]);
        __m256 vb4 = _mm256_load_ps(&b[i + 24]);
        __m256 vc4 = _mm256_load_ps(&c[i + 24]);
        
        // Multiple FMA chains to saturate execution units
        __m256 result1 = _mm256_fmadd_ps(va1, vb1, vc1);
        __m256 result2 = _mm256_fmadd_ps(va2, vb2, vc2);
        __m256 result3 = _mm256_fmadd_ps(va3, vb3, vc3);
        __m256 result4 = _mm256_fmadd_ps(va4, vb4, vc4);
        
        // Additional operations to increase computational intensity
        result1 = _mm256_fmadd_ps(result1, va1, vb1);
        result2 = _mm256_fmadd_ps(result2, va2, vb2);
        result3 = _mm256_fmadd_ps(result3, va3, vb3);
        result4 = _mm256_fmadd_ps(result4, va4, vb4);
        
        result1 = _mm256_fmadd_ps(result1, vc1, va1);
        result2 = _mm256_fmadd_ps(result2, vc2, va2);
        result3 = _mm256_fmadd_ps(result3, vc3, va3);
        result4 = _mm256_fmadd_ps(result4, vc4, va4);
        
        _mm256_store_ps(&a[i], result1);
        _mm256_store_ps(&a[i + 8], result2);
        _mm256_store_ps(&a[i + 16], result3);
        _mm256_store_ps(&a[i + 24], result4);
    }
}

// AGGRESSIVE KERNEL 2: Register saturation beast
void aggressive_register_saturation(float* data, size_t n) {
    #pragma omp parallel for schedule(static) num_threads(20)
    for (size_t i = 0; i < n; i += 64) {
        // Use all 16 YMM registers for maximum parallelism
        __m256 r0 = _mm256_load_ps(&data[i]);
        __m256 r1 = _mm256_load_ps(&data[i + 8]);
        __m256 r2 = _mm256_load_ps(&data[i + 16]);
        __m256 r3 = _mm256_load_ps(&data[i + 24]);
        __m256 r4 = _mm256_load_ps(&data[i + 32]);
        __m256 r5 = _mm256_load_ps(&data[i + 40]);
        __m256 r6 = _mm256_load_ps(&data[i + 48]);
        __m256 r7 = _mm256_load_ps(&data[i + 56]);
        
        // Create 8 more registers from computations
        __m256 r8 = _mm256_mul_ps(r0, r1);
        __m256 r9 = _mm256_mul_ps(r2, r3);
        __m256 r10 = _mm256_mul_ps(r4, r5);
        __m256 r11 = _mm256_mul_ps(r6, r7);
        __m256 r12 = _mm256_add_ps(r0, r4);
        __m256 r13 = _mm256_add_ps(r1, r5);
        __m256 r14 = _mm256_add_ps(r2, r6);
        __m256 r15 = _mm256_add_ps(r3, r7);
        
        // Massive computation with all registers
        for (int iter = 0; iter < 100; ++iter) {
            r0 = _mm256_fmadd_ps(r0, r8, r12);
            r1 = _mm256_fmadd_ps(r1, r9, r13);
            r2 = _mm256_fmadd_ps(r2, r10, r14);
            r3 = _mm256_fmadd_ps(r3, r11, r15);
            r4 = _mm256_fmadd_ps(r4, r12, r8);
            r5 = _mm256_fmadd_ps(r5, r13, r9);
            r6 = _mm256_fmadd_ps(r6, r14, r10);
            r7 = _mm256_fmadd_ps(r7, r15, r11);
            
            r8 = _mm256_fmadd_ps(r8, r0, r4);
            r9 = _mm256_fmadd_ps(r9, r1, r5);
            r10 = _mm256_fmadd_ps(r10, r2, r6);
            r11 = _mm256_fmadd_ps(r11, r3, r7);
            r12 = _mm256_fmadd_ps(r12, r4, r0);
            r13 = _mm256_fmadd_ps(r13, r5, r1);
            r14 = _mm256_fmadd_ps(r14, r6, r2);
            r15 = _mm256_fmadd_ps(r15, r7, r3);
        }
        
        // Store results
        _mm256_store_ps(&data[i], _mm256_add_ps(r0, r8));
        _mm256_store_ps(&data[i + 8], _mm256_add_ps(r1, r9));
        _mm256_store_ps(&data[i + 16], _mm256_add_ps(r2, r10));
        _mm256_store_ps(&data[i + 24], _mm256_add_ps(r3, r11));
        _mm256_store_ps(&data[i + 32], _mm256_add_ps(r4, r12));
        _mm256_store_ps(&data[i + 40], _mm256_add_ps(r5, r13));
        _mm256_store_ps(&data[i + 48], _mm256_add_ps(r6, r14));
        _mm256_store_ps(&data[i + 56], _mm256_add_ps(r7, r15));
    }
}

int main() {
    alignas(32) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f;
        c[i] = i * 0.3f;
    }

    std::cout << "Intel OneAPI Ultra-Optimized (16MB + OpenMP + Aggressive Kernels)" << std::endl;
    std::cout << "Using " << omp_get_max_threads() << " threads" << std::endl;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        aggressive_memory_bandwidth_kernel(a.data(), b.data(), c.data(), N);
        aggressive_register_saturation(a.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    // Aggressive FLOP counting: 
    // Memory bandwidth kernel: 6 FMA ops per element per iteration = 12 FLOPs
    // Register saturation: 16 FMA ops * 100 iterations = 3200 FLOPs per element per iteration
    double ops = 1000.0 * N * (12.0 + 3200.0);
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "Intel OneAPI AGGRESSIVE AVX2: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
