{"rustc": 1842507548689473721, "features": "[\"archive\", \"coff\", \"compression\", \"default\", \"elf\", \"macho\", \"pe\", \"read\", \"read_core\", \"std\", \"unaligned\", \"xcoff\"]", "declared_features": "[\"all\", \"alloc\", \"archive\", \"build\", \"build_core\", \"cargo-all\", \"coff\", \"compression\", \"core\", \"default\", \"doc\", \"elf\", \"macho\", \"pe\", \"read\", \"read_core\", \"rustc-dep-of-std\", \"std\", \"unaligned\", \"unstable\", \"unstable-all\", \"wasm\", \"write\", \"write_core\", \"write_std\", \"xcoff\"]", "target": 5743048264439000431, "profile": 15657897354478470176, "path": 9938854818288484454, "deps": [[3215782898819282321, "build_script_build", false, 9855125352720847359], [15667726689128706326, "ruzstd", false, 3682766974440102363], [15932120279885307830, "memchr", false, 13101643477351540418], [17772299992546037086, "flate2", false, 13809356338170060949]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\object-99d929d954fd53e2\\dep-lib-object", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}