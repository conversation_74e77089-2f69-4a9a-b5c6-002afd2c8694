{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2225463790103693989, "path": 93625086084127041, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\weezl-51c13af9eeab456a\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}