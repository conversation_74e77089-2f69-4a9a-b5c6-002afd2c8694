{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 15657897354478470176, "path": 16115957479182443466, "deps": [[1988483478007900009, "unicode_ident", false, 728640981034292054], [3060637413840920116, "proc_macro2", false, 15371664435471233259], [17990358020177143287, "quote", false, 15243228526347011181]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-f956054714f42e55\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}