// src/util/file_utils.rs
//! File utility functions for the OmniForge compiler.
//!
//! This module provides common file operations used throughout the OmniCodex framework,
//! with robust error handling and path manipulation capabilities.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::{self, File, OpenOptions};
use std::io::{self, Read, Write, BufReader, BufWriter};


/// Ensure that a directory exists, creating it if necessary
///
/// # Arguments
///
/// * `path` - The directory path to ensure exists
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::ensure_directory_exists;
/// use std::path::Path;
///
/// let dir_path = Path::new("output/data");
/// ensure_directory_exists(dir_path).expect("Failed to create directory");
/// ```
pub fn ensure_directory_exists<P: AsRef<Path>>(path: P) -> io::Result<()> {
    let path = path.as_ref();
    
    if !path.exists() {
        fs::create_dir_all(path)?;
    } else if !path.is_dir() {
        return Err(io::Error::new(
            io::ErrorKind::AlreadyExists,
            format!("Path exists but is not a directory: {}", path.display()),
        ));
    }
    
    Ok(())
}

/// Ensure that the parent directory of a file exists, creating it if necessary
///
/// # Arguments
///
/// * `path` - The file path whose parent directory should exist
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::ensure_parent_directory_exists;
/// use std::path::Path;
///
/// let file_path = Path::new("output/data/results.txt");
/// ensure_parent_directory_exists(file_path).expect("Failed to create parent directory");
/// ```
pub fn ensure_parent_directory_exists<P: AsRef<Path>>(path: P) -> io::Result<()> {
    let path = path.as_ref();
    
    if let Some(parent) = path.parent() {
        ensure_directory_exists(parent)?;
    }
    
    Ok(())
}

/// Read a file into a string
///
/// # Arguments
///
/// * `path` - The path to the file to read
///
/// # Returns
///
/// * `io::Result<String>` - The contents of the file as a string
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::read_file_to_string;
/// use std::path::Path;
///
/// let file_path = Path::new("input/data.txt");
/// let contents = read_file_to_string(file_path).expect("Failed to read file");
/// println!("File contents: {}", contents);
/// ```
pub fn read_file_to_string<P: AsRef<Path>>(path: P) -> io::Result<String> {
    let file = File::open(path)?;
    let mut reader = BufReader::new(file);
    let mut contents = String::new();
    reader.read_to_string(&mut contents)?;
    Ok(contents)
}

/// Write a string to a file
///
/// # Arguments
///
/// * `path` - The path to the file to write
/// * `contents` - The string to write to the file
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::write_string_to_file;
/// use std::path::Path;
///
/// let file_path = Path::new("output/data.txt");
/// let contents = "Hello, world!";
/// write_string_to_file(file_path, contents).expect("Failed to write file");
/// ```
pub fn write_string_to_file<P: AsRef<Path>, C: AsRef<str>>(path: P, contents: C) -> io::Result<()> {
    ensure_parent_directory_exists(&path)?;
    
    let file = OpenOptions::new()
        .write(true)
        .create(true)
        .truncate(true)
        .open(path)?;
    
    let mut writer = BufWriter::new(file);
    writer.write_all(contents.as_ref().as_bytes())?;
    writer.flush()?;
    
    Ok(())
}








