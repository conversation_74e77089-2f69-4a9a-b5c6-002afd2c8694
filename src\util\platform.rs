// src/util/platform.rs
//! Platform detection utilities for the OmniForge compiler.
//!
//! This module provides functionality for detecting and identifying the
//! current operating system, architecture, and platform-specific features,
//! enabling platform-aware compilation and code generation.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::env;
use std::process::Command;

/// Operating system types
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum OperatingSystem {
    /// Windows operating system
    Windows,
    /// Linux operating system
    Linux,
    /// macOS operating system
    MacOS,
    /// BSD operating system
    BSD,
    /// Android operating system
    Android,
    /// iOS operating system
    IOS,
    /// Other/unknown operating system
    Other,
}

impl OperatingSystem {
    /// Get the string representation of the operating system
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Windows => "windows",
            Self::Linux => "linux",
            Self::MacOS => "macos",
            Self::BSD => "bsd",
            Self::Android => "android",
            Self::IOS => "ios",
            Self::Other => "other",
        }
    }
    
    /// Check if the operating system is Windows
    pub fn is_windows(&self) -> bool {
        matches!(self, Self::Windows)
    }
    
    /// Check if the operating system is Linux
    pub fn is_linux(&self) -> bool {
        matches!(self, Self::Linux)
    }
    
    /// Check if the operating system is macOS
    pub fn is_macos(&self) -> bool {
        matches!(self, Self::MacOS)
    }
    
    /// Check if the operating system is Unix-like
    pub fn is_unix_like(&self) -> bool {
        matches!(self, Self::Linux | Self::MacOS | Self::BSD | Self::Android | Self::IOS)
    }
}

/// Processor architecture types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Architecture {
    /// x86 (32-bit) architecture
    X86,
    /// x86_64 (64-bit) architecture
    X86_64,
    /// ARM (32-bit) architecture
    ARM,
    /// ARM64 (64-bit) architecture
    ARM64,
    /// MIPS architecture
    MIPS,
    /// PowerPC architecture
    PowerPC,
    /// RISC-V architecture
    RISCV,
    /// WebAssembly
    WASM,
    /// Other/unknown architecture
    Other,
}

impl Architecture {
    /// Get the string representation of the architecture
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::X86 => "x86",
            Self::X86_64 => "x86_64",
            Self::ARM => "arm",
            Self::ARM64 => "aarch64",
            Self::MIPS => "mips",
            Self::PowerPC => "powerpc",
            Self::RISCV => "riscv",
            Self::WASM => "wasm",
            Self::Other => "other",
        }
    }
    
    /// Check if the architecture is 64-bit
    pub fn is_64bit(&self) -> bool {
        matches!(self, Self::X86_64 | Self::ARM64)
    }
    
    /// Check if the architecture is x86 family
    pub fn is_x86_family(&self) -> bool {
        matches!(self, Self::X86 | Self::X86_64)
    }
    
    /// Check if the architecture is ARM family
    pub fn is_arm_family(&self) -> bool {
        matches!(self, Self::ARM | Self::ARM64)
    }
}

/// Platform information combining operating system and architecture
#[derive(Debug, Clone)]
pub struct Platform {
    /// Operating system
    pub os: OperatingSystem,
    /// Architecture
    pub arch: Architecture,
    /// Operating system version
    pub os_version: Option<String>,
    /// Additional platform features
    pub features: Vec<String>,
}

impl Platform {
    /// Check if the platform supports CUDA
    pub fn supports_cuda(&self) -> bool {
        // CUDA is supported on Windows, Linux, and macOS (up to certain versions)
        // And only on x86_64 and ARM64 architectures
        match (self.os, self.arch) {
            (OperatingSystem::Windows | OperatingSystem::Linux, Architecture::X86_64) => true,
            (OperatingSystem::MacOS, Architecture::X86_64) => {
                // macOS dropped official CUDA support after 10.13 (High Sierra)
                if let Some(ref version) = self.os_version {
                    // Simple version check (not entirely accurate for all cases)
                    version.starts_with("10.") && version.split('.').nth(1).is_some_and(|minor| {
                        minor.parse::<u32>().is_ok_and(|m| m <= 13)
                    })
                } else {
                    false
                }
            }
            (OperatingSystem::Linux, Architecture::ARM64) => true, // NVIDIA Jetson
            _ => false,
        }
    }
    
    /// Check if the platform supports OpenCL
    pub fn supports_opencl(&self) -> bool {
        // OpenCL is more widely supported than CUDA
        match self.os {
            OperatingSystem::Windows | OperatingSystem::Linux | OperatingSystem::MacOS => true,
            _ => false,
        }
    }
    
    /// Check if the platform supports AVX instructions
    pub fn supports_avx(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx")
    }
    
    /// Check if the platform supports AVX2 instructions
    pub fn supports_avx2(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx2")
    }
    
    /// Check if the platform supports AVX-512 instructions
    pub fn supports_avx512(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx512f")
    }
    
    /// Check if the platform supports NEON instructions
    pub fn supports_neon(&self) -> bool {
        self.arch.is_arm_family() && self.features.iter().any(|f| f == "neon")
    }
    
    /// Get the standard library name prefix for the platform
    pub fn lib_prefix(&self) -> &'static str {
        if self.os.is_windows() {
            ""
        } else {
            "lib"
        }
    }
    
    /// Get the standard library extension for the platform
    pub fn lib_extension(&self) -> &'static str {
        match self.os {
            OperatingSystem::Windows => "dll",
            OperatingSystem::MacOS => "dylib",
            OperatingSystem::Linux | OperatingSystem::BSD | OperatingSystem::Android => "so",
            _ => "so", // Default to .so for unknown platforms
        }
    }
    
    /// Get the executable extension for the platform
    pub fn exe_extension(&self) -> &'static str {
        if self.os.is_windows() {
            "exe"
        } else {
            ""
        }
    }
    
    /// Get the standard environment variable separator for the platform
    pub fn path_separator(&self) -> &'static str {
        if self.os.is_windows() {
            ";"
        } else {
            ":"
        }
    }
    
    /// Get the standard path separator for the platform
    pub fn dir_separator(&self) -> &'static str {
        if self.os.is_windows() {
            "\\"
        } else {
            "/"
        }
    }
}

/// Detect the current platform
pub fn detect_platform() -> Platform {
    // Detect operating system
    let os = if cfg!(windows) {
        OperatingSystem::Windows
    } else if cfg!(target_os = "macos") {
        OperatingSystem::MacOS
    } else if cfg!(target_os = "ios") {
        OperatingSystem::IOS
    } else if cfg!(target_os = "android") {
        OperatingSystem::Android
    } else if cfg!(target_os = "linux") {
        OperatingSystem::Linux
    } else if cfg!(target_family = "unix") {
        // Could be BSD or other Unix-like
        if cfg!(target_os = "freebsd") || cfg!(target_os = "openbsd") || cfg!(target_os = "netbsd") {
            OperatingSystem::BSD
        } else {
            OperatingSystem::Other
        }
    } else {
        OperatingSystem::Other
    };
    
    // Detect architecture
    let arch = if cfg!(target_arch = "x86") {
        Architecture::X86
    } else if cfg!(target_arch = "x86_64") {
        Architecture::X86_64
    } else if cfg!(target_arch = "arm") {
        Architecture::ARM
    } else if cfg!(target_arch = "aarch64") {
        Architecture::ARM64
    } else if cfg!(target_arch = "mips") {
        Architecture::MIPS
    } else if cfg!(target_arch = "powerpc") {
        Architecture::PowerPC
    } else if cfg!(any(target_arch = "riscv32", target_arch = "riscv64")) {
        Architecture::RISCV
    } else if cfg!(target_arch = "wasm32") {
        Architecture::WASM
    } else {
        Architecture::Other
    };
    
    // Detect OS version
    let os_version = detect_os_version(&os);
    
    // Detect CPU features
    let features = detect_cpu_features(&arch);
    
    Platform {
        os,
        arch,
        os_version,
        features,
    }
}

/// Detect the operating system version
fn detect_os_version(os: &OperatingSystem) -> Option<String> {
    match os {
        OperatingSystem::Windows => {
            // Use PowerShell to get Windows version
            let output = Command::new("powershell")
                .args(["-Command", "[System.Environment]::OSVersion.Version.ToString()"])
                .output()
                .ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                // Fallback to env var if PowerShell fails
                env::var("WINVER").ok()
            }
        }
        OperatingSystem::MacOS => {
            // Use sw_vers to get macOS version
            let output = Command::new("sw_vers")
                .arg("-productVersion")
                .output()
                .ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        OperatingSystem::Linux => {
            // Try to get Linux distribution version
            if let Ok(output) = Command::new("lsb_release").arg("-r").arg("-s").output() {
                if output.status.success() {
                    let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                    return Some(version);
                }
            }
            
            // Fallback to kernel version
            let output = Command::new("uname").arg("-r").output().ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        OperatingSystem::BSD => {
            // Use uname for BSD version
            let output = Command::new("uname").arg("-r").output().ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        _ => None,
    }
}

/// Detect CPU features
fn detect_cpu_features(arch: &Architecture) -> Vec<String> {
    let mut features = Vec::new();
    
    match arch {
        Architecture::X86 | Architecture::X86_64 => {
            // Use is_x86_feature_detected on x86 platforms
            #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
            {
                if std::is_x86_feature_detected!("sse") {
                    features.push("sse".to_string());
                }
                if std::is_x86_feature_detected!("sse2") {
                    features.push("sse2".to_string());
                }
                if std::is_x86_feature_detected!("sse3") {
                    features.push("sse3".to_string());
                }
                if std::is_x86_feature_detected!("ssse3") {
                    features.push("ssse3".to_string());
                }
                if std::is_x86_feature_detected!("sse4.1") {
                    features.push("sse4.1".to_string());
                }
                if std::is_x86_feature_detected!("sse4.2") {
                    features.push("sse4.2".to_string());
                }
                if std::is_x86_feature_detected!("avx") {
                    features.push("avx".to_string());
                }
                if std::is_x86_feature_detected!("avx2") {
                    features.push("avx2".to_string());
                }
                if std::is_x86_feature_detected!("avx512f") {
                    features.push("avx512f".to_string());
                }
                if std::is_x86_feature_detected!("fma") {
                    features.push("fma".to_string());
                }
                if std::is_x86_feature_detected!("bmi1") {
                    features.push("bmi1".to_string());
                }
                if std::is_x86_feature_detected!("bmi2") {
                    features.push("bmi2".to_string());
                }
                if std::is_x86_feature_detected!("popcnt") {
                    features.push("popcnt".to_string());
                }
                if std::is_x86_feature_detected!("lzcnt") {
                    features.push("lzcnt".to_string());
                }
                if std::is_x86_feature_detected!("aes") {
                    features.push("aes".to_string());
                }
            }
            
            // Fallback when compile-time detection is not available
            #[cfg(not(any(target_arch = "x86", target_arch = "x86_64")))]
            {
                // Parse /proc/cpuinfo on Linux
                if let Ok(cpuinfo) = std::fs::read_to_string("/proc/cpuinfo") {
                    let mut cpu_flags = String::new();
                    
                    for line in cpuinfo.lines() {
                        if line.starts_with("flags") || line.starts_with("Features") {
                            cpu_flags = line.split(':').nth(1).unwrap_or("").trim().to_string();
                            break;
                        }
                    }
                    
                    for flag in cpu_flags.split_whitespace() {
                        features.push(flag.to_string());
                    }
                }
            }
        }
        Architecture::ARM | Architecture::ARM64 => {
            // ARM feature detection
            #[cfg(any(target_arch = "arm", target_arch = "aarch64"))]
            {
                // Check for NEON support on ARM
                #[cfg(target_arch = "arm")]
                if std::arch::is_arm_feature_detected!("neon") {
                    features.push("neon".to_string());
                }
                
                // NEON is mandatory on AArch64
                #[cfg(target_arch = "aarch64")]
                features.push("neon".to_string());
                
                // Check for additional ARM features
                #[cfg(target_arch = "aarch64")]
                {
                    if std::arch::is_aarch64_feature_detected!("sve") {
                        features.push("sve".to_string());
                    }
                    if std::arch::is_aarch64_feature_detected!("crc") {
                        features.push("crc".to_string());
                    }
                    if std::arch::is_aarch64_feature_detected!("lse") {
                        features.push("lse".to_string());
                    }
                }
            }
            
            // Fallback when compile-time detection is not available
            #[cfg(not(any(target_arch = "arm", target_arch = "aarch64")))]
            {
                if *arch == Architecture::ARM64 {
                    // NEON is mandatory on AArch64
                    features.push("neon".to_string());
                }
                
                // Parse /proc/cpuinfo on Linux
                if let Ok(cpuinfo) = std::fs::read_to_string("/proc/cpuinfo") {
                    let mut cpu_features = String::new();
                    
                    for line in cpuinfo.lines() {
                        if line.starts_with("Features") {
                            cpu_features = line.split(':').nth(1).unwrap_or("").trim().to_string();
                            break;
                        }
                    }
                    
                    for feature in cpu_features.split_whitespace() {
                        features.push(feature.to_string());
                    }
                }
            }
        }
        _ => {
            // Other architectures not yet supported for feature detection
        }
    }
    
    features
}
