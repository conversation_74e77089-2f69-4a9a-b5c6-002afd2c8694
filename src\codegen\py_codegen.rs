// src/codegen/py_codegen.rs
//! Python code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating Python code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of heterogeneous
//! computing capabilities into Python applications, with support for NumPy, CuPy,
//! and CFFI for interfacing with native code.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen, ArgType, TargetType};

/// Python code generator
pub struct PythonCodeGenerator {
    /// Use NumPy for array operations
    use_numpy: bool,
    
    /// Use CuPy for GPU array operations
    use_cupy: bool,
    
    /// Use CFFI for interfacing with C code
    use_cffi: bool,
    
    /// Use type hints (Python 3.6+)
    use_type_hints: bool,
    
    /// Target Python version
    target_version: String,
    
    /// Include debug logging
    include_debug_logging: bool,
}

#[allow(dead_code)]
impl PythonCodeGenerator {
    /// Create a new Python code generator with default settings
    pub fn new() -> Self {
        Self {
            use_numpy: true,
            use_cupy: true,
            use_cffi: true,
            use_type_hints: true,
            target_version: "3.9".to_string(),
            include_debug_logging: true,
        }
    }
    
    /// Create a new Python code generator with specific configuration
    pub fn with_config(
        use_numpy: bool,
        use_cupy: bool,
        use_cffi: bool,
        use_type_hints: bool,
        target_version: String,
        include_debug_logging: bool,
    ) -> Self {
        Self {
            use_numpy,
            use_cupy,
            use_cffi,
            use_type_hints,
            target_version,
            include_debug_logging,
        }
    }
    
    /// Generate Python type from argument type
    fn generate_py_type(&self, arg_type: &ArgType) -> String {
        if !self.use_type_hints {
            return String::new();
        }
        
        match arg_type {
            ArgType::Void => "None".to_string(),
            ArgType::I8 => "int".to_string(),
            ArgType::I16 => "int".to_string(),
            ArgType::I32 => "int".to_string(),
            ArgType::I64 => "int".to_string(),
            ArgType::U8 => "int".to_string(),
            ArgType::U16 => "int".to_string(),
            ArgType::U32 => "int".to_string(),
            ArgType::U64 => "int".to_string(),
            ArgType::F32 => "float".to_string(),
            ArgType::F64 => "float".to_string(),
            ArgType::Bool => "bool".to_string(),
            ArgType::I8Ptr => "np.ndarray".to_string(),
            ArgType::I16Ptr => "np.ndarray".to_string(),
            ArgType::I32Ptr => "np.ndarray".to_string(),
            ArgType::I64Ptr => "np.ndarray".to_string(),
            ArgType::U8Ptr => "np.ndarray".to_string(),
            ArgType::U16Ptr => "np.ndarray".to_string(),
            ArgType::U32Ptr => "np.ndarray".to_string(),
            ArgType::U64Ptr => "np.ndarray".to_string(),
            ArgType::F32Ptr => "np.ndarray".to_string(),
            ArgType::F64Ptr => "np.ndarray".to_string(),
            ArgType::BoolPtr => "np.ndarray".to_string(),
            ArgType::VoidPtr => "ctypes.c_void_p".to_string(),
            ArgType::Custom(name) => format!("Any # {name}"),
        }
    }
    
    /// Generate Python function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if self.use_type_hints {
            if let Some(signature) = &function.signature {
                let py_type = self.generate_py_type(&Codegen::map_type_to_arg_type(
                    &signature.return_type.name,
                    signature.return_type.is_pointer,
                ));
                
                format!(" -> {py_type}")
            } else {
                " -> None".to_string()
            }
        } else {
            String::new()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                String::new()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        if self.use_type_hints {
                            let py_type = self.generate_py_type(&arg_type);
                            format!("arg{i}: {py_type}")
                        } else {
                            format!("arg{i}")
                        }
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            String::new()
        };
        
        Ok(format!("def {}({}){}", function.name, params, return_type))
    }
    
    /// Generate OmniCodex module
    fn generate_module(&self, entries: &[CodexEntry]) -> OmniResult<String> {
        let mut result = String::new();
        
        // Generate header with comments
        result.push_str(&format!(
            r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex Python Integration

Generated by OmniForge - The OmniCodex Compiler Framework

This module provides Python interfaces and implementations for
heterogeneous computing with the OmniCodex framework.

Target Python Version: {target_version}
NumPy: {numpy}
CuPy: {cupy}
CFFI: {cffi}
Type Hints: {type_hints}
"""

from __future__ import annotations
import os
import sys
import enum
import logging
import ctypes
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, TypeVar, Generic, cast
{numpy_import}
{cupy_import}
{cffi_import}

# Configure logging
{logging_config}

{enums}
"#,
            target_version = self.target_version,
            numpy = if self.use_numpy { "Enabled" } else { "Disabled" },
            cupy = if self.use_cupy { "Enabled" } else { "Disabled" },
            cffi = if self.use_cffi { "Enabled" } else { "Disabled" },
            type_hints = if self.use_type_hints { "Enabled" } else { "Disabled" },
            numpy_import = if self.use_numpy { 
                "import numpy as np" 
            } else { 
                "# NumPy integration disabled" 
            },
            cupy_import = if self.use_cupy { 
                "try:\n    import cupy as cp\nexcept ImportError:\n    logger.warning(\"CuPy not found. GPU operations will not be available.\")\n    cp = None" 
            } else { 
                "# CuPy integration disabled" 
            },
            cffi_import = if self.use_cffi { 
                "try:\n    import cffi\n    _ffi = cffi.FFI()\nexcept ImportError:\n    logger.warning(\"CFFI not found. Native code integration will be limited.\")\n    _ffi = None" 
            } else { 
                "# CFFI integration disabled" 
            },
            logging_config = if self.include_debug_logging {
                r#"logger = logging.getLogger("omnicodex")
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)"#
            } else {
                r#"logger = logging.getLogger("omnicodex")
logger.addHandler(logging.NullHandler())"#
            },
            enums = self.generate_enums(),
        ));
        
        // Generate core classes
        result.push_str(r#"
class ComputeMetadata:
    """Function metadata for compute operations"""
    
    def __init__(
        self, 
        grid_size: Tuple[int, int, int],
        block_size: Tuple[int, int, int],
        shared_mem: int,
        args_layout: List[ArgType]
    ):
        self.grid_size = grid_size
        self.block_size = block_size
        self.shared_mem = shared_mem
        self.args_layout = args_layout
    
    @classmethod
    def cpu(cls) -> 'ComputeMetadata':
        """Create CPU metadata"""
        return cls(
            grid_size=(1, 1, 1),
            block_size=(1, 1, 1),
            shared_mem=0,
            args_layout=[]
        )
    
    @classmethod
    def cpu_simd(cls) -> 'ComputeMetadata':
        """Create CPU SIMD metadata"""
        return cls(
            grid_size=(1, 1, 1),
            block_size=(1, 1, 1),
            shared_mem=0,
            args_layout=[]
        )
    
    @classmethod
    def gpu(
        cls,
        grid_size: Tuple[int, int, int],
        block_size: Tuple[int, int, int],
        shared_mem: int,
        args_layout: List[ArgType]
    ) -> 'ComputeMetadata':
        """Create GPU metadata"""
        return cls(
            grid_size=grid_size,
            block_size=block_size,
            shared_mem=shared_mem,
            args_layout=args_layout
        )
    
    def __repr__(self) -> str:
        return (
            f"ComputeMetadata(grid_size={self.grid_size}, "
            f"block_size={self.block_size}, "
            f"shared_mem={self.shared_mem}, "
            f"args_layout={self.args_layout})"
        )


class OmniCodexEntry:
    """Entry in the OmniCodex dispatch table"""
    
    def __init__(
        self,
        name: str,
        target: OmniTarget,
        impl: Callable,
        metadata: ComputeMetadata
    ):
        self.name = name
        self.target = target
        self.impl = impl
        self.metadata = metadata
    
    def __repr__(self) -> str:
        return f"OmniCodexEntry(name='{self.name}', target={self.target})"


class OmniError(Exception):
    """OmniCodex error"""
    
    def __init__(self, code: OmniErrorCode, message: str):
        self.code = code
        self.message = message
        super().__init__(f"{code.name}: {message}")


# Type variable for generic return types
T = TypeVar('T')

"#);
        
        // Generate OmniCodex dispatch table
        result.push_str("# OmniCodex dispatch table\nOMNI_CODEX = [\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("({}, {}, {})", grid[0], grid[1], grid[2]),
                    format!("({}, {}, {})", block[0], block[1], block[2]),
                )
            } else {
                ("(1, 1, 1)".to_string(), "(256, 1, 1)".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = entry
                .metadata
                .arg_layout
                .iter()
                .map(|arg| format!("ArgType.{arg}"))
                .collect::<Vec<_>>()
                .join(", ");
            
            // Generate target type
            let target_type = match entry.target_type {
                TargetType::CPU => "OmniTarget.CPU",
                TargetType::GPU => "OmniTarget.GPU",
                TargetType::CPUSIMD => "OmniTarget.CPUSIMD",
                TargetType::TPU => "OmniTarget.TPU",
                TargetType::FPGA => "OmniTarget.FPGA",
                TargetType::Other => "OmniTarget.OTHER",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    # {name} - {target_desc}
    OmniCodexEntry(
        name='{name}',
        target={target},
        impl={impl_name},
        metadata=ComputeMetadata(
            grid_size={grid_size},
            block_size={block_size},
            shared_mem={shared_mem},
            args_layout=[{args_layout}]
        )
    ),
"#,
                name = entry.name,
                target_desc = entry.target_type,
                target = target_type,
                impl_name = format!("{}_{}", entry.target_type.to_string().to_lowercase(), entry.name),
                grid_size = grid_size,
                block_size = block_size,
                shared_mem = shared_mem,
                args_layout = args_layout,
            ));
        }
        
        result.push_str("]\n\n");
        
        // Generate platform-specific implementations
        result.push_str("# CPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::CPU) {
            result.push_str(&format!(
                r#"def cpu_{name}({args}){return_type}:
    """CPU implementation of {name}"""
    raise OmniError(OmniErrorCode.NOT_IMPLEMENTED, "CPU implementation not provided")

"#,
                name = entry.name,
                args = if entry.metadata.arg_layout.is_empty() {
                    String::new()
                } else {
                    (0..entry.metadata.arg_layout.len())
                        .map(|i| format!("arg{i}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                },
                return_type = if self.use_type_hints {
                    format!(" -> {}", self.generate_py_type(&entry.metadata.return_type))
                } else {
                    String::new()
                },
            ));
        }
        
        result.push_str("# GPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::GPU) {
            result.push_str(&format!(
                r#"def gpu_{name}({args}){return_type}:
    """GPU implementation of {name}"""
    if cp is None:
        raise OmniError(OmniErrorCode.GPU_NOT_AVAILABLE, "CuPy not available")
    raise OmniError(OmniErrorCode.NOT_IMPLEMENTED, "GPU implementation not provided")

"#,
                name = entry.name,
                args = if entry.metadata.arg_layout.is_empty() {
                    String::new()
                } else {
                    (0..entry.metadata.arg_layout.len())
                        .map(|i| format!("arg{i}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                },
                return_type = if self.use_type_hints {
                    format!(" -> {}", self.generate_py_type(&entry.metadata.return_type))
                } else {
                    String::new()
                },
            ));
        }
        
        // Generate utility functions
        result.push_str(r#"
def find_function(name: str) -> Optional[OmniCodexEntry]:
    """
    Find a function in the OmniCodex dispatch table
    
    Args:
        name: Function name
        
    Returns:
        OmniCodexEntry or None if not found
    """
    for entry in OMNI_CODEX:
        if entry.name == name:
            return entry
    return None


def execute(name: str, *args: Any) -> Any:
    """
    Execute a function by name
    
    Args:
        name: Function name
        args: Function arguments
        
    Returns:
        Function result
        
    Raises:
        OmniError: If an error occurs during execution
    """
    # Find the function in the dispatch table
    entry = find_function(name)
    if entry is None:
        raise OmniError(
            OmniErrorCode.FUNCTION_NOT_FOUND,
            f"Function not found: {name}"
        )
    
    # Check argument count
    if len(args) != len(entry.metadata.args_layout):
        raise OmniError(
            OmniErrorCode.ARGUMENT_COUNT_MISMATCH,
            f"Argument count mismatch: expected {len(entry.metadata.args_layout)}, got {len(args)}"
        )
    
    # Execute function
    try:
        logger.debug(f"Executing {name} with {len(args)} arguments")
        return entry.impl(*args)
    except Exception as e:
        logger.error(f"Error executing {name}: {e}")
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Runtime error: {str(e)}"
        ) from e


class OmniCodex:
    """OmniCodex API class"""
    
    def __init__(self):
        self._entries = {entry.name: entry for entry in OMNI_CODEX}
    
    def __getattr__(self, name: str) -> Callable:
        """
        Get a function by name
        
        Args:
            name: Function name
            
        Returns:
            Function wrapper
            
        Raises:
            AttributeError: If function not found
        """
        if name in self._entries:
            def wrapper(*args: Any) -> Any:
                return execute(name, *args)
            return wrapper
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def available_functions(self) -> List[str]:
        """
        Get list of available functions
        
        Returns:
            List of function names
        """
        return list(self._entries.keys())
    
    def function_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a function
        
        Args:
            name: Function name
            
        Returns:
            Function information or None if not found
        """
        entry = self._entries.get(name)
        if entry is None:
            return None
        
        return {
            "name": entry.name,
            "target": entry.target.name,
            "grid_size": entry.metadata.grid_size,
            "block_size": entry.metadata.block_size,
            "shared_mem": entry.metadata.shared_mem,
            "args_layout": [arg.name for arg in entry.metadata.args_layout],
        }


# Create API instance
api = OmniCodex()


def is_gpu_available() -> bool:
    """
    Check if GPU is available
    
    Returns:
        True if GPU is available
    """
    return cp is not None


def is_cffi_available() -> bool:
    """
    Check if CFFI is available
    
    Returns:
        True if CFFI is available
    """
    return _ffi is not None


# Module exports
__all__ = [
    'OmniTarget',
    'ArgType',
    'OmniErrorCode',
    'OmniError',
    'ComputeMetadata',
    'OmniCodexEntry',
    'OMNI_CODEX',
    'find_function',
    'execute',
    'api',
    'is_gpu_available',
    'is_cffi_available',
]
"#);
        
        Ok(result)
    }
    
    /// Generate Python enums
    fn generate_enums(&self) -> String {
        r#"class OmniTarget(enum.Enum):
    """Target platform for computation"""
    CPU = "CPU"           # Central Processing Unit
    GPU = "GPU"           # Graphics Processing Unit
    CPUSIMD = "CPUSIMD"   # CPU with SIMD instructions
    TPU = "TPU"           # Tensor Processing Unit
    FPGA = "FPGA"         # Field-Programmable Gate Array
    OTHER = "OTHER"       # Other compute device


class ArgType(enum.Enum):
    """Argument type enumeration"""
    Void = "Void"         # Void type
    I8 = "I8"             # 8-bit signed integer
    I16 = "I16"           # 16-bit signed integer
    I32 = "I32"           # 32-bit signed integer
    I64 = "I64"           # 64-bit signed integer
    U8 = "U8"             # 8-bit unsigned integer
    U16 = "U16"           # 16-bit unsigned integer
    U32 = "U32"           # 32-bit unsigned integer
    U64 = "U64"           # 64-bit unsigned integer
    F32 = "F32"           # 32-bit floating point
    F64 = "F64"           # 64-bit floating point
    Bool = "Bool"         # Boolean
    I8Ptr = "I8Ptr"       # Pointer to 8-bit signed integer
    I16Ptr = "I16Ptr"     # Pointer to 16-bit signed integer
    I32Ptr = "I32Ptr"     # Pointer to 32-bit signed integer
    I64Ptr = "I64Ptr"     # Pointer to 64-bit signed integer
    U8Ptr = "U8Ptr"       # Pointer to 8-bit unsigned integer
    U16Ptr = "U16Ptr"     # Pointer to 16-bit unsigned integer
    U32Ptr = "U32Ptr"     # Pointer to 32-bit unsigned integer
    U64Ptr = "U64Ptr"     # Pointer to 64-bit unsigned integer
    F32Ptr = "F32Ptr"     # Pointer to 32-bit floating point
    F64Ptr = "F64Ptr"     # Pointer to 64-bit floating point
    BoolPtr = "BoolPtr"   # Pointer to boolean
    VoidPtr = "VoidPtr"   # Pointer to void


class OmniErrorCode(enum.Enum):
    """Error codes for OmniCodex operations"""
    NONE = "NONE"                           # No error
    FUNCTION_NOT_FOUND = "FUNCTION_NOT_FOUND"  # Function not found in OmniCodex
    ARGUMENT_COUNT_MISMATCH = "ARGUMENT_COUNT_MISMATCH"  # Argument count mismatch
    ARGUMENT_TYPE_MISMATCH = "ARGUMENT_TYPE_MISMATCH"  # Argument type mismatch
    NOT_IMPLEMENTED = "NOT_IMPLEMENTED"     # Not implemented
    GPU_NOT_AVAILABLE = "GPU_NOT_AVAILABLE"  # GPU not available
    CFFI_NOT_AVAILABLE = "CFFI_NOT_AVAILABLE"  # CFFI not available
    RUNTIME_ERROR = "RUNTIME_ERROR"         # Runtime error
"#.to_string()
    }
    
    /// Generate CFFI integration
    fn generate_cffi_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate CFFI integration if enabled
        if !self.use_cffi {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex CFFI Integration

This module provides CFFI integration for the OmniCodex framework.
"""

import os
import sys
import logging
import ctypes
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import cffi
    _ffi = cffi.FFI()
except ImportError:
    raise ImportError("CFFI not found. Please install it with 'pip install cffi'")

from .omnicodex import OmniError, OmniErrorCode, logger

# CFFI library handles
_lib_handles = {}


def load_library(lib_name: str, lib_path: Optional[str] = None) -> Any:
    """
    Load a native library using CFFI
    
    Args:
        lib_name: Library name
        lib_path: Library path (optional)
        
    Returns:
        CFFI library handle
        
    Raises:
        OmniError: If library cannot be loaded
    """
    if lib_name in _lib_handles:
        return _lib_handles[lib_name]
    
    try:
        if lib_path:
            lib = _ffi.dlopen(lib_path)
        else:
            lib = _ffi.dlopen(lib_name)
        
        _lib_handles[lib_name] = lib
        return lib
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to load library {lib_name}: {str(e)}"
        ) from e


def unload_library(lib_name: str) -> None:
    """
    Unload a native library
    
    Args:
        lib_name: Library name
    """
    if lib_name in _lib_handles:
        # CFFI doesn't have an explicit unload mechanism, but we can
        # remove the reference to allow garbage collection
        del _lib_handles[lib_name]


def define_function(lib_name: str, func_name: str, signature: str) -> Callable:
    """
    Define a function in a library
    
    Args:
        lib_name: Library name
        func_name: Function name
        signature: Function signature
        
    Returns:
        Function wrapper
        
    Raises:
        OmniError: If function cannot be defined
    """
    lib = _lib_handles.get(lib_name)
    if lib is None:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Library {lib_name} not loaded"
        )
    
    try:
        _ffi.cdef(f"{signature};")
        func = getattr(lib, func_name)
        
        def wrapper(*args: Any) -> Any:
            try:
                return func(*args)
            except Exception as e:
                raise OmniError(
                    OmniErrorCode.RUNTIME_ERROR,
                    f"Error calling {func_name}: {str(e)}"
                ) from e
        
        return wrapper
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to define function {func_name}: {str(e)}"
        ) from e


def array_to_ptr(array: Any) -> Any:
    """
    Convert a Python array to a pointer
    
    Args:
        array: Python array (numpy.ndarray, list, etc.)
        
    Returns:
        CFFI pointer
        
    Raises:
        OmniError: If array cannot be converted
    """
    import numpy as np
    
    try:
        if isinstance(array, np.ndarray):
            # NumPy array
            return _ffi.cast("void*", array.ctypes.data)
        elif isinstance(array, (list, tuple)):
            # Convert list/tuple to NumPy array
            arr = np.array(array)
            return _ffi.cast("void*", arr.ctypes.data)
        else:
            raise TypeError(f"Unsupported array type: {type(array)}")
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to pointer: {str(e)}"
        ) from e


def ptr_to_array(ptr: Any, shape: Tuple[int, ...], dtype: Any) -> Any:
    """
    Convert a pointer to a NumPy array
    
    Args:
        ptr: CFFI pointer
        shape: Array shape
        dtype: NumPy dtype
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If pointer cannot be converted
    """
    import numpy as np
    
    try:
        # Calculate total size
        size = 1
        for dim in shape:
            size *= dim
        
        # Get pointer address
        addr = int(_ffi.cast("uintptr_t", ptr))
        
        # Create NumPy array from memory
        arr = np.ndarray(
            shape=shape,
            dtype=dtype,
            buffer=_ffi.buffer(ptr, size * np.dtype(dtype).itemsize)
        )
        
        # Return a copy to avoid memory issues
        return arr.copy()
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert pointer to array: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'load_library',
    'unload_library',
    'define_function',
    'array_to_ptr',
    'ptr_to_array',
]
"#);
        
        Ok(result)
    }
    
    /// Generate NumPy integration
    fn generate_numpy_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate NumPy integration if enabled
        if !self.use_numpy {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex NumPy Integration

This module provides NumPy integration for the OmniCodex framework.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import numpy as np
except ImportError:
    raise ImportError("NumPy not found. Please install it with 'pip install numpy'")

from .omnicodex import OmniError, OmniErrorCode, ArgType, logger

# Type mapping from ArgType to NumPy dtype
TYPE_MAPPING = {
    ArgType.I8: np.int8,
    ArgType.I16: np.int16,
    ArgType.I32: np.int32,
    ArgType.I64: np.int64,
    ArgType.U8: np.uint8,
    ArgType.U16: np.uint16,
    ArgType.U32: np.uint32,
    ArgType.U64: np.uint64,
    ArgType.F32: np.float32,
    ArgType.F64: np.float64,
    ArgType.Bool: np.bool_,
    ArgType.I8Ptr: np.int8,
    ArgType.I16Ptr: np.int16,
    ArgType.I32Ptr: np.int32,
    ArgType.I64Ptr: np.int64,
    ArgType.U8Ptr: np.uint8,
    ArgType.U16Ptr: np.uint16,
    ArgType.U32Ptr: np.uint32,
    ArgType.U64Ptr: np.uint64,
    ArgType.F32Ptr: np.float32,
    ArgType.F64Ptr: np.float64,
    ArgType.BoolPtr: np.bool_,
}


def arg_type_to_dtype(arg_type: ArgType) -> np.dtype:
    """
    Convert ArgType to NumPy dtype
    
    Args:
        arg_type: ArgType
        
    Returns:
        NumPy dtype
        
    Raises:
        OmniError: If arg_type cannot be converted
    """
    if arg_type in TYPE_MAPPING:
        return TYPE_MAPPING[arg_type]
    
    raise OmniError(
        OmniErrorCode.ARGUMENT_TYPE_MISMATCH,
        f"Cannot convert {arg_type} to NumPy dtype"
    )


def ensure_array(arg: Any, arg_type: ArgType) -> np.ndarray:
    """
    Ensure that an argument is a NumPy array of the correct type
    
    Args:
        arg: Argument
        arg_type: Expected ArgType
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If arg cannot be converted
    """
    try:
        dtype = arg_type_to_dtype(arg_type)
        
        if isinstance(arg, np.ndarray):
            # Already a NumPy array, just convert dtype if needed
            if arg.dtype != dtype:
                return arg.astype(dtype)
            return arg
        
        # Convert to NumPy array
        return np.array(arg, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.ARGUMENT_TYPE_MISMATCH,
            f"Failed to convert argument to NumPy array: {str(e)}"
        ) from e


def create_array(shape: Tuple[int, ...], arg_type: ArgType) -> np.ndarray:
    """
    Create a new NumPy array
    
    Args:
        shape: Array shape
        arg_type: ArgType
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If array cannot be created
    """
    try:
        dtype = arg_type_to_dtype(arg_type)
        return np.zeros(shape, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to create NumPy array: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'TYPE_MAPPING',
    'arg_type_to_dtype',
    'ensure_array',
    'create_array',
]
"#);
        
        Ok(result)
    }
    
    /// Generate CuPy integration
    fn generate_cupy_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate CuPy integration if enabled
        if !self.use_cupy {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex CuPy Integration

This module provides CuPy integration for the OmniCodex framework.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import numpy as np
    import cupy as cp
except ImportError:
    raise ImportError("CuPy not found. Please install it with 'pip install cupy'")

from .omnicodex import OmniError, OmniErrorCode, ArgType, logger
from .numpy_utils import arg_type_to_dtype

# Check if CuPy is available and CUDA is installed
try:
    _cuda_available = cp.cuda.is_available()
except Exception:
    _cuda_available = False

# Cached CUDA kernels
_kernel_cache = {}


def is_cuda_available() -> bool:
    """
    Check if CUDA is available
    
    Returns:
        True if CUDA is available
    """
    return _cuda_available


def to_gpu(arr: Any) -> cp.ndarray:
    """
    Convert an array to a CuPy array
    
    Args:
        arr: NumPy array or compatible
        
    Returns:
        CuPy array
        
    Raises:
        OmniError: If arr cannot be converted
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        if isinstance(arr, cp.ndarray):
            return arr
        return cp.asarray(arr)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to GPU: {str(e)}"
        ) from e


def to_cpu(arr: cp.ndarray) -> np.ndarray:
    """
    Convert a CuPy array to a NumPy array
    
    Args:
        arr: CuPy array
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If arr cannot be converted
    """
    try:
        if isinstance(arr, np.ndarray):
            return arr
        return arr.get()
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to CPU: {str(e)}"
        ) from e


def create_gpu_array(shape: Tuple[int, ...], arg_type: ArgType) -> cp.ndarray:
    """
    Create a new CuPy array
    
    Args:
        shape: Array shape
        arg_type: ArgType
        
    Returns:
        CuPy array
        
    Raises:
        OmniError: If array cannot be created
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        dtype = arg_type_to_dtype(arg_type)
        return cp.zeros(shape, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to create GPU array: {str(e)}"
        ) from e


def compile_kernel(kernel_code: str, kernel_name: str) -> Callable:
    """
    Compile a CUDA kernel
    
    Args:
        kernel_code: CUDA kernel code
        kernel_name: Kernel name
        
    Returns:
        Compiled kernel
        
    Raises:
        OmniError: If kernel cannot be compiled
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    # Check cache
    cache_key = f"{kernel_name}_{hash(kernel_code)}"
    if cache_key in _kernel_cache:
        return _kernel_cache[cache_key]
    
    try:
        # Compile kernel
        module = cp.RawModule(code=kernel_code)
        kernel = module.get_function(kernel_name)
        
        # Cache kernel
        _kernel_cache[cache_key] = kernel
        
        return kernel
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to compile CUDA kernel: {str(e)}"
        ) from e


def launch_kernel(
    kernel: Callable,
    grid: Tuple[int, int, int],
    block: Tuple[int, int, int],
    args: List[Any],
    shared_mem: int = 0
) -> None:
    """
    Launch a CUDA kernel
    
    Args:
        kernel: Compiled kernel
        grid: Grid dimensions
        block: Block dimensions
        args: Kernel arguments
        shared_mem: Shared memory size
        
    Raises:
        OmniError: If kernel cannot be launched
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        # Convert grid and block to tuples
        grid_tuple = (int(grid[0]), int(grid[1]), int(grid[2]))
        block_tuple = (int(block[0]), int(block[1]), int(block[2]))
        
        # Launch kernel
        kernel(grid_tuple, block_tuple, args, shared_mem=shared_mem)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to launch CUDA kernel: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'is_cuda_available',
    'to_gpu',
    'to_cpu',
    'create_gpu_array',
    'compile_kernel',
    'launch_kernel',
]
"#);
        
        Ok(result)
    }
}

impl CodeGenerator for PythonCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating Python OmniCodex");
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate main module
        let main_module = self.generate_module(&entries)?;
        
        // Generate additional modules
        let mut additional_modules = Vec::new();
        
        // Generate CFFI integration if enabled
        if self.use_cffi {
            let cffi_module = self.generate_cffi_integration(&entries)?;
            if !cffi_module.is_empty() {
                additional_modules.push(("cffi_utils.py", cffi_module));
            }
        }
        
        // Generate NumPy integration if enabled
        if self.use_numpy {
            let numpy_module = self.generate_numpy_integration(&entries)?;
            if !numpy_module.is_empty() {
                additional_modules.push(("numpy_utils.py", numpy_module));
            }
        }
        
        // Generate CuPy integration if enabled
        if self.use_cupy {
            let cupy_module = self.generate_cupy_integration(&entries)?;
            if !cupy_module.is_empty() {
                additional_modules.push(("cupy_utils.py", cupy_module));
            }
        }
        
        // Generate wrapper code
        let mut wrapper_code = String::new();
        
        for (filename, content) in additional_modules {
            wrapper_code.push_str(&format!("# File: {filename}\n\n"));
            wrapper_code.push_str(&content);
            wrapper_code.push_str("\n\n");
        }
        
        // Generate __init__.py
        wrapper_code.push_str("# File: __init__.py\n\n");
        wrapper_code.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex Python Integration

Generated by OmniForge - The OmniCodex Compiler Framework
"""

from .omnicodex import (
    OmniTarget,
    ArgType,
    OmniErrorCode,
    OmniError,
    ComputeMetadata,
    OmniCodexEntry,
    OMNI_CODEX,
    find_function,
    execute,
    api,
    is_gpu_available,
    is_cffi_available,
)

# Version
__version__ = '0.1.0'
"#);
        
        if self.use_numpy {
            wrapper_code.push_str("\n# NumPy integration\nfrom .numpy_utils import (\n    arg_type_to_dtype,\n    ensure_array,\n    create_array,\n)\n");
        }
        
        if self.use_cupy {
            wrapper_code.push_str("\n# CuPy integration\ntry:\n    from .cupy_utils import (\n        is_cuda_available,\n        to_gpu,\n        to_cpu,\n        create_gpu_array,\n        compile_kernel,\n        launch_kernel,\n    )\nexcept ImportError:\n    pass\n");
        }
        
        if self.use_cffi {
            wrapper_code.push_str("\n# CFFI integration\ntry:\n    from .cffi_utils import (\n        load_library,\n        unload_library,\n        define_function,\n        array_to_ptr,\n        ptr_to_array,\n    )\nexcept ImportError:\n    pass\n");
        }
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code: main_module,
            wrapper_code: Some(wrapper_code),
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, _metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating Python wrappers");
        
        // This is handled in the main codex generation
        Ok(String::new())
    }
}

impl Default for PythonCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}
