{"rustc": 1842507548689473721, "features": "[\"default\", \"hardware-acceleration\", \"libc\", \"ml-inference\", \"ndarray\", \"once_cell\", \"safetensors\"]", "declared_features": "[\"bindgen\", \"bindings-generation\", \"default\", \"hardware-acceleration\", \"libc\", \"ml-inference\", \"ndarray\", \"once_cell\", \"safetensors\"]", "target": 2059014308450143550, "profile": 17672942494452627365, "path": 14199415984607037515, "deps": [[720731745621185994, "criterion", false, 16736150993461229235], [1441306149310335789, "tempfile", false, 12275533538183153947], [2098583196738611028, "rand", false, 5659456336574174734], [2133066668466761424, "safetensors", false, 8882792878251111643], [2357570525450087091, "num_cpus", false, 6570877565145768542], [3060637413840920116, "proc_macro2", false, 17962853542639788514], [3124552836158245642, "indicatif", false, 10080493086272013694], [3215782898819282321, "object", false, 4420303453573079129], [3722963349756955755, "once_cell", false, 4683668061861388514], [4684437522915235464, "libc", false, 6979579291704086421], [4931842561887251499, "slint", false, 903433027228684215], [4974441333307933176, "syn", false, 6308461413805097534], [5986029879202738730, "log", false, 6163580429061228050], [6192306049807094004, "omni_forge", false, 7768494376865208082], [6192306049807094004, "build_script_build", false, 5822544868433039804], [6898646762435821041, "env_logger", false, 16626497328716912588], [7085222851776090619, "reqwest", false, 2808804817354056307], [8319709847752024821, "uuid", false, 2519552892266979369], [8569119365930580996, "serde_json", false, 17768060534915365149], [8700459469608572718, "blake2", false, 12168404662782421359], [8931583763680323206, "twox_hash", false, 8230496192457279773], [9114801806035203750, "colored", false, 16999896328119527840], [9241925498456048256, "blake3", false, 10312135322893217547], [9451456094439810778, "regex", false, 11510618457941491834], [9689903380558560274, "serde", false, 11415862898145335498], [9857275760291862238, "sha2", false, 3688084614867537183], [9897246384292347999, "chrono", false, 6394693748915964637], [10058577953979766589, "atty", false, 10506266066322632199], [10697383615564341592, "rayon", false, 13464913170832741689], [10724389056617919257, "sha1", false, 14265914792076350936], [10806645703491011684, "thiserror", false, 17975458214952839659], [12393800526703971956, "tokio", false, 12364510645567468726], [13268876162597347677, "memmap2", false, 8127077252144668605], [13608731599072018412, "goblin", false, 6548485057345896459], [13625485746686963219, "anyhow", false, 17676458166178008493], [16684705727134695305, "pdb", false, 16576185591948933367], [16928111194414003569, "dirs", false, 10862370467666629703], [17433017841942338824, "clap", false, 3505487870885049858], [17548070434322971711, "md5", false, 17680682609107072768], [17990358020177143287, "quote", false, 15906026899850567383], [18259966568667970611, "n<PERSON><PERSON>", false, 14700044974097703494]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\omni_forge-f813a18cb1b95c02\\dep-example-ml_inference_demo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}