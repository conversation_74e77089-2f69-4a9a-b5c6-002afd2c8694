# 🔷 Intel OneAPI Permanent Environment Setup Script
# ================================================
# This script permanently adds Intel OneAPI to your Windows environment

Write-Host "🔷 Intel OneAPI Permanent Environment Setup" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

# Define Intel OneAPI paths
$ONEAPI_ROOT = "C:\Program Files (x86)\Intel\oneAPI"
$COMPILER_PATH = "$ONEAPI_ROOT\compiler\latest\bin"
$MKL_PATH = "$ONEAPI_ROOT\mkl\latest\bin"
$TBB_PATH = "$ONEAPI_ROOT\tbb\latest\bin"
$DEBUGGER_PATH = "$ONEAPI_ROOT\debugger\latest\bin"

# Check if Intel OneAPI is installed
if (-not (Test-Path $ONEAPI_ROOT)) {
    Write-Host "❌ Intel OneAPI not found at $ONEAPI_ROOT" -ForegroundColor Red
    Write-Host "Please install Intel OneAPI Base Toolkit first." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Intel OneAPI found at $ONEAPI_ROOT" -ForegroundColor Green

# Get current user PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)

# Paths to add
$pathsToAdd = @($COMPILER_PATH, $MKL_PATH, $TBB_PATH, $DEBUGGER_PATH)

Write-Host "`n🔧 Adding Intel OneAPI paths to user PATH..." -ForegroundColor Yellow

foreach ($path in $pathsToAdd) {
    if (Test-Path $path) {
        if ($currentPath -notlike "*$path*") {
            $currentPath += ";$path"
            Write-Host "  ✅ Added: $path" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  Already exists: $path" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ Path not found: $path" -ForegroundColor Red
    }
}

# Set the updated PATH
[Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)

# Set Intel OneAPI environment variables
Write-Host "`n🔧 Setting Intel OneAPI environment variables..." -ForegroundColor Yellow

$envVars = @{
    "ONEAPI_ROOT" = $ONEAPI_ROOT
    "INTEL_COMPILER_ROOT" = "$ONEAPI_ROOT\compiler\latest"
    "MKLROOT" = "$ONEAPI_ROOT\mkl\latest"
    "TBBROOT" = "$ONEAPI_ROOT\tbb\latest"
}

foreach ($var in $envVars.GetEnumerator()) {
    [Environment]::SetEnvironmentVariable($var.Key, $var.Value, [EnvironmentVariableTarget]::User)
    Write-Host "  ✅ Set $($var.Key) = $($var.Value)" -ForegroundColor Green
}

Write-Host "`n🎯 VERIFICATION:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

# Test if icpx is now available (in current session)
$env:PATH += ";$COMPILER_PATH"
try {
    $version = & icpx --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Intel OneAPI C++ Compiler (icpx) is working!" -ForegroundColor Green
        Write-Host "   Version: $($version[0])" -ForegroundColor Gray
    } else {
        Write-Host "WARNING: icpx test failed, but paths are set for next session" -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNING: icpx test failed, but paths are set for next session" -ForegroundColor Yellow
}

Write-Host "`n🚀 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "1. Close and reopen your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Test Intel OneAPI: icpx --version" -ForegroundColor White
Write-Host "3. Compile with Intel OneAPI:" -ForegroundColor White
Write-Host "   icpx your-code.cpp -o output.exe -O3 -xHost -ipo" -ForegroundColor Gray

Write-Host "`n🔷 Intel OneAPI environment setup complete!" -ForegroundColor Cyan
Write-Host "Environment variables will be available in new terminal sessions." -ForegroundColor Gray
