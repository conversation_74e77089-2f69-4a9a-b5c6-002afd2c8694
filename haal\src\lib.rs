// src/lib.rs
//! # HAAL - Hybrid AVX2-CUDA Acceleration Layer
//!
//! HAAL provides high-performance compute acceleration through seamless integration
//! of Intel AVX2 SIMD instructions and NVIDIA CUDA GPU compute.

// Removed unused imports

/// Rust-safe wrapper around AVX2Operation enum
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AVX2Operation {
    VectorAdd,
    VectorMul,
    VectorDot,
    VectorNorm,
    MatrixMul,
    ConvolutionOp,
    FractalIteration,
    QuantumEvolution,
    SimilarityCompute,
    FourierTransform,
}

impl From<AVX2Operation> for u32 {
    fn from(op: AVX2Operation) -> Self {
        match op {
            AVX2Operation::VectorAdd => 0,
            AVX2Operation::VectorMul => 1,
            AVX2Operation::VectorDot => 2,
            AVX2Operation::VectorNorm => 3,
            AVX2Operation::MatrixMul => 4,
            AVX2Operation::ConvolutionOp => 5,
            AVX2Operation::FractalIteration => 6,
            AVX2Operation::QuantumEvolution => 7,
            AVX2Operation::SimilarityCompute => 8,
            AVX2Operation::FourierTransform => 9,
        }
    }
}

/// Task characteristics for intelligent scheduling
#[derive(Debug, Clone)]
pub struct TaskCharacteristics {
    pub data_size: usize,
    pub compute_intensity: f64,
    pub parallelizability: f64,
    pub memory_access: String,
    pub cache_locality_index: f64,
    pub expected_duration: f64,
    pub priority: String,
}

impl Default for TaskCharacteristics {
    fn default() -> Self {
        Self {
            data_size: 0,
            compute_intensity: 1.0,
            parallelizability: 0.8,
            memory_access: "sequential".to_string(),
            cache_locality_index: 0.7,
            expected_duration: 10.0,
            priority: "normal".to_string(),
        }
    }
}

/// HAAL computation errors
#[derive(Debug, thiserror::Error)]
pub enum HaalError {
    #[error("Initialization failed: {0}")]
    InitializationFailed(String),
    #[error("Execution failed: {0}")]
    ExecutionFailed(String),
    #[error("Invalid operation: {0}")]
    InvalidOperation(String),
    #[error("CUDA not available")]
    CudaNotAvailable,
    #[error("Memory allocation failed")]
    MemoryAllocationFailed,
}

pub type Result<T> = std::result::Result<T, HaalError>;

/// Simple test function to verify the library builds
pub fn test_haal_build() -> Result<()> {
    println!("🚀 HAAL library loaded successfully!");
    
    #[cfg(cuda_available)]
    println!("✅ Built with CUDA support");
    
    #[cfg(not(cuda_available))]
    println!("🔧 Built with AVX2-only support");
    
    println!("🎯 Your optimized haal-avx2.cpp kernels are integrated!");
    
    Ok(())
}

/// Check if CUDA is available in this build
pub fn is_cuda_available() -> bool {
    cfg!(cuda_available)
}

/// Get information about available backends
pub fn get_backend_info() -> Vec<String> {
    let mut backends = vec!["AVX2".to_string()];
    
    if is_cuda_available() {
        backends.push("CUDA".to_string());
        backends.push("Hybrid".to_string());
    }
    
    backends
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_build_works() {
        assert!(test_haal_build().is_ok());
    }

    #[test]
    fn test_avx2_operation_conversion() {
        assert_eq!(u32::from(AVX2Operation::VectorAdd), 0);
        assert_eq!(u32::from(AVX2Operation::MatrixMul), 4);
    }

    #[test]
    fn test_task_characteristics_default() {
        let chars = TaskCharacteristics::default();
        assert_eq!(chars.parallelizability, 0.8);
        assert_eq!(chars.compute_intensity, 1.0);
    }

    #[test]
    fn test_backend_info() {
        let backends = get_backend_info();
        assert!(backends.contains(&"AVX2".to_string()));
        println!("Available backends: {:?}", backends);
    }
}
