// src/codegen/cu_codegen.rs
//! CUDA code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating CUDA code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of CUDA
//! kernels into the OmniCodex framework with appropriate launch configurations.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::fmt::Write as FmtWrite;
use crate::error::{OmniError, OmniResult};
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction, FunctionType};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, Codegen, ArgType};

/// CUDA code generator
pub struct CudaCodeGenerator {
    /// CUDA compute capability
    compute_capability: String,
    
    /// Use unified memory (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_unified_memory: bool,

    /// Use managed memory (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_managed_memory: bool,

    /// Use CUDA dynamic parallelism (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_dynamic_parallelism: bool,
}

impl CudaCodeGenerator {
    /// Create a new CUDA code generator
    pub fn new() -> Self {
        Self {
            compute_capability: "7.5".to_string(),
            use_unified_memory: true,
            use_managed_memory: false,
            use_dynamic_parallelism: false,
        }
    }
    
    /// Create a new CUDA code generator with specific configuration
    pub fn with_config(
        compute_capability: String,
        use_unified_memory: bool,
        use_managed_memory: bool,
        use_dynamic_parallelism: bool,
    ) -> Self {
        Self {
            compute_capability,
            use_unified_memory,
            use_managed_memory,
            use_dynamic_parallelism,
        }
    }
    
    /// Generate CUDA type from argument type
    fn generate_cuda_type(&self, arg_type: &ArgType) -> String {
        match arg_type {
            ArgType::Void => "void".to_string(),
            ArgType::I8 => "int8_t".to_string(),
            ArgType::I16 => "int16_t".to_string(),
            ArgType::I32 => "int32_t".to_string(),
            ArgType::I64 => "int64_t".to_string(),
            ArgType::U8 => "uint8_t".to_string(),
            ArgType::U16 => "uint16_t".to_string(),
            ArgType::U32 => "uint32_t".to_string(),
            ArgType::U64 => "uint64_t".to_string(),
            ArgType::F32 => "float".to_string(),
            ArgType::F64 => "double".to_string(),
            ArgType::Bool => "bool".to_string(),
            ArgType::I8Ptr => "int8_t*".to_string(),
            ArgType::I16Ptr => "int16_t*".to_string(),
            ArgType::I32Ptr => "int32_t*".to_string(),
            ArgType::I64Ptr => "int64_t*".to_string(),
            ArgType::U8Ptr => "uint8_t*".to_string(),
            ArgType::U16Ptr => "uint16_t*".to_string(),
            ArgType::U32Ptr => "uint32_t*".to_string(),
            ArgType::U64Ptr => "uint64_t*".to_string(),
            ArgType::F32Ptr => "float*".to_string(),
            ArgType::F64Ptr => "double*".to_string(),
            ArgType::BoolPtr => "bool*".to_string(),
            ArgType::VoidPtr => "void*".to_string(),
            ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate CUDA function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Add appropriate CUDA qualifiers
        let qualifier = match function.function_type {
            FunctionType::Kernel => "__global__",
            FunctionType::Device => "__device__",
            FunctionType::Host => "",
        };
        
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_cuda_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cuda_type = self.generate_cuda_type(&arg_type);
                        format!("{cuda_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        if params == "void" {
            Ok(format!("{} {} {}()", qualifier, return_type, function.name))
        } else {
            Ok(format!("{} {} {}({})", qualifier, return_type, function.name, params))
        }
    }
    
    /// Generate kernel launch wrapper
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cuda_type = self.generate_cuda_type(&arg_type);
                        format!("{cuda_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("dim3({}, {}, {})", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("dim3({}, {}, {})", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "dim3(1, 1, 1)".to_string(),
                "dim3(256, 1, 1)".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch wrapper for the CUDA kernel `{function_name}`
 * 
 * This function encapsulates the kernel launch configuration and
 * provides a C-compatible interface for the OmniCodex framework.
 */
extern "C" void launch_{function_name}({params}) {{
    // Launch parameters
    {grid_dim_def} gridDim = {grid_dim};
    {block_dim_def} blockDim = {block_dim};
    size_t sharedMemBytes = {shared_mem};
    
    // Configure CUDA stream (optional)
    cudaStream_t stream = 0;
    
    // Launch kernel
    {function_name}<<<gridDim, blockDim, sharedMemBytes, stream>>>({args});
    
    // Check for launch errors (optional)
    cudaError_t err = cudaGetLastError();
    if (err != cudaSuccess) {{
        // Handle error
        printf("CUDA Error: %s\n", cudaGetErrorString(err));
    }}
    
    // Synchronize (comment out for asynchronous operation)
    cudaDeviceSynchronize();
}}
"#,
            function_name = function.name,
            params = if params == "void" { "".to_string() } else { params.to_string() },
            grid_dim_def = "const dim3",
            block_dim_def = "const dim3",
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("arg{i}"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
        ));
        
        Ok(result)
    }
    
    /// Generate header content
    fn generate_header(&self) -> String {
        format!(
            r#"/**
 * OmniCodex CUDA integration generated by OmniForge.
 *
 * This file contains CUDA kernel declarations and launch wrappers
 * for the OmniCodex framework. It enables seamless integration of
 * CUDA kernels with the OmniCodex dispatch system.
 *
 * Target compute capability: {compute_capability}
 */

#ifndef OMNICODEX_CUDA_H
#define OMNICODEX_CUDA_H

#include <cuda_runtime.h>
#include <stdint.h>
#include <stdio.h>

// Forward declarations of kernel functions
"#,
            compute_capability = self.compute_capability,
        )
    }
    
    /// Generate implementation content header
    fn generate_implementation_header(&self) -> String {
        format!(
            r#"/**
 * OmniCodex CUDA integration generated by OmniForge.
 *
 * This file contains CUDA kernel implementations and launch wrappers
 * for the OmniCodex framework. It enables seamless integration of
 * CUDA kernels with the OmniCodex dispatch system.
 *
 * Target compute capability: {compute_capability}
 */

#include "omnicodex_cuda.h"

// Utility macros and functions
#define CUDA_CHECK(call) \
    do {{ \
        cudaError_t err = call; \
        if (err != cudaSuccess) {{ \
            printf("CUDA error in %s at line %d: %s\n", \
                   __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        }} \
    }} while(0)

// Implementation of kernel launch wrappers
"#,
            compute_capability = self.compute_capability,
        )
    }
    
    /// Generate footer content
    fn generate_footer(&self) -> String {
        r#"
#endif // OMNICODEX_CUDA_H
"#.to_string()
    }
    
    /// Generate OmniCodex utilities for CUDA
    fn generate_utilities(&self) -> String {
        r#"
/**
 * OmniCodex CUDA utilities
 */
namespace omnicodex {
namespace cuda {

/**
 * Initialize CUDA runtime
 * 
 * @return cudaError_t Result of initialization
 */
inline cudaError_t initialize() {
    cudaError_t err = cudaFree(0);  // Simple call to initialize context
    return err;
}

/**
 * Get device properties
 * 
 * @param prop Pointer to cudaDeviceProp structure
 * @param device Device ID (default: 0)
 * @return cudaError_t Result of operation
 */
inline cudaError_t get_device_properties(cudaDeviceProp* prop, int device = 0) {
    return cudaGetDeviceProperties(prop, device);
}

/**
 * Get optimal block size for a kernel
 * 
 * @param kernel Kernel function pointer
 * @return dim3 Optimal block dimensions
 */
template<typename KernelFunc>
inline dim3 get_optimal_block_size(KernelFunc kernel) {
    int blockSize = 0;
    int minGridSize = 0;
    
    cudaOccupancyMaxPotentialBlockSize(&minGridSize, &blockSize, kernel, 0, 0);
    
    return dim3(blockSize, 1, 1);
}

/**
 * Calculate grid size based on problem size and block size
 * 
 * @param problemSize Total number of threads needed
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size(size_t problemSize, dim3 blockSize) {
    size_t numBlocks = (problemSize + blockSize.x - 1) / blockSize.x;
    return dim3(numBlocks, 1, 1);
}

/**
 * Calculate 2D grid size based on problem dimensions and block size
 * 
 * @param width Width of the problem
 * @param height Height of the problem
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size_2d(size_t width, size_t height, dim3 blockSize) {
    dim3 gridSize;
    gridSize.x = (width + blockSize.x - 1) / blockSize.x;
    gridSize.y = (height + blockSize.y - 1) / blockSize.y;
    gridSize.z = 1;
    return gridSize;
}

/**
 * Calculate 3D grid size based on problem dimensions and block size
 * 
 * @param width Width of the problem
 * @param height Height of the problem
 * @param depth Depth of the problem
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size_3d(size_t width, size_t height, size_t depth, dim3 blockSize) {
    dim3 gridSize;
    gridSize.x = (width + blockSize.x - 1) / blockSize.x;
    gridSize.y = (height + blockSize.y - 1) / blockSize.y;
    gridSize.z = (depth + blockSize.z - 1) / blockSize.z;
    return gridSize;
}

} // namespace cuda
} // namespace omnicodex
"#.to_string()
    }
    
    /// Generate kernels and launch wrappers
    fn generate_kernels(&self, metadata: &[ExtractedMetadata]) -> OmniResult<(String, String)> {
        let mut header = String::new();
        let mut implementation = String::new();
        
        // Collect all kernel functions
        let mut kernel_functions = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    kernel_functions.push(function);
                }
            }
        }
        
        // Generate kernel declarations
        for function in &kernel_functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration to header
            writeln!(header, "{signature};").map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
        }
        
        // Generate kernel launch wrappers
        header.push_str("\n// Kernel launch wrapper declarations\n");
        
        for function in &kernel_functions {
            // Extract parameter types
            let params = if let Some(signature) = &function.signature {
                if signature.parameter_types.is_empty() {
                    "void".to_string()
                } else {
                    signature
                        .parameter_types
                        .iter()
                        .enumerate()
                        .map(|(i, param)| {
                            let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                            let cuda_type = self.generate_cuda_type(&arg_type);
                            format!("{cuda_type} arg{i}")
                        })
                        .collect::<Vec<_>>()
                        .join(", ")
                }
            } else {
                "void".to_string()
            };
            
            // Add declaration to header
            if params == "void" {
                writeln!(header, "extern \"C\" void launch_{}();", function.name).map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
            } else {
                writeln!(header, "extern \"C\" void launch_{}({});", function.name, params).map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
            }
            
            // Add implementation
            implementation.push_str(&self.generate_kernel_launcher(function)?);
        }
        
        Ok((header, implementation))
    }
}

impl CodeGenerator for CudaCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating CUDA OmniCodex");
        
        // Generate header
        let mut header_content = self.generate_header();
        
        // Generate implementation header
        let mut implementation_content = self.generate_implementation_header();
        
        // Generate kernels and launch wrappers
        let (kernel_declarations, kernel_implementations) = self.generate_kernels(metadata)?;
        
        // Add kernel declarations to header
        header_content.push_str(&kernel_declarations);
        
        // Add utilities to header
        header_content.push_str(&self.generate_utilities());
        
        // Add footer to header
        header_content.push_str(&self.generate_footer());
        
        // Add kernel implementations to implementation
        implementation_content.push_str(&kernel_implementations);
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                        entries.push(entry);
                    } else {
                        log::warn!("Failed to map function {} to codex entry", function.name);
                    }
                }
            }
        }
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CUDA_CODEX".to_string(),
            entries,
            code: implementation_content,
            wrapper_code: None,
            header_code: Some(header_content),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating CUDA wrappers");
        
        let mut wrappers = String::new();
        
        wrappers.push_str(r#"/**
 * OmniCodex CUDA wrapper functions generated by OmniForge.
 *
 * This file contains wrapper functions for CUDA kernels in the
 * OmniCodex framework.
 */

#include "omnicodex_cuda.h"

/**
 * Utility functions for wrapping CUDA operations
 */
"#);
        
        // Generate wrapper functions
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    // We've already generated the launch wrappers in the main file
                    // This is just for additional utility functions
                }
            }
        }
        
        Ok(wrappers)
    }
}

impl Default for CudaCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}
