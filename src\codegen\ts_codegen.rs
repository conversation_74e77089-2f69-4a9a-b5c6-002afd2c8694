// src/codegen/ts_codegen.rs
//! TypeScript code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating TypeScript code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of heterogeneous
//! computing capabilities into TypeScript/JavaScript applications.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use std::fmt::Write as FmtWrite;

use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen, ArgType, TargetType};

/// TypeScript code generator
pub struct TypeScriptCodeGenerator {
    /// Use ESM modules
    use_esm: bool,
    
    /// Use WebAssembly
    use_wasm: bool,
    
    /// Use WebGPU
    use_webgpu: bool,
    
    /// Use TypeScript strict mode
    use_strict: bool,
    
    /// Target TypeScript version
    target_version: String,
}

#[allow(dead_code)]
impl TypeScriptCodeGenerator {
    /// Create a new TypeScript code generator with default settings
    pub fn new() -> Self {
        Self {
            use_esm: true,
            use_wasm: true,
            use_webgpu: false,
            use_strict: true,
            target_version: "4.9".to_string(),
        }
    }
    
    /// Create a new TypeScript code generator with specific configuration
    pub fn with_config(
        use_esm: bool,
        use_wasm: bool,
        use_webgpu: bool,
        use_strict: bool,
        target_version: String,
    ) -> Self {
        Self {
            use_esm,
            use_wasm,
            use_webgpu,
            use_strict,
            target_version,
        }
    }
    
    /// Generate TypeScript type from argument type
    fn generate_ts_type(&self, arg_type: &ArgType) -> String {
        match arg_type {
            ArgType::Void => "void".to_string(),
            ArgType::I8 => "number".to_string(),
            ArgType::I16 => "number".to_string(),
            ArgType::I32 => "number".to_string(),
            ArgType::I64 => "bigint".to_string(),
            ArgType::U8 => "number".to_string(),
            ArgType::U16 => "number".to_string(),
            ArgType::U32 => "number".to_string(),
            ArgType::U64 => "bigint".to_string(),
            ArgType::F32 => "number".to_string(),
            ArgType::F64 => "number".to_string(),
            ArgType::Bool => "boolean".to_string(),
            ArgType::I8Ptr => "Int8Array".to_string(),
            ArgType::I16Ptr => "Int16Array".to_string(),
            ArgType::I32Ptr => "Int32Array".to_string(),
            ArgType::I64Ptr => "BigInt64Array".to_string(),
            ArgType::U8Ptr => "Uint8Array".to_string(),
            ArgType::U16Ptr => "Uint16Array".to_string(),
            ArgType::U32Ptr => "Uint32Array".to_string(),
            ArgType::U64Ptr => "BigUint64Array".to_string(),
            ArgType::F32Ptr => "Float32Array".to_string(),
            ArgType::F64Ptr => "Float64Array".to_string(),
            ArgType::BoolPtr => "Uint8Array".to_string(), // TypeScript doesn't have a BooleanArray
            ArgType::VoidPtr => "ArrayBuffer".to_string(),
            ArgType::Custom(name) => format!("Custom_{}", name.replace(" ", "_").replace("*", "Ptr")),
        }
    }
    
    /// Generate TypeScript function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let ts_type = self.generate_ts_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if ts_type == "void" {
                "void".to_string()
            } else {
                ts_type
            }
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let ts_type = self.generate_ts_type(&arg_type);
                        format!("arg{i}: {ts_type}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "".to_string()
        };
        
        Ok(format!("function {}({}): {}", function.name, params, return_type))
    }
    
    /// Generate TypeScript interface for a function
    fn generate_function_interface(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let ts_type = self.generate_ts_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if ts_type == "void" {
                "void".to_string()
            } else {
                ts_type
            }
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let ts_type = self.generate_ts_type(&arg_type);
                        format!("arg{i}: {ts_type}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "".to_string()
        };
        
        Ok(format!("  {}: ({}) => {}", function.name, params, return_type))
    }
    
    /// Generate TypeScript enum
    fn generate_enum(&self, name: &str, variants: &[(&str, &str)]) -> String {
        let mut result = format!("export enum {name} {{\n");
        
        for (variant, comment) in variants {
            writeln!(result, "  /** {comment} */").unwrap();
            writeln!(result, "  {variant} = '{variant}',").unwrap();
        }
        
        result.push_str("}\n");
        
        result
    }
    
    /// Generate TypeScript OmniCodex module
    fn generate_module(&self, entries: &[CodexEntry]) -> OmniResult<String> {
        let mut result = String::new();
        
        // Generate header with comments
        result.push_str(&format!(
            r#"/**
 * OmniCodex TypeScript Integration
 * 
 * Generated by OmniForge - The OmniCodex Compiler Framework
 * 
 * This module provides TypeScript interfaces and implementations for
 * heterogeneous computing with the OmniCodex framework.
 * 
 * Target TypeScript Version: {target_version}
 * Module Type: {module_type}
 * WebAssembly: {wasm}
 * WebGPU: {webgpu}
 * Strict Mode: {strict}
 */

{strict_directive}

{imports}
"#,
            target_version = self.target_version,
            module_type = if self.use_esm { "ESM" } else { "CommonJS" },
            wasm = if self.use_wasm { "Enabled" } else { "Disabled" },
            webgpu = if self.use_webgpu { "Enabled" } else { "Disabled" },
            strict = if self.use_strict { "Enabled" } else { "Disabled" },
            strict_directive = if self.use_strict { "\"use strict\";\n" } else { "" },
            imports = if self.use_esm {
                "// No external imports required"
            } else {
                "// No external imports required"
            },
        ));
        
        // Generate type definitions
        result.push_str(r#"
/**
 * Target platform for computation
 */
export enum OmniTarget {
  /** Central Processing Unit */
  CPU = 'CPU',
  /** Graphics Processing Unit */
  GPU = 'GPU',
  /** CPU with SIMD instructions */
  CPUSIMD = 'CPUSIMD',
  /** Tensor Processing Unit */
  TPU = 'TPU',
  /** Field-Programmable Gate Array */
  FPGA = 'FPGA',
  /** Other compute device */
  Other = 'Other',
}

/**
 * Argument type enumeration
 */
export enum ArgType {
  /** Void type */
  Void = 'Void',
  /** 8-bit signed integer */
  I8 = 'I8',
  /** 16-bit signed integer */
  I16 = 'I16',
  /** 32-bit signed integer */
  I32 = 'I32',
  /** 64-bit signed integer */
  I64 = 'I64',
  /** 8-bit unsigned integer */
  U8 = 'U8',
  /** 16-bit unsigned integer */
  U16 = 'U16',
  /** 32-bit unsigned integer */
  U32 = 'U32',
  /** 64-bit unsigned integer */
  U64 = 'U64',
  /** 32-bit floating point */
  F32 = 'F32',
  /** 64-bit floating point */
  F64 = 'F64',
  /** Boolean */
  Bool = 'Bool',
  /** Pointer to 8-bit signed integer */
  I8Ptr = 'I8Ptr',
  /** Pointer to 16-bit signed integer */
  I16Ptr = 'I16Ptr',
  /** Pointer to 32-bit signed integer */
  I32Ptr = 'I32Ptr',
  /** Pointer to 64-bit signed integer */
  I64Ptr = 'I64Ptr',
  /** Pointer to 8-bit unsigned integer */
  U8Ptr = 'U8Ptr',
  /** Pointer to 16-bit unsigned integer */
  U16Ptr = 'U16Ptr',
  /** Pointer to 32-bit unsigned integer */
  U32Ptr = 'U32Ptr',
  /** Pointer to 64-bit unsigned integer */
  U64Ptr = 'U64Ptr',
  /** Pointer to 32-bit floating point */
  F32Ptr = 'F32Ptr',
  /** Pointer to 64-bit floating point */
  F64Ptr = 'F64Ptr',
  /** Pointer to boolean */
  BoolPtr = 'BoolPtr',
  /** Pointer to void */
  VoidPtr = 'VoidPtr',
}

/**
 * Function metadata for compute operations
 */
export interface ComputeMetadata {
  /** Grid dimensions for parallel execution */
  gridSize: [number, number, number];
  /** Block dimensions for parallel execution */
  blockSize: [number, number, number];
  /** Shared memory size in bytes */
  sharedMem: number;
  /** Argument types */
  argsLayout: ArgType[];
}

/**
 * Entry in the OmniCodex dispatch table
 */
export interface OmniCodexEntry {
  /** Function name */
  name: string;
  /** Target platform */
  target: OmniTarget;
  /** Function implementation */
  impl: Function;
  /** Function metadata */
  metadata: ComputeMetadata;
}

/**
 * Error codes for OmniCodex operations
 */
export enum OmniErrorCode {
  /** No error */
  None = 'None',
  /** Function not found in OmniCodex */
  FunctionNotFound = 'FunctionNotFound',
  /** Argument count mismatch */
  ArgumentCountMismatch = 'ArgumentCountMismatch',
  /** Argument type mismatch */
  ArgumentTypeMismatch = 'ArgumentTypeMismatch',
  /** Not implemented */
  NotImplemented = 'NotImplemented',
  /** WebAssembly not available */
  WasmNotAvailable = 'WasmNotAvailable',
  /** WebGPU not available */
  WebGpuNotAvailable = 'WebGpuNotAvailable',
  /** Runtime error */
  RuntimeError = 'RuntimeError',
}

/**
 * OmniCodex error
 */
export class OmniError extends Error {
  constructor(
    /** Error code */
    public code: OmniErrorCode,
    /** Error message */
    message: string,
  ) {
    super(message);
    this.name = 'OmniError';
  }
}

/**
 * Interface for all functions in the OmniCodex
 */
export interface OmniFunctions {
"#);
        
        // Generate function interfaces
        let mut function_interfaces = Vec::new();
        let mut function_map = HashMap::new();
        
        for entry in entries {
            if function_map.contains_key(&entry.name) {
                continue;
            }
            
            let interface = format!("  /** {} function */\n  {}: Function;", entry.target_type, entry.name);
            function_interfaces.push(interface);
            function_map.insert(entry.name.clone(), true);
        }
        
        result.push_str(&function_interfaces.join("\n"));
        result.push_str("\n}\n\n");
        
        // Generate OmniCodex dispatch table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("export const OMNI_CODEX: OmniCodexEntry[] = [\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("[{}, {}, {}]", grid[0], grid[1], grid[2]),
                    format!("[{}, {}, {}]", block[0], block[1], block[2]),
                )
            } else {
                ("[1, 1, 1]".to_string(), "[256, 1, 1]".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = entry
                .metadata
                .arg_layout
                .iter()
                .map(|arg| format!("ArgType.{arg}"))
                .collect::<Vec<_>>()
                .join(", ");
            
            // Generate target type
            let target_type = match entry.target_type {
                TargetType::CPU => "OmniTarget.CPU",
                TargetType::GPU => "OmniTarget.GPU",
                TargetType::CPUSIMD => "OmniTarget.CPUSIMD",
                TargetType::TPU => "OmniTarget.TPU",
                TargetType::FPGA => "OmniTarget.FPGA",
                TargetType::Other => "OmniTarget.Other",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"  {{
    // {name} - {target_desc}
    name: '{name}',
    target: {target},
    impl: {impl_name},
    metadata: {{
      gridSize: {grid_size},
      blockSize: {block_size},
      sharedMem: {shared_mem},
      argsLayout: [{args_layout}],
    }},
  }},
"#,
                name = entry.name,
                target_desc = entry.target_type,
                target = target_type,
                impl_name = format!("{}_{}", entry.target_type.to_string().to_lowercase(), entry.name),
                grid_size = grid_size,
                block_size = block_size,
                shared_mem = shared_mem,
                args_layout = args_layout,
            ));
        }
        
        result.push_str("];\n\n");
        
        // Generate platform-specific implementations
        result.push_str("// CPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::CPU) {
            result.push_str(&format!(
                r#"/** CPU implementation of {name} */
function cpu_{name}(...args: any[]): any {{
  throw new OmniError(OmniErrorCode.NotImplemented, 'CPU implementation not provided');
}}

"#,
                name = entry.name,
            ));
        }
        
        result.push_str("// GPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::GPU) {
            result.push_str(&format!(
                r#"/** GPU implementation of {name} */
function gpu_{name}(...args: any[]): any {{
  throw new OmniError(OmniErrorCode.NotImplemented, 'GPU implementation not provided');
}}

"#,
                name = entry.name,
            ));
        }
        
        // Generate utility functions
        result.push_str(r#"/**
 * Find a function in the OmniCodex dispatch table
 * 
 * @param name Function name
 * @returns OmniCodexEntry or undefined if not found
 */
export function findFunction(name: string): OmniCodexEntry | undefined {
  return OMNI_CODEX.find(entry => entry.name === name);
}

/**
 * Execute a function by name
 * 
 * @param name Function name
 * @param args Function arguments
 * @returns Function result
 * @throws OmniError if an error occurs
 */
export function execute<T>(name: string, ...args: any[]): T {
  // Find the function in the dispatch table
  const entry = findFunction(name);
  if (!entry) {
    throw new OmniError(
      OmniErrorCode.FunctionNotFound,
      `Function not found: ${name}`
    );
  }
  
  // Check argument count
  if (args.length !== entry.metadata.argsLayout.length) {
    throw new OmniError(
      OmniErrorCode.ArgumentCountMismatch,
      `Argument count mismatch: expected ${entry.metadata.argsLayout.length}, got ${args.length}`
    );
  }
  
  // Execute function
  try {
    return entry.impl(...args) as T;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `Runtime error: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Generate a type-safe API for OmniCodex functions
 * 
 * @returns OmniFunctions object with type-safe function wrappers
 */
export function createApi(): OmniFunctions {
  const api: Record<string, Function> = {};
  
  for (const entry of OMNI_CODEX) {
    api[entry.name] = (...args: any[]) => execute(entry.name, ...args);
  }
  
  return api as unknown as OmniFunctions;
}

/**
 * Check if WebAssembly is available
 * 
 * @returns True if WebAssembly is available
 */
export function isWasmAvailable(): boolean {
  return typeof WebAssembly !== 'undefined';
}

/**
 * Check if WebGPU is available
 * 
 * @returns True if WebGPU is available
 */
export function isWebGpuAvailable(): boolean {
  return typeof navigator !== 'undefined' && 'gpu' in navigator;
}

/**
 * OmniCodex runtime
 */
export const runtime = {
  /** OmniCodex dispatch table */
  codex: OMNI_CODEX,
  /** Type-safe API for OmniCodex functions */
  api: createApi(),
  /** Find a function in the OmniCodex dispatch table */
  findFunction,
  /** Execute a function by name */
  execute,
  /** Check if WebAssembly is available */
  isWasmAvailable,
  /** Check if WebGPU is available */
  isWebGpuAvailable,
};

/**
 * Default export
 */
export default runtime;
"#);
        
        Ok(result)
    }
    
    /// Generate WebAssembly integration
    fn generate_wasm_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate WebAssembly integration if enabled
        if !self.use_wasm {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"/**
 * OmniCodex WebAssembly Integration
 * 
 * This module provides WebAssembly integration for the OmniCodex framework.
 */

import { OmniError, OmniErrorCode } from './omnicodex';

/**
 * WebAssembly instance
 */
let wasmInstance: WebAssembly.Instance | null = null;

/**
 * WebAssembly memory
 */
let wasmMemory: WebAssembly.Memory | null = null;

/**
 * WebAssembly exports
 */
let wasmExports: Record<string, any> = {};

/**
 * Initialize WebAssembly
 * 
 * @param wasmUrl URL to WebAssembly module
 * @returns Promise that resolves when WebAssembly is initialized
 * @throws OmniError if WebAssembly is not available or initialization fails
 */
export async function initWasm(wasmUrl: string): Promise<void> {
  if (typeof WebAssembly === 'undefined') {
    throw new OmniError(
      OmniErrorCode.WasmNotAvailable,
      'WebAssembly is not available in this environment'
    );
  }
  
  try {
    // Create import object with memory
    const importObject = {
      env: {
        memory: new WebAssembly.Memory({ initial: 256, maximum: 1024 }),
      },
    };
    
    // Fetch and instantiate WebAssembly module
    const response = await fetch(wasmUrl);
    const wasmBytes = await response.arrayBuffer();
    const wasmModule = await WebAssembly.compile(wasmBytes);
    const instance = await WebAssembly.instantiate(wasmModule, importObject);
    
    // Store WebAssembly instance, memory, and exports
    wasmInstance = instance;
    wasmMemory = importObject.env.memory;
    wasmExports = instance.exports as Record<string, any>;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebAssembly initialization failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Get WebAssembly exports
 * 
 * @returns WebAssembly exports
 * @throws OmniError if WebAssembly is not initialized
 */
export function getWasmExports(): Record<string, any> {
  if (!wasmInstance || !wasmExports) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebAssembly not initialized. Call initWasm() first.'
    );
  }
  
  return wasmExports;
}

/**
 * Get WebAssembly memory
 * 
 * @returns WebAssembly memory
 * @throws OmniError if WebAssembly is not initialized
 */
export function getWasmMemory(): WebAssembly.Memory {
  if (!wasmMemory) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebAssembly not initialized. Call initWasm() first.'
    );
  }
  
  return wasmMemory;
}

/**
 * Create a TypedArray view of WebAssembly memory
 * 
 * @param byteOffset Byte offset in WebAssembly memory
 * @param byteLength Byte length of the view
 * @param type Type of the view
 * @returns TypedArray view of WebAssembly memory
 * @throws OmniError if WebAssembly is not initialized
 */
export function createWasmView<T extends ArrayBufferView>(
  byteOffset: number,
  byteLength: number,
  type: new (buffer: ArrayBuffer, byteOffset: number, length: number) => T
): T {
  const memory = getWasmMemory();
  const buffer = memory.buffer;
  
  return new type(buffer, byteOffset, byteLength / (type.prototype as any).BYTES_PER_ELEMENT);
}

/**
 * Call a WebAssembly function
 * 
 * @param name Function name
 * @param args Function arguments
 * @returns Function result
 * @throws OmniError if WebAssembly is not initialized or function not found
 */
export function callWasm<T>(name: string, ...args: any[]): T {
  const exports = getWasmExports();
  
  if (!(name in exports)) {
    throw new OmniError(
      OmniErrorCode.FunctionNotFound,
      `WebAssembly function not found: ${name}`
    );
  }
  
  try {
    return exports[name](...args) as T;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebAssembly function call failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * WebAssembly utilities
 */
export const wasmUtils = {
  /** Initialize WebAssembly */
  initWasm,
  /** Get WebAssembly exports */
  getWasmExports,
  /** Get WebAssembly memory */
  getWasmMemory,
  /** Create a TypedArray view of WebAssembly memory */
  createWasmView,
  /** Call a WebAssembly function */
  callWasm,
};

export default wasmUtils;
"#);
        
        Ok(result)
    }
    
    /// Generate WebGPU integration
    fn generate_webgpu_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate WebGPU integration if enabled
        if !self.use_webgpu {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"/**
 * OmniCodex WebGPU Integration
 * 
 * This module provides WebGPU integration for the OmniCodex framework.
 */

import { OmniError, OmniErrorCode } from './omnicodex';

/**
 * WebGPU device
 */
let gpuDevice: GPUDevice | null = null;

/**
 * Check if WebGPU is available
 * 
 * @returns True if WebGPU is available
 */
export function isWebGpuAvailable(): boolean {
  return typeof navigator !== 'undefined' && 'gpu' in navigator;
}

/**
 * Initialize WebGPU
 * 
 * @returns Promise that resolves when WebGPU is initialized
 * @throws OmniError if WebGPU is not available or initialization fails
 */
export async function initWebGpu(): Promise<void> {
  if (!isWebGpuAvailable()) {
    throw new OmniError(
      OmniErrorCode.WebGpuNotAvailable,
      'WebGPU is not available in this environment'
    );
  }
  
  try {
    // Get GPU adapter
    const adapter = await (navigator as any).gpu.requestAdapter();
    if (!adapter) {
      throw new OmniError(
        OmniErrorCode.WebGpuNotAvailable,
        'No WebGPU adapter found'
      );
    }
    
    // Get GPU device
    const device = await adapter.requestDevice();
    if (!device) {
      throw new OmniError(
        OmniErrorCode.WebGpuNotAvailable,
        'Failed to get WebGPU device'
      );
    }
    
    // Store GPU device
    gpuDevice = device;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebGPU initialization failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Get WebGPU device
 * 
 * @returns WebGPU device
 * @throws OmniError if WebGPU is not initialized
 */
export function getGpuDevice(): GPUDevice {
  if (!gpuDevice) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebGPU not initialized. Call initWebGpu() first.'
    );
  }
  
  return gpuDevice;
}

/**
 * Create a compute pipeline
 * 
 * @param shaderCode WebGPU shader code
 * @param entryPoint Entry point function name
 * @returns Compute pipeline
 * @throws OmniError if WebGPU is not initialized
 */
export function createComputePipeline(
  shaderCode: string,
  entryPoint: string = 'main'
): GPUComputePipeline {
  const device = getGpuDevice();
  
  // Create shader module
  const shaderModule = device.createShaderModule({
    code: shaderCode,
  });
  
  // Create compute pipeline
  const pipeline = device.createComputePipeline({
    layout: 'auto',
    compute: {
      module: shaderModule,
      entryPoint,
    },
  });
  
  return pipeline;
}

/**
 * Create a GPU buffer
 * 
 * @param size Buffer size in bytes
 * @param usage Buffer usage flags
 * @param mappedAtCreation Whether the buffer should be mapped at creation
 * @returns GPU buffer
 * @throws OmniError if WebGPU is not initialized
 */
export function createBuffer(
  size: number,
  usage: GPUBufferUsageFlags,
  mappedAtCreation: boolean = false
): GPUBuffer {
  const device = getGpuDevice();
  
  return device.createBuffer({
    size,
    usage,
    mappedAtCreation,
  });
}

/**
 * Create a buffer from TypedArray
 * 
 * @param data TypedArray data
 * @param usage Buffer usage flags
 * @returns GPU buffer
 * @throws OmniError if WebGPU is not initialized
 */
export function createBufferFromTypedArray(
  data: ArrayBufferView,
  usage: GPUBufferUsageFlags
): GPUBuffer {
  const device = getGpuDevice();
  
  const buffer = device.createBuffer({
    size: data.byteLength,
    usage,
    mappedAtCreation: true,
  });
  
  const arrayBuffer = buffer.getMappedRange();
  new Uint8Array(arrayBuffer).set(new Uint8Array(data.buffer, data.byteOffset, data.byteLength));
  buffer.unmap();
  
  return buffer;
}

/**
 * Read buffer data
 * 
 * @param buffer GPU buffer
 * @param type TypedArray constructor
 * @returns Promise that resolves to TypedArray with buffer data
 * @throws OmniError if WebGPU is not initialized
 */
export async function readBuffer<T extends ArrayBufferView>(
  buffer: GPUBuffer,
  type: new (buffer: ArrayBuffer, byteOffset?: number, length?: number) => T
): Promise<T> {
  const device = getGpuDevice();
  
  // Create staging buffer
  const stagingBuffer = device.createBuffer({
    size: buffer.size,
    usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.MAP_READ,
  });
  
  // Create command encoder
  const encoder = device.createCommandEncoder();
  
  // Copy buffer to staging buffer
  encoder.copyBufferToBuffer(buffer, 0, stagingBuffer, 0, buffer.size);
  
  // Submit commands
  const commands = encoder.finish();
  device.queue.submit([commands]);
  
  // Map staging buffer
  await stagingBuffer.mapAsync(GPUMapMode.READ);
  
  // Get mapped range
  const arrayBuffer = stagingBuffer.getMappedRange();
  
  // Create TypedArray view
  const typedArray = new type(arrayBuffer);
  
  // Create a copy of the data
  const result = new type(typedArray.length);
  result.set(typedArray);
  
  // Unmap staging buffer
  stagingBuffer.unmap();
  
  return result;
}

/**
 * WebGPU utilities
 */
export const gpuUtils = {
  /** Check if WebGPU is available */
  isWebGpuAvailable,
  /** Initialize WebGPU */
  initWebGpu,
  /** Get WebGPU device */
  getGpuDevice,
  /** Create a compute pipeline */
  createComputePipeline,
  /** Create a GPU buffer */
  createBuffer,
  /** Create a buffer from TypedArray */
  createBufferFromTypedArray,
  /** Read buffer data */
  readBuffer,
};

export default gpuUtils;
"#);
        
        Ok(result)
    }
}

impl CodeGenerator for TypeScriptCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating TypeScript OmniCodex");
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate main module
        let main_module = self.generate_module(&entries)?;
        
        // Generate WebAssembly integration if enabled
        let wasm_module = self.generate_wasm_integration(&entries)?;
        
        // Generate WebGPU integration if enabled
        let webgpu_module = self.generate_webgpu_integration(&entries)?;
        
        // Combine modules
        let code = main_module;
        
        // Generate wrapper modules
        let mut wrapper_code = String::new();
        
        if !wasm_module.is_empty() {
            wrapper_code.push_str("// WebAssembly Integration\n\n");
            wrapper_code.push_str(&wasm_module);
            wrapper_code.push_str("\n\n");
        }
        
        if !webgpu_module.is_empty() {
            wrapper_code.push_str("// WebGPU Integration\n\n");
            wrapper_code.push_str(&webgpu_module);
        }
        
        let wrapper_code_option = if wrapper_code.is_empty() {
            None
        } else {
            Some(wrapper_code)
        };

        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code: wrapper_code_option,
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, _metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating TypeScript wrappers");
        
        // This is covered by the WebAssembly and WebGPU integration modules
        Ok(String::new())
    }
}

impl Default for TypeScriptCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}
