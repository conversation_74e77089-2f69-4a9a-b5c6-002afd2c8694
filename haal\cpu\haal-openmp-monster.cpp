#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>
#include <omp.h>

const size_t N = 8 * 1024 * 1024; // 32 MB for parallel

void monster_parallel_kernel(float* data, size_t n) {
    #pragma omp parallel for schedule(dynamic, 1024) num_threads(20)
    for (size_t i = 0; i < n; i += 32) {
        // 4 AVX2 vectors per thread iteration
        __m256 v0 = _mm256_load_ps(&data[i]);
        __m256 v1 = _mm256_load_ps(&data[i + 8]);
        __m256 v2 = _mm256_load_ps(&data[i + 16]);
        __m256 v3 = _mm256_load_ps(&data[i + 24]);
        
        // Create many computational chains
        __m256 acc0 = v0, acc1 = v1, acc2 = v2, acc3 = v3;
        __m256 acc4 = v0, acc5 = v1, acc6 = v2, acc7 = v3;
        
        // Massive parallel computation
        for (int iter = 0; iter < 500; ++iter) {
            acc0 = _mm256_fmadd_ps(acc0, v0, _mm256_set1_ps(1.0001f));
            acc1 = _mm256_fmadd_ps(acc1, v1, _mm256_set1_ps(0.9999f));
            acc2 = _mm256_fmadd_ps(acc2, v2, _mm256_set1_ps(1.0002f));
            acc3 = _mm256_fmadd_ps(acc3, v3, _mm256_set1_ps(0.9998f));
            acc4 = _mm256_fmadd_ps(acc4, v0, _mm256_set1_ps(1.0003f));
            acc5 = _mm256_fmadd_ps(acc5, v1, _mm256_set1_ps(0.9997f));
            acc6 = _mm256_fmadd_ps(acc6, v2, _mm256_set1_ps(1.0004f));
            acc7 = _mm256_fmadd_ps(acc7, v3, _mm256_set1_ps(0.9996f));
            
            // Cross-accumulator operations
            acc0 = _mm256_fmadd_ps(acc0, acc4, _mm256_set1_ps(0.5f));
            acc1 = _mm256_fmadd_ps(acc1, acc5, _mm256_set1_ps(0.5f));
            acc2 = _mm256_fmadd_ps(acc2, acc6, _mm256_set1_ps(0.5f));
            acc3 = _mm256_fmadd_ps(acc3, acc7, _mm256_set1_ps(0.5f));
        }
        
        // Final reduction and store
        __m256 result0 = _mm256_add_ps(acc0, acc4);
        __m256 result1 = _mm256_add_ps(acc1, acc5);
        __m256 result2 = _mm256_add_ps(acc2, acc6);
        __m256 result3 = _mm256_add_ps(acc3, acc7);
        
        _mm256_store_ps(&data[i], result0);
        _mm256_store_ps(&data[i + 8], result1);
        _mm256_store_ps(&data[i + 16], result2);
        _mm256_store_ps(&data[i + 24], result3);
    }
}

int main() {
    alignas(32) std::vector<float> a(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
    }

    std::cout << "Monster OpenMP (32MB, " << omp_get_max_threads() << " threads)" << std::endl;

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 100; ++iter) {
        monster_parallel_kernel(a.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    // 12 FMA ops per inner iteration * 500 inner iterations = 6000 FLOPs per element per outer iteration
    double ops = 100.0 * N * 6000.0;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "OpenMP MONSTER PARALLEL: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
