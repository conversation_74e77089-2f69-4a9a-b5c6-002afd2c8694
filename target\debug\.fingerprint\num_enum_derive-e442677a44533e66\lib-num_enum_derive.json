{"rustc": 1842507548689473721, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 2225463790103693989, "path": 5909776974073169828, "deps": [[3060637413840920116, "proc_macro2", false, 16584593718369059566], [4974441333307933176, "syn", false, 12478268728860239467], [15203748914246919255, "proc_macro_crate", false, 13846465393468585352], [17990358020177143287, "quote", false, 9129625013016073763]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\num_enum_derive-e442677a44533e66\\dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}