name: 🚀 Elite Build Pipeline (MSVC + CUDA + GCC + OpenMP + AVX2)

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  BUILD_TYPE: Release

jobs:
  build-windows-msvc:
    name: 🔧 Windows MSVC (OpenMP + AVX2)
    runs-on: windows-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🔧 Setup MSVC
      uses: microsoft/setup-msbuild@v2
      
    - name: 🏗️ Build with MSVC
      run: |
        cl /EHsc /std:c++20 /openmp /arch:AVX2 /O2 /favor:INTEL64 *.cpp *.c
        
    - name: 🧪 Test Executables
      run: |
        Get-ChildItem -Filter "*.exe" | ForEach-Object { & $_.FullName }

  build-windows-gcc:
    name: ⚡ Windows GCC (OpenMP + AVX2)
    runs-on: windows-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: ⚡ Setup MinGW
      uses: msys2/setup-msys2@v2
      with:
        msystem: MINGW64
        install: mingw-w64-x86_64-gcc mingw-w64-x86_64-make
        
    - name: 🏗️ Build with GCC
      shell: msys2 {0}
      run: |
        gcc -std=c17 -fopenmp -mavx2 -O3 -march=native -mtune=native *.c -o test_c.exe
        g++ -std=c++20 -fopenmp -mavx2 -O3 -march=native -mtune=native *.cpp -o test_cpp.exe
        
    - name: 🧪 Test Executables
      shell: msys2 {0}
      run: |
        ./test_c.exe || true
        ./test_cpp.exe || true

  build-linux-gcc:
    name: 🐧 Linux GCC (OpenMP + AVX2)
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 📦 Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc g++ libomp-dev
        
    - name: 🏗️ Build with GCC
      run: |
        gcc -std=c17 -fopenmp -mavx2 -O3 -march=native -mtune=native *.c -o test_c || true
        g++ -std=c++20 -fopenmp -mavx2 -O3 -march=native -mtune=native *.cpp -o test_cpp || true
        
    - name: 🧪 Test Executables
      run: |
        ./test_c || true
        ./test_cpp || true

  build-cuda:
    name: 🚀 CUDA Build (RTX Support)
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🚀 Setup CUDA
      uses: Jimver/cuda-toolkit@v0.2.14
      with:
        cuda: '12.2'
        
    - name: 🏗️ Build CUDA
      run: |
        nvcc -arch=sm_89 -O3 --use_fast_math *.cu -o test_cuda || true
        
    - name: 🧪 Validate CUDA Build
      run: |
        ./test_cuda || echo "CUDA test completed (may need GPU runtime)"

  performance-analysis:
    name: 📊 Performance Analysis
    runs-on: ubuntu-latest
    needs: [build-windows-msvc, build-windows-gcc, build-linux-gcc]
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 📊 CPU Info
      run: |
        echo "=== CPU Information ==="
        lscpu
        echo "=== AVX2 Support Check ==="
        grep -o 'avx2' /proc/cpuinfo | head -1 || echo "AVX2 not detected"
        echo "=== OpenMP Support Check ==="
        echo '#include <omp.h>' > test_omp.c
        echo 'int main() { return omp_get_max_threads(); }' >> test_omp.c
        gcc -fopenmp test_omp.c -o test_omp && ./test_omp && echo "OpenMP working" || echo "OpenMP test failed"
        
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🔒 Run CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: cpp
        
    - name: 🏗️ Autobuild
      uses: github/codeql-action/autobuild@v2
      
    - name: 🔍 Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2