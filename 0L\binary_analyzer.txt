
Directory: src\binary_analyzer
File: elf_analyzer.rs
=====================
// src/binary_analyzer/elf_analyzer.rs
//! ELF binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing ELF (Executable and Linkable Format)
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::elf::{Elf, sym::{STT_FUNC, STB_GLOBAL, STB_WEAK}};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// ELF binary analyzer
pub struct ELFAnalyzer {
    // Configuration options can be added here
}

impl Default for ELFAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl ELFAnalyzer {
    /// Create a new ELF analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze an ELF file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing ELF file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the ELF file
        let elf = Elf::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse ELF file: {e}")))?;
        
        // Extract exports (symbols)
        let exports = self.extract_exports(&elf)?;
        
        // Extract imports
        let imports = self.extract_imports(&elf)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(&elf)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(&elf)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::ELF,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the ELF file
    fn extract_exports(&self, elf: &Elf) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for sym in elf.syms.iter() {
            // Only consider function symbols that are defined and global/weak
            if sym.st_type() == STT_FUNC && (sym.st_bind() == STB_GLOBAL || sym.st_bind() == STB_WEAK) && !sym.is_import() {
                if let Some(name) = elf.strtab.get_at(sym.st_name) {
                    exports.push(ExportedFunction {
                        name: name.to_string(),
                        address: sym.st_value,
                        signature: None, // Cannot determine signature from ELF alone
                        calling_convention: Some(CallingConvention::C),
                        metadata: serde_json::json!({
                            "size": sym.st_size,
                            "visibility": if sym.st_bind() == STB_GLOBAL { "global" } else { "weak" },
                        }),
                    });
                }
            }
        }
        
        Ok(exports)
    }
    
    /// Extract imported functions from the ELF file
    fn extract_imports(&self, elf: &Elf) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for sym in elf.syms.iter() {
            // Only consider function symbols that are undefined (imported)
            if sym.st_type() == STT_FUNC && sym.is_import() {
                if let Some(name) = elf.strtab.get_at(sym.st_name) {
                    // Try to determine the library name from the dynamic section
                    let library = self.determine_library_for_import(elf, name);
                    
                    imports.push(ImportedFunction {
                        name: name.to_string(),
                        library,
                        signature: None, // Cannot determine signature from ELF alone
                    });
                }
            }
        }
        
        Ok(imports)
    }
    
    /// Determine the library name for an imported symbol
    fn determine_library_for_import(&self, elf: &Elf, symbol_name: &str) -> String {
        // This is a simplified implementation. A full implementation requires analyzing
        // the .dynamic section for DT_NEEDED entries and potentially the version definitions
        // in .gnu.version_r to map symbols to specific libraries.

        // For now, return the first DT_NEEDED entry if available, or "unknown".
        if let Some(library) = elf.libraries.first() {
            log::warn!(
                "Unable to definitively map import '{}' to a specific library. Guessing '{}'.", 
                symbol_name, 
                library
            );
            return library.to_string();
        }

        log::warn!("Could not determine library for imported symbol '{}'.", symbol_name);
        "unknown".to_string()
    }
    
    /// Extract dependencies from the ELF file
    fn extract_dependencies(&self, elf: &Elf) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        for dyn_lib in &elf.libraries {
            dependencies.push(dyn_lib.to_string());
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the ELF file
    fn extract_additional_metadata(&self, elf: &Elf) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "machine": elf.header.e_machine,
            "class": if elf.is_64 { "ELF64" } else { "ELF32" },
            "endianness": if elf.little_endian { "little" } else { "big" },
            "entry_point": elf.entry,
            "is_executable": elf.header.e_type == goblin::elf::header::ET_EXEC,
            "is_dynamic": elf.header.e_type == goblin::elf::header::ET_DYN,
        }))
    }
}



Directory: src\binary_analyzer
File: pe_analyzer.rs
====================
// src/binary_analyzer/pe_analyzer.rs
//! PE binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing Windows PE (Portable Executable)
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::pe::{PE, export::Export, import::Import};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// PE binary analyzer
pub struct PEAnalyzer {
    // Configuration options can be added here
}

impl Default for PEAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl PEAnalyzer {
    /// Create a new PE analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a PE file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing PE file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the PE file
        let pe = PE::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse PE file: {e}")))?;
        
        // Extract exports
        let exports = self.extract_exports(&pe)?;
        
        // Extract imports
        let imports = self.extract_imports(&pe)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(&pe)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(&pe)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::PE,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the PE file
    fn extract_exports(&self, pe: &PE) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for export in &pe.exports {
            // Note: Forwarded export checking removed due to API changes

            exports.push(self.convert_export(export)?);
        }
        
        Ok(exports)
    }
    
    /// Convert a PE export to an ExportedFunction
    fn convert_export(&self, export: &Export) -> OmniResult<ExportedFunction> {
        // Try to determine calling convention based on name prefixes/suffixes
        let name_str = export.name.unwrap_or("unknown");
        let (name, calling_convention) = self.determine_calling_convention(name_str);
        
        Ok(ExportedFunction {
            name,
            address: export.rva as u64,
            signature: None, // Cannot determine signature from PE alone
            calling_convention: Some(calling_convention),
            metadata: serde_json::json!({
                "rva": export.rva,
            }),
        })
    }
    
    /// Determine calling convention based on function name
    fn determine_calling_convention(&self, name: &str) -> (String, CallingConvention) {
        let name = name.to_string();
        
        // Check for common calling convention indicators in function names
        if name.starts_with("_stdcall@") || name.contains("@") && name.matches("@").count() == 1 {
            // _stdcall functions often have @N suffix where N is the byte count of parameters
            return (name.clone(), CallingConvention::Stdcall);
        } else if name.starts_with("@fastcall@") {
            return (name.clone(), CallingConvention::Fastcall);
        } else if name.starts_with("_thiscall@") {
            return (name.clone(), CallingConvention::Thiscall);
        } else if name.starts_with("_vectorcall@") {
            return (name.clone(), CallingConvention::Vectorcall);
        }
        
        // Default to C calling convention
        (name, CallingConvention::C)
    }
    
    /// Extract imported functions from the PE file
    fn extract_imports(&self, pe: &PE) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for import in &pe.imports {
            imports.push(self.convert_import(import)?);
        }
        
        Ok(imports)
    }
    
    /// Convert a PE import to an ImportedFunction
    fn convert_import(&self, import: &Import) -> OmniResult<ImportedFunction> {
        Ok(ImportedFunction {
            name: import.name.to_string(),
            library: import.dll.to_string(),
            signature: None, // Cannot determine signature from PE alone
        })
    }
    
    /// Extract dependencies from the PE file
    fn extract_dependencies(&self, pe: &PE) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        // Get unique DLL names
        for import in &pe.imports {
            let dll_name = import.dll.to_string();
            if !dependencies.contains(&dll_name) {
                dependencies.push(dll_name);
            }
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the PE file
    fn extract_additional_metadata(&self, pe: &PE) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "machine": pe.header.coff_header.machine,
            "characteristics": pe.header.coff_header.characteristics,
            "is_dll": pe.is_lib,
            "is_executable": !pe.is_lib,
            "is_32_bit": !pe.is_64,
            "entry_point": pe.entry,
            "image_base": pe.image_base,
            "number_of_sections": pe.header.coff_header.number_of_sections,
        }))
    }
}



Directory: src\binary_analyzer
File: macho_analyzer.rs
=======================
// src/binary_analyzer/macho_analyzer.rs
//! Mach-O binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing macOS Mach-O
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::mach::{Mach, MachO, exports::Export, imports::Import, header::{MH_MAGIC_64, MH_CIGAM_64}};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// Mach-O binary analyzer
pub struct MachOAnalyzer {
    // Configuration options can be added here
}

impl Default for MachOAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl MachOAnalyzer {
    /// Create a new Mach-O analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a Mach-O file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing Mach-O file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the Mach-O file
        let mach = Mach::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse Mach-O file: {e}")))?;
        
        match mach {
            Mach::Binary(macho) => self.analyze_macho(path, &macho),
            Mach::Fat(_fat) => {
                // For fat binaries, we'll skip detailed analysis for now
                // This is a simplified implementation
                Err(OmniError::BinaryFormat("Fat binary analysis not fully implemented".to_string()))
            }
        }
    }
    
    /// Analyze a Mach-O binary and extract metadata
    fn analyze_macho(&self, path: &Path, macho: &MachO) -> OmniResult<BinaryMetadata> {
        // Extract exports
        let exports = self.extract_exports(macho)?;
        
        // Extract imports
        let imports = self.extract_imports(macho)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(macho)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(macho)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::MachO,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the Mach-O file
    fn extract_exports(&self, macho: &MachO) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for export in macho.exports()
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to get exports: {e}")))? {
            exports.push(self.convert_export(&export)?);
        }
        
        Ok(exports)
    }
    
    /// Convert a Mach-O export to an ExportedFunction
    fn convert_export(&self, export: &Export) -> OmniResult<ExportedFunction> {
        Ok(ExportedFunction {
            name: export.name.to_string(),
            address: export.offset,
            signature: None, // Cannot determine signature from Mach-O alone
            calling_convention: Some(CallingConvention::C),
            metadata: serde_json::json!({
                "offset": export.offset,
            }),
        })
    }
    
    /// Extract imported functions from the Mach-O file
    fn extract_imports(&self, macho: &MachO) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for import in macho.imports()
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to get imports: {e}")))? {
            imports.push(self.convert_import(&import)?);
        }
        
        Ok(imports)
    }
    
    /// Convert a Mach-O import to an ImportedFunction
    fn convert_import(&self, import: &Import) -> OmniResult<ImportedFunction> {
        let library = if import.dylib.is_empty() {
            "unknown".to_string()
        } else {
            import.dylib.to_string()
        };

        Ok(ImportedFunction {
            name: import.name.to_string(),
            library,
            signature: None, // Cannot determine signature from Mach-O alone
        })
    }
    
    /// Extract dependencies from the Mach-O file
    fn extract_dependencies(&self, macho: &MachO) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        for lib in &macho.libs {
            dependencies.push(lib.to_string());
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the Mach-O file
    fn extract_additional_metadata(&self, macho: &MachO) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "cputype": macho.header.cputype,
            "cpusubtype": macho.header.cpusubtype,
            "filetype": macho.header.filetype,
            "is_64": macho.header.magic == MH_MAGIC_64 || macho.header.magic == MH_CIGAM_64,
            "is_executable": macho.header.filetype == goblin::mach::header::MH_EXECUTE,
            "is_dylib": macho.header.filetype == goblin::mach::header::MH_DYLIB,
        }))
    }
}



Directory: src\binary_analyzer
File: mod.rs
============
// src/binary_analyzer/mod.rs
//! Binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing binary files and extracting
//! metadata such as exported functions, memory layouts, and other information
//! needed for generating OmniCodex dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::io::Read;
use serde::{Serialize, Deserialize};
use object::{Object, ObjectSymbol};
use crate::error::{OmniError, OmniResult};
use crate::ahaw::{self};

mod pe_analyzer;
mod elf_analyzer;
mod macho_analyzer;
mod ptx_analyzer;

pub use self::pe_analyzer::PEAnalyzer;
pub use self::elf_analyzer::ELFAnalyzer;
pub use self::macho_analyzer::MachOAnalyzer;
pub use self::ptx_analyzer::PTXAnalyzer;

/// Supported binary types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BinaryType {
    /// Windows PE executable/DLL
    PE,
    /// Linux ELF executable/shared object
    ELF,
    /// macOS Mach-O executable/dylib
    MachO,
    /// NVIDIA PTX file
    PTX,
    /// NVIDIA cubin file
    Cubin,
    /// Object file
    Object,
    /// Unknown binary type
    Unknown,
}

/// Extracted metadata from a binary file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinaryMetadata {
    /// Binary type
    pub binary_type: BinaryType,
    
    /// Path to the binary file
    pub path: String,
    
    /// Exported functions
    pub exports: Vec<ExportedFunction>,
    
    /// Imported functions
    pub imports: Vec<ImportedFunction>,
    
    /// Dependencies
    pub dependencies: Vec<String>,
    
    /// Additional metadata specific to the binary type
    pub additional_metadata: serde_json::Value,
}

/// Information about an exported function
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportedFunction {
    /// Function name
    pub name: String,
    
    /// Function address
    pub address: u64,
    
    /// Function signature (if available)
    pub signature: Option<FunctionSignature>,
    
    /// Calling convention (if available)
    pub calling_convention: Option<CallingConvention>,
    
    /// Additional function metadata
    pub metadata: serde_json::Value,
}

/// Information about an imported function
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportedFunction {
    /// Function name
    pub name: String,
    
    /// Library name
    pub library: String,
    
    /// Function signature (if available)
    pub signature: Option<FunctionSignature>,
}

/// Function signature information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionSignature {
    /// Return type
    pub return_type: TypeInfo,
    
    /// Parameter types
    pub parameter_types: Vec<TypeInfo>,
    
    /// Is variadic
    pub is_variadic: bool,
}

/// Type information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TypeInfo {
    /// Type name
    pub name: String,
    
    /// Type size in bytes
    pub size: Option<usize>,
    
    /// Type alignment in bytes
    pub alignment: Option<usize>,
    
    /// Is pointer
    pub is_pointer: bool,
    
    /// Is array
    pub is_array: bool,
    
    /// Array dimensions (if is_array is true)
    pub array_dimensions: Vec<usize>,
}

/// Symbol type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SymbolType {
    /// Function symbol
    Function,
    /// Variable symbol
    Variable,
    /// Constant symbol
    Constant,
    /// Import symbol
    Import,
    /// Export symbol
    Export,
}

/// Calling convention
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CallingConvention {
    /// C calling convention
    C,
    /// stdcall calling convention
    Stdcall,
    /// fastcall calling convention
    Fastcall,
    /// thiscall calling convention
    Thiscall,
    /// vectorcall calling convention
    Vectorcall,
    /// CUDA kernel
    CudaKernel,
    /// Unknown calling convention
    Unknown,
}

/// Binary analyzer
pub struct BinaryAnalyzer {
    pe_analyzer: PEAnalyzer,
    elf_analyzer: ELFAnalyzer,
    macho_analyzer: MachOAnalyzer,
    ptx_analyzer: PTXAnalyzer,
}

impl BinaryAnalyzer {
    /// Create a new binary analyzer
    pub fn new() -> Self {
        Self {
            pe_analyzer: PEAnalyzer::new(),
            elf_analyzer: ELFAnalyzer::new(),
            macho_analyzer: MachOAnalyzer::new(),
            ptx_analyzer: PTXAnalyzer::new(),
        }
    }
    
    /// Analyze a binary file and extract metadata
    pub fn analyze_binary(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        // Determine the binary type based on file extension and content
        let binary_type = self.determine_binary_type(path)?;

        // Analyze the binary based on its type
        match binary_type {
            BinaryType::PE => self.pe_analyzer.analyze(path),
            BinaryType::ELF => self.elf_analyzer.analyze(path),
            BinaryType::MachO => self.macho_analyzer.analyze(path),
            BinaryType::PTX | BinaryType::Cubin => self.ptx_analyzer.analyze(path),
            BinaryType::Object => self.analyze_object_file(path),
            BinaryType::Unknown => Err(OmniError::UnsupportedFileType(path.to_path_buf())),
        }
    }

    /// Accelerated pattern matching for binary signatures
    pub fn accelerated_pattern_match(&self, patterns: &[u8], signatures: &[u8]) -> OmniResult<Vec<usize>> {
        // Convert byte patterns to f32 for acceleration
        let mut pattern_data: Vec<f32> = patterns.iter().map(|&b| b as f32).collect();
        let mut signature_data: Vec<f32> = signatures.iter().map(|&b| b as f32).collect();

        // Use acceleration for pattern matching if data size is significant
        if pattern_data.len() > 1000 {
            match ahaw::binary_analyzer::accelerate_pattern_matching(&mut pattern_data, &mut signature_data) {
                Ok(result) => {
                    println!("🚀 Accelerated pattern matching: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Perform actual pattern matching logic (simplified example)
        let mut matches = Vec::new();
        for (i, window) in signatures.windows(patterns.len()).enumerate() {
            if window == patterns {
                matches.push(i);
            }
        }

        Ok(matches)
    }

    /// Accelerated signature validation
    pub fn accelerated_signature_validation(&self, signatures: &[u8]) -> OmniResult<bool> {
        // Convert signatures to f32 for acceleration
        let mut signature_data: Vec<f32> = signatures.iter().map(|&b| b as f32).collect();

        // Use acceleration for signature validation if data size is significant
        if signature_data.len() > 500 {
            match ahaw::binary_analyzer::accelerate_signature_validation(&mut signature_data) {
                Ok(result) => {
                    println!("🚀 Accelerated signature validation: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);

                    // Use the computed norm for validation
                    let norm_threshold = 100.0; // Example threshold
                    return Ok(result.performance_metrics.throughput_gflops > norm_threshold);
                },
                Err(e) => {
                    println!("⚠️ Acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Fallback CPU validation logic
        Ok(!signatures.is_empty() && signatures.len() >= 4)
    }
    
    /// Determine the binary type based on file extension and content
    fn determine_binary_type(&self, path: &Path) -> OmniResult<BinaryType> {
        // First check by extension
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            match extension.to_lowercase().as_str() {
                "exe" | "dll" => return Ok(BinaryType::PE),
                "so" | "elf" => return Ok(BinaryType::ELF),
                "dylib" => return Ok(BinaryType::MachO),
                "ptx" => return Ok(BinaryType::PTX),
                "cubin" => return Ok(BinaryType::Cubin),
                "o" | "obj" => return Ok(BinaryType::Object),
                _ => {} // Continue with content-based detection
            }
        }
        
        // If extension doesn't give a definitive answer, check file content
        let file = std::fs::File::open(path)?;
        let mut buffer = [0; 16];
        // Use a buffered reader to avoid consuming the file handle
        let mut reader = std::io::BufReader::new(&file);

        if reader.read_exact(&mut buffer).is_err() {
            return Ok(BinaryType::Unknown);
        }
        
        // Check for magic numbers
        if buffer.starts_with(b"MZ") {
            Ok(BinaryType::PE)
        } else if buffer.starts_with(&[0x7F, b'E', b'L', b'F']) {
            Ok(BinaryType::ELF)
        } else if buffer.starts_with(&[0xCF, 0xFA, 0xED, 0xFE]) || buffer.starts_with(&[0xCE, 0xFA, 0xED, 0xFE]) {
            Ok(BinaryType::MachO)
        } else if buffer.starts_with(b".version") || buffer.starts_with(b"//") {
            // PTX files often start with .version or //
            Ok(BinaryType::PTX)
        } else {
            // Try to parse as an object file using the same file handle
            let map = unsafe { memmap2::MmapOptions::new().map(&file)? };
            match object::File::parse(&*map) {
                Ok(_) => Ok(BinaryType::Object),
                Err(_) => Ok(BinaryType::Unknown),
            }
        }
    }    
    /// Analyze an object file
    fn analyze_object_file(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        let file = std::fs::File::open(path)?;
        let map = unsafe { memmap2::MmapOptions::new().map(&file)? };
        
        let obj_file = object::File::parse(&*map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse object file: {e}")))?;
        
        let mut exports = Vec::new();
        let mut imports = Vec::new();
        
        // Extract symbols
        for symbol in obj_file.symbols() {
            let name = symbol.name()
                .map_err(|e| OmniError::BinaryFormat(format!("Failed to get symbol name: {e}")))?
                .to_string();
            
            let address = symbol.address();
            
            if symbol.is_definition() {
                exports.push(ExportedFunction {
                    name,
                    address,
                    signature: None, // Cannot determine signature from object file alone
                    calling_convention: None,
                    metadata: serde_json::Value::Null,
                });
            } else {
                imports.push(ImportedFunction {
                    name,
                    library: String::new(), // Cannot determine library from object file alone
                    signature: None,
                });
            }
        }
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::Object,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies: Vec::new(),
            additional_metadata: serde_json::Value::Null,
        })
    }
}

impl Default for BinaryAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}



Directory: src\binary_analyzer
File: ptx_analyzer.rs
=====================
// src/binary_analyzer/ptx_analyzer.rs
//! PTX analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing NVIDIA PTX files and extracting
//! metadata such as kernel functions, memory usage, and launch parameters.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs;
use regex::Regex;

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, FunctionSignature, TypeInfo, CallingConvention};

/// PTX analyzer
pub struct PTXAnalyzer {
    // Configuration options can be added here
}

impl Default for PTXAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl PTXAnalyzer {
    /// Create a new PTX analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a PTX file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing PTX file: {}", path.display());
        
        // Read the PTX file
        let ptx_content = fs::read_to_string(path)?;
        
        // Extract kernel functions
        let exports = self.extract_kernels(&ptx_content)?;
        
        // PTX files don't have traditional imports
        let imports = Vec::new();
        
        // PTX files don't have traditional dependencies
        let dependencies = Vec::new();
        
        // Extract additional metadata
        let additional_metadata = self.extract_additional_metadata(&ptx_content)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::PTX,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract kernel functions from the PTX content
    fn extract_kernels(&self, ptx_content: &str) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        // Regular expression to match kernel function declarations
        // Format: .entry kernel_name(param_list)
        let kernel_regex = Regex::new(r"\.entry\s+(\w+)\s*\(([^)]*)\)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        // Regular expression to match parameter declarations
        // Format: .param .type .ptr .align X .space .param_space param_name
        let param_regex = Regex::new(r"\.param\s+(\.\w+)(?:\s+\.ptr)?(?:\s+\.align\s+(\d+))?(?:\s+\.space\s+\.(\w+))?\s+(\w+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        // Find all kernel declarations
        for captures in kernel_regex.captures_iter(ptx_content) {
            let kernel_name = captures.get(1).unwrap().as_str().to_string();
            let param_list = captures.get(2).unwrap().as_str();
            
            // Parse parameters
            let mut parameter_types = Vec::new();
            for param in param_list.split(',') {
                if let Some(param_captures) = param_regex.captures(param.trim()) {
                    let type_name = param_captures.get(1).unwrap().as_str().to_string();
                    let alignment = param_captures.get(2).map(|m| m.as_str().parse::<usize>().unwrap_or(0));
                    let _space = param_captures.get(3).map(|m| m.as_str().to_string());
                    let _param_name = param_captures.get(4).unwrap().as_str().to_string();
                    
                    parameter_types.push(TypeInfo {
                        name: type_name,
                        size: None, // Cannot determine size from PTX alone
                        alignment,
                        is_pointer: param.contains(".ptr"),
                        is_array: false,
                        array_dimensions: Vec::new(),
                    });
                }
            }
            
            // Create function signature
            let signature = FunctionSignature {
                return_type: TypeInfo {
                    name: ".void".to_string(),
                    size: Some(0),
                    alignment: Some(0),
                    is_pointer: false,
                    is_array: false,
                    array_dimensions: Vec::new(),
                },
                parameter_types,
                is_variadic: false,
            };
            
            // Extract shared memory usage
            let shared_memory = self.extract_shared_memory(ptx_content, &kernel_name);
            
            // Extract register usage
            let register_count = self.extract_register_count(ptx_content, &kernel_name);
            
            // Create exported function
            exports.push(ExportedFunction {
                name: kernel_name.clone(),
                address: 0, // PTX doesn't have addresses
                signature: Some(signature),
                calling_convention: Some(CallingConvention::CudaKernel),
                metadata: serde_json::json!({
                    "shared_memory": shared_memory,
                    "register_count": register_count,
                    "is_kernel": true,
                }),
            });
        }
        
        Ok(exports)
    }
    
    /// Extract shared memory usage for a kernel
    fn extract_shared_memory(&self, ptx_content: &str, kernel_name: &str) -> Option<usize> {
        // Find all shared memory declarations within the kernel
        let shared_mem_regex = Regex::new(&format!(r"{}[\s\S]+?\.shared\s+\.align\s+\d+\s+\.b8\s+\w+\[(\d+)\]", regex::escape(kernel_name))).ok()?;
        
        let mut total_shared_mem = 0;
        for captures in shared_mem_regex.captures_iter(ptx_content) {
            if let Some(size_match) = captures.get(1) {
                if let Ok(size) = size_match.as_str().parse::<usize>() {
                    total_shared_mem += size;
                }
            }
        }
        
        if total_shared_mem > 0 {
            Some(total_shared_mem)
        } else {
            None
        }
    }
    
    /// Extract register count for a kernel
    fn extract_register_count(&self, ptx_content: &str, kernel_name: &str) -> Option<usize> {
        // Regular expression to match register count
        // Format: // Function requires X registers
        let reg_regex = Regex::new(&format!(r"// Function {}.*?requires\s+(\d+)\s+registers", regex::escape(kernel_name))).ok()?;
        
        if let Some(captures) = reg_regex.captures(ptx_content) {
            if let Some(count_match) = captures.get(1) {
                return count_match.as_str().parse::<usize>().ok();
            }
        }
        
        None
    }
    
    /// Extract additional metadata from the PTX content
    fn extract_additional_metadata(&self, ptx_content: &str) -> OmniResult<serde_json::Value> {
        // Extract PTX version
        let version_regex = Regex::new(r"\.version\s+(\d+)\.(\d+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let version = if let Some(captures) = version_regex.captures(ptx_content) {
            let major = captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0);
            let minor = captures.get(2).unwrap().as_str().parse::<u32>().unwrap_or(0);
            format!("{major}.{minor}")
        } else {
            "unknown".to_string()
        };
        
        // Extract target architecture
        let target_regex = Regex::new(r"\.target\s+([\w\.]+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let target = if let Some(captures) = target_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().to_string()
        } else {
            "unknown".to_string()
        };
        
        // Extract addressing mode
        let address_size_regex = Regex::new(r"\.address_size\s+(\d+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let address_size = if let Some(captures) = address_size_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0)
        } else {
            0
        };
        
        Ok(serde_json::json!({
            "ptx_version": version,
            "target_architecture": target,
            "address_size": address_size,
        }))
    }
}


