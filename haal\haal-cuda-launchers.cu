// haal-cuda-launchers.cu
/**
 * # HAAL CUDA Kernel Launchers
 * 
 * @brief Wrapper functions that launch the high-performance CUDA kernels
 * defined in haal-cuda.cu. Provides C-compatible interface for orchestrator.
 *
 *▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <iostream>

// CUDA Error Checking Macro
#define CUDA_CHECK(call)                                                  \
    do                                                                    \
    {                                                                     \
        cudaError_t error = call;                                         \
        if (error != cudaSuccess)                                         \
        {                                                                 \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__  \
                      << " - " << cudaGetErrorString(error) << std::endl; \
        }                                                                 \
    } while (0)

// Forward declarations of kernels from haal-cuda.cu
extern "C" {
    __global__ void xOneTensorCoreKernel(half *__restrict__ data, int size, int iterations);
    __global__ void xOnePersistentKernel(float *__restrict__ data, int size, int iterations, int total_blocks);
    __global__ void xOneVectorOptimizedKernel(half2 *__restrict__ data, int size, int iterations);
    __global__ void xOneRegisterSaturationKernel(float *__restrict__ data, int size, int iterations);
    __global__ void xOneRegisterOptimizedKernel(float *__restrict__ data, int size, int iterations);
}

extern "C" {

// CUDA device availability check
bool checkCudaDeviceAvailability() {
    int deviceCount = 0;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    
    if (error != cudaSuccess) {
        std::cerr << "CUDA error getting device count: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    if (deviceCount == 0) {
        std::cout << "No CUDA devices found" << std::endl;
        return false;
    }
    
    // Check if at least one device has compute capability 3.5 or higher
    for (int i = 0; i < deviceCount; ++i) {
        cudaDeviceProp prop;
        if (cudaGetDeviceProperties(&prop, i) == cudaSuccess) {
            if (prop.major >= 3 && prop.minor >= 5) {
                std::cout << "✅ CUDA device " << i << ": " << prop.name 
                          << " (Compute " << prop.major << "." << prop.minor << ")" << std::endl;
                return true;
            }
        }
    }
    
    std::cout << "No suitable CUDA devices found (need compute capability 3.5+)" << std::endl;
    return false;
}

// CUDA context initialization
bool initializeCudaContext() {
    cudaError_t error = cudaSetDevice(0);
    if (error != cudaSuccess) {
        std::cerr << "CUDA error setting device: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    
    // Warm up the CUDA context
    void* dummy;
    error = cudaMalloc(&dummy, 1);
    if (error != cudaSuccess) {
        std::cerr << "CUDA error allocating memory: " << cudaGetErrorString(error) << std::endl;
        return false;
    }
    cudaFree(dummy);
    
    std::cout << "✅ CUDA context initialized" << std::endl;
    return true;
}

// CUDA context cleanup
void cleanupCudaContext() {
    cudaDeviceReset();
    std::cout << "🧹 CUDA context cleaned up" << std::endl;
}

// Launcher for Tensor Core kernel
void launchTensorCoreKernel(void* data, int size, int iterations) {
    // Tensor Core kernel operates on half precision data
    half* half_data = static_cast<half*>(data);
    
    // WMMA operates on 16x16 matrices
    int tensor_size = size / 16;
    
    int blockSize = 256;
    int gridSize = (tensor_size + blockSize - 1) / blockSize;
    
    xOneTensorCoreKernel<<<gridSize, blockSize>>>(half_data, tensor_size * 16, iterations);
    CUDA_CHECK(cudaGetLastError());
}

// Launcher for Persistent Thread kernel
void launchPersistentKernel(float* data, int size, int iterations, int total_blocks) {
    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;
    
    // Use the provided total_blocks or default to gridSize * 4
    int persistent_blocks = (total_blocks > 0) ? total_blocks : gridSize * 4;
    
    xOnePersistentKernel<<<gridSize, blockSize>>>(data, size, iterations, persistent_blocks);
    CUDA_CHECK(cudaGetLastError());
}

// Launcher for Vector Optimized kernel (FP16)
void launchVectorOptimizedKernel(void* data, int size, int iterations) {
    // Vector kernel operates on half2 data
    half2* half2_data = static_cast<half2*>(data);
    
    // half2 uses 1/2 the elements
    int vector_size = size / 2;
    
    int blockSize = 256;
    int gridSize = (vector_size + blockSize - 1) / blockSize;
    
    xOneVectorOptimizedKernel<<<gridSize, blockSize>>>(half2_data, vector_size, iterations);
    CUDA_CHECK(cudaGetLastError());
}

// Launcher for Register Saturation kernel
void launchRegisterSaturationKernel(float* data, int size, int iterations) {
    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;
    
    xOneRegisterSaturationKernel<<<gridSize, blockSize>>>(data, size, iterations);
    CUDA_CHECK(cudaGetLastError());
}

// Launcher for Register Optimized kernel
void launchRegisterOptimizedKernel(float* data, int size, int iterations) {
    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;
    
    xOneRegisterOptimizedKernel<<<gridSize, blockSize>>>(data, size, iterations);
    CUDA_CHECK(cudaGetLastError());
}

} // extern "C"
