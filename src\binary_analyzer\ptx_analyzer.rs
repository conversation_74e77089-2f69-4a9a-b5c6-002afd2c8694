// src/binary_analyzer/ptx_analyzer.rs
//! PTX analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing NVIDIA PTX files and extracting
//! metadata such as kernel functions, memory usage, and launch parameters.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs;
use regex::Regex;

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, FunctionSignature, TypeInfo, CallingConvention};

/// PTX analyzer
pub struct PTXAnalyzer {
    // Configuration options can be added here
}

impl Default for PTXAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl PTXAnalyzer {
    /// Create a new PTX analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a PTX file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing PTX file: {}", path.display());
        
        // Read the PTX file
        let ptx_content = fs::read_to_string(path)?;
        
        // Extract kernel functions
        let exports = self.extract_kernels(&ptx_content)?;
        
        // PTX files don't have traditional imports
        let imports = Vec::new();
        
        // PTX files don't have traditional dependencies
        let dependencies = Vec::new();
        
        // Extract additional metadata
        let additional_metadata = self.extract_additional_metadata(&ptx_content)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::PTX,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract kernel functions from the PTX content
    fn extract_kernels(&self, ptx_content: &str) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        // Regular expression to match kernel function declarations
        // Format: .entry kernel_name(param_list)
        let kernel_regex = Regex::new(r"\.entry\s+(\w+)\s*\(([^)]*)\)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        // Regular expression to match parameter declarations
        // Format: .param .type .ptr .align X .space .param_space param_name
        let param_regex = Regex::new(r"\.param\s+(\.\w+)(?:\s+\.ptr)?(?:\s+\.align\s+(\d+))?(?:\s+\.space\s+\.(\w+))?\s+(\w+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        // Find all kernel declarations
        for captures in kernel_regex.captures_iter(ptx_content) {
            let kernel_name = captures.get(1).unwrap().as_str().to_string();
            let param_list = captures.get(2).unwrap().as_str();
            
            // Parse parameters
            let mut parameter_types = Vec::new();
            for param in param_list.split(',') {
                if let Some(param_captures) = param_regex.captures(param.trim()) {
                    let type_name = param_captures.get(1).unwrap().as_str().to_string();
                    let alignment = param_captures.get(2).map(|m| m.as_str().parse::<usize>().unwrap_or(0));
                    let _space = param_captures.get(3).map(|m| m.as_str().to_string());
                    let _param_name = param_captures.get(4).unwrap().as_str().to_string();
                    
                    parameter_types.push(TypeInfo {
                        name: type_name,
                        size: None, // Cannot determine size from PTX alone
                        alignment,
                        is_pointer: param.contains(".ptr"),
                        is_array: false,
                        array_dimensions: Vec::new(),
                    });
                }
            }
            
            // Create function signature
            let signature = FunctionSignature {
                return_type: TypeInfo {
                    name: ".void".to_string(),
                    size: Some(0),
                    alignment: Some(0),
                    is_pointer: false,
                    is_array: false,
                    array_dimensions: Vec::new(),
                },
                parameter_types,
                is_variadic: false,
            };
            
            // Extract shared memory usage
            let shared_memory = self.extract_shared_memory(ptx_content, &kernel_name);
            
            // Extract register usage
            let register_count = self.extract_register_count(ptx_content, &kernel_name);
            
            // Create exported function
            exports.push(ExportedFunction {
                name: kernel_name.clone(),
                address: 0, // PTX doesn't have addresses
                signature: Some(signature),
                calling_convention: Some(CallingConvention::CudaKernel),
                metadata: serde_json::json!({
                    "shared_memory": shared_memory,
                    "register_count": register_count,
                    "is_kernel": true,
                }),
            });
        }
        
        Ok(exports)
    }
    
    /// Extract shared memory usage for a kernel
    fn extract_shared_memory(&self, ptx_content: &str, kernel_name: &str) -> Option<usize> {
        // Find all shared memory declarations within the kernel
        let shared_mem_regex = Regex::new(&format!(r"{}[\s\S]+?\.shared\s+\.align\s+\d+\s+\.b8\s+\w+\[(\d+)\]", regex::escape(kernel_name))).ok()?;
        
        let mut total_shared_mem = 0;
        for captures in shared_mem_regex.captures_iter(ptx_content) {
            if let Some(size_match) = captures.get(1) {
                if let Ok(size) = size_match.as_str().parse::<usize>() {
                    total_shared_mem += size;
                }
            }
        }
        
        if total_shared_mem > 0 {
            Some(total_shared_mem)
        } else {
            None
        }
    }
    
    /// Extract register count for a kernel
    fn extract_register_count(&self, ptx_content: &str, kernel_name: &str) -> Option<usize> {
        // Regular expression to match register count
        // Format: // Function requires X registers
        let reg_regex = Regex::new(&format!(r"// Function {}.*?requires\s+(\d+)\s+registers", regex::escape(kernel_name))).ok()?;
        
        if let Some(captures) = reg_regex.captures(ptx_content) {
            if let Some(count_match) = captures.get(1) {
                return count_match.as_str().parse::<usize>().ok();
            }
        }
        
        None
    }
    
    /// Extract additional metadata from the PTX content
    fn extract_additional_metadata(&self, ptx_content: &str) -> OmniResult<serde_json::Value> {
        // Extract PTX version
        let version_regex = Regex::new(r"\.version\s+(\d+)\.(\d+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let version = if let Some(captures) = version_regex.captures(ptx_content) {
            let major = captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0);
            let minor = captures.get(2).unwrap().as_str().parse::<u32>().unwrap_or(0);
            format!("{major}.{minor}")
        } else {
            "unknown".to_string()
        };
        
        // Extract target architecture
        let target_regex = Regex::new(r"\.target\s+([\w\.]+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let target = if let Some(captures) = target_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().to_string()
        } else {
            "unknown".to_string()
        };
        
        // Extract addressing mode
        let address_size_regex = Regex::new(r"\.address_size\s+(\d+)")
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to compile regex: {e}")))?;
        
        let address_size = if let Some(captures) = address_size_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0)
        } else {
            0
        };
        
        Ok(serde_json::json!({
            "ptx_version": version,
            "target_architecture": target,
            "address_size": address_size,
        }))
    }
}
