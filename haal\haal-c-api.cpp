// haal-c-api.cpp
/**
 * # HAAL C API Wrapper
 * 
 * @brief C API wrapper for Rust FFI integration with the C++ HAAL orchestrator.
 * Provides a C-compatible interface for cross-language integration.
 *
 *▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

#include "include/haal-orc.hpp"
#include <cstring>
#include <map>
#include <memory>

// Forward declaration for CUDA availability check
bool checkCudaDeviceAvailability() {
#ifndef CUDA_DISABLED
    // Create a temporary CUDA backend to check availability
    CUDABackend tempBackend;
    return tempBackend.checkCudaAvailability();
#else
    return false;
#endif
}

extern "C" {

// C-compatible structs for FFI
typedef struct {
    int data_size;
    double compute_intensity;
    double parallelizability;
    double cache_locality_index;
    double expected_duration;
} HaalTaskCharacteristics;

typedef struct {
    char task_id[64];
    double execution_time;
    char execution_path[32];
    bool success;
    char error_message[256];
    double operation_latency;
    double throughput_gflops;
} HaalExecutionResult;

// Create orchestrator instance
void* haal_orchestrator_create() {
    try {
        auto* orchestrator = new HaalOrchestrator();
        return static_cast<void*>(orchestrator);
    } catch (...) {
        return nullptr;
    }
}

// Initialize orchestrator
bool haal_orchestrator_initialize(void* orchestrator_ptr) {
    if (!orchestrator_ptr) return false;
    
    try {
        auto* orchestrator = static_cast<HaalOrchestrator*>(orchestrator_ptr);
        return orchestrator->initialize();
    } catch (...) {
        return false;
    }
}

// Execute computation
void* haal_orchestrator_execute(
    void* orchestrator_ptr,
    unsigned int operation,
    float* data,
    float* auxiliary,
    void* params,
    const void* characteristics_ptr
) {
    if (!orchestrator_ptr || !data || !characteristics_ptr) {
        return nullptr;
    }
    
    try {
        auto* orchestrator = static_cast<HaalOrchestrator*>(orchestrator_ptr);
        auto* c_chars = static_cast<const HaalTaskCharacteristics*>(characteristics_ptr);
        
        // Convert C characteristics to C++ struct
        TaskCharacteristics characteristics;
        characteristics.dataSize = c_chars->data_size;
        characteristics.computeIntensity = c_chars->compute_intensity;
        characteristics.parallelizability = c_chars->parallelizability;
        characteristics.cacheLocalityIndex = c_chars->cache_locality_index;
        characteristics.expectedDuration = c_chars->expected_duration;
        
        // Convert operation enum
        AVX2Operation op = static_cast<AVX2Operation>(operation);
        
        // Execute computation
        auto cpp_result = orchestrator->executeComputation(
            op, data, auxiliary, params, characteristics
        );
        
        // Convert result to C struct
        auto* c_result = new HaalExecutionResult();
        strncpy(c_result->task_id, cpp_result.taskId.c_str(), sizeof(c_result->task_id) - 1);
        c_result->execution_time = cpp_result.executionTime;
        strncpy(c_result->execution_path, cpp_result.executionPath.c_str(), sizeof(c_result->execution_path) - 1);
        c_result->success = cpp_result.success;
        
        if (!cpp_result.errorMessage.empty()) {
            strncpy(c_result->error_message, cpp_result.errorMessage.c_str(), sizeof(c_result->error_message) - 1);
        } else {
            c_result->error_message[0] = '\0';
        }
        
        c_result->operation_latency = cpp_result.performanceMetrics.operationLatency;
        c_result->throughput_gflops = cpp_result.performanceMetrics.throughputGFLOPS;
        
        return static_cast<void*>(c_result);
        
    } catch (...) {
        return nullptr;
    }
}

// Get system metrics (simplified)
void* haal_orchestrator_get_metrics(void* orchestrator_ptr) {
    if (!orchestrator_ptr) return nullptr;
    
    try {
        auto* orchestrator = static_cast<HaalOrchestrator*>(orchestrator_ptr);
        auto metrics = orchestrator->getSystemMetrics();
        
        // For simplicity, just return a copy of the metrics map
        auto* metrics_copy = new std::map<std::string, double>(metrics);
        return static_cast<void*>(metrics_copy);
        
    } catch (...) {
        return nullptr;
    }
}

// Cleanup orchestrator
void haal_orchestrator_cleanup(void* orchestrator_ptr) {
    if (!orchestrator_ptr) return;
    
    try {
        auto* orchestrator = static_cast<HaalOrchestrator*>(orchestrator_ptr);
        orchestrator->cleanup();
    } catch (...) {
        // Ignore cleanup errors
    }
}

// Destroy orchestrator
void haal_orchestrator_destroy(void* orchestrator_ptr) {
    if (!orchestrator_ptr) return;
    
    try {
        auto* orchestrator = static_cast<HaalOrchestrator*>(orchestrator_ptr);
        delete orchestrator;
    } catch (...) {
        // Ignore destruction errors
    }
}

// Cleanup result
void haal_result_cleanup(void* result_ptr) {
    if (!result_ptr) return;
    
    try {
        auto* result = static_cast<HaalExecutionResult*>(result_ptr);
        delete result;
    } catch (...) {
        // Ignore cleanup errors
    }
}

// Cleanup metrics
void haal_metrics_cleanup(void* metrics_ptr) {
    if (!metrics_ptr) return;
    
    try {
        auto* metrics = static_cast<std::map<std::string, double>*>(metrics_ptr);
        delete metrics;
    } catch (...) {
        // Ignore cleanup errors
    }
}

// Utility: Check CUDA availability
bool haal_check_cuda_available() {
    return checkCudaDeviceAvailability();
}

// Utility: Get backend count
int haal_get_backend_count() {
    return checkCudaDeviceAvailability() ? 3 : 1; // AVX2 + CUDA + Hybrid or just AVX2
}

} // extern "C"
