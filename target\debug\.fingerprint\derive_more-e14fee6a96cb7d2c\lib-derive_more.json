{"rustc": 1842507548689473721, "features": "[\"add\", \"add_assign\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"into\", \"mul\", \"not\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 17818141490371658307, "path": 9599848085689692436, "deps": [[15774985133158646067, "derive_more_impl", false, 7716001881651340573]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-e14fee6a96cb7d2c\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}