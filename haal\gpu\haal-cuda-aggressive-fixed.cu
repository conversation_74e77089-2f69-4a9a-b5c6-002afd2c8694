// haal-cuda-aggressive-fixed.cu - FIXED ULTRA-AGGRESSIVE CUDA Performance Benchmark
/**
 * 🚀 FIXED ULTRA-AGGRESSIVE Multi-kernel CUDA benchmark with proper memory management
 * for RTX 4080 laptop - targeting 200+ TFLOPS across all kernels
 *
 * FIXES APPLIED:
 * ✅ Proper bounds checking for all kernels
 * ✅ Reasonable memory allocation (512 MB vs 1 GB)
 * ✅ Fixed tensor core memory access patterns
 * ✅ Safe memory alignment and access
 *
 * AGGRESSIVE OPTIMIZATIONS:
 * 🔥 10x Higher Computational Intensity
 * ⚡ Maximum Register Utilization per kernel
 * 💥 Massive Loop Unrolling (32x-64x)
 * 🧠 Warp-Level Cooperation & Shuffle Operations
 */

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <mma.h>
#include <cooperative_groups.h>
#include <iostream>
#include <chrono>
#include <thread>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <cmath>
#include <cfloat>

using namespace nvcuda;
namespace cg = cooperative_groups;

#define CUDA_CHECK(call)                                                  \
    do                                                                    \
    {                                                                     \
        cudaError_t error = call;                                         \
        if (error != cudaSuccess)                                         \
        {                                                                 \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__  \
                      << " - " << cudaGetErrorString(error) << std::endl; \
            exit(1);                                                      \
        }                                                                 \
    } while (0)

// ============================================================================
// FIXED TENSOR CORE KERNEL - Proper bounds checking
// ============================================================================
__global__ void xOneTensorCoreKernel(half *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int matrices_count = size / 256; // Each matrix is 16x16 = 256 elements
    
    if (idx >= matrices_count) return;

    wmma::fragment<wmma::matrix_a, 16, 16, 16, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, 16, 16, 16, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, 16, 16, 16, half> acc_frag;

    wmma::fill_fragment(acc_frag, __float2half(1.0f));
    wmma::fill_fragment(a_frag, __float2half(0.9f + static_cast<float>(idx % 100) / 1000.0f));
    wmma::fill_fragment(b_frag, __float2half(1.1f + static_cast<float>(idx % 97) / 1000.0f));

    #pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        for (int j = 0; j < a_frag.num_elements; ++j)
        {
            a_frag.x[j] = __hfma(a_frag.x[j], __float2half(1.0001f), __float2half(0.0001f));
        }
        for (int j = 0; j < b_frag.num_elements; ++j)
        {
            b_frag.x[j] = __hfma(b_frag.x[j], __float2half(0.9999f), __float2half(0.0001f));
        }
    }

    int matrix_offset = idx * 256;
    if (matrix_offset + 255 < size)
    {
        wmma::store_matrix_sync(data + matrix_offset, acc_frag, 16, wmma::mem_row_major);
    }
}

// ============================================================================
// 🚀 FIXED AGGRESSIVE PERSISTENT KERNEL (Target: 50-80 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(512, 2)
xOneAggressivePersistentKernel(float *__restrict__ data, int size, int iterations, int total_blocks)
{
    cg::thread_block block = cg::this_thread_block();
    cg::thread_block_tile<32> warp = cg::tiled_partition<32>(block);

    int block_id = blockIdx.x;
    int thread_id = threadIdx.x;
    int threads_per_block = blockDim.x;

    // 🔥 64 accumulator chains (vs 16 original)
    register float acc[64];
    
    for (int persistent_block = block_id; persistent_block < total_blocks; persistent_block += gridDim.x)
    {
        int base_idx = persistent_block * threads_per_block + thread_id;
        if (base_idx >= size) continue;

        float x = data[base_idx];

        // Initialize 64 accumulator chains
        #pragma unroll 64
        for (int i = 0; i < 64; ++i)
        {
            acc[i] = x * (0.1f + static_cast<float>(i) * 0.015625f);
        }

        // 💥 BEAST MODE: 3x iteration count with 64 parallel chains (reduced from 5x for stability)
        #pragma unroll 16
        for (int iter = 0; iter < iterations * 3; ++iter)
        {
            // Warp shuffle every 25 iterations
            if (iter % 25 == 0)
            {
                float shared_val = warp.shfl_xor(x, iter & 31);
                x = __fmaf_rn(x, 0.9999f, shared_val * 0.0001f);
            }

            // 64 parallel FMA chains
            #pragma unroll 64
            for (int i = 0; i < 64; ++i)
            {
                acc[i] = __fmaf_rn(acc[i], x, (1.00005f + static_cast<float>(i) * 0.000001f));
            }

            // Cross-chain dependencies every 16 iterations
            if ((iter & 15) == 0)
            {
                #pragma unroll 32
                for (int i = 1; i < 64; i += 2)
                {
                    acc[i] = __fmaf_rn(acc[i], acc[i-1], 0.0000001f);
                }
            }
        }

        // Warp-level reduction
        float result = 0.0f;
        #pragma unroll 64
        for (int i = 0; i < 64; ++i)
        {
            result += acc[i];
        }

        #pragma unroll 5
        for (int mask = 16; mask > 0; mask >>= 1) 
        {
            result += warp.shfl_xor(result, mask);
        }

        data[base_idx] = result;
    }
}

// ============================================================================
// ⚡ FIXED AGGRESSIVE VECTOR KERNEL (Target: 80-120 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(1024, 1)
xOneAggressiveVectorKernel(half2 *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    half2 x = data[idx];
    
    // 🔥 32 accumulator pairs (vs 2 original)
    register half2 acc[32];
    
    #pragma unroll 32
    for (int i = 0; i < 32; ++i)
    {
        float scale = 0.1f + i * 0.03125f;
        acc[i] = __hmul2(x, __float2half2_rn(scale));
    }

    // 💥 BEAST MODE: 5x iteration count with 32 parallel chains (reduced from 8x for stability)
    #pragma unroll 32
    for (int iter = 0; iter < iterations * 5; iter += 4)
    {
        // 4x unrolled iterations with 32 parallel FMA chains
        #pragma unroll 4
        for (int unroll = 0; unroll < 4; ++unroll)
        {
            const half2 coeff_base = __float2half2_rn(1.0001f + unroll * 0.0001f);
            const half2 coeff_alt = __float2half2_rn(0.9999f - unroll * 0.0001f);
            
            #pragma unroll 32
            for (int i = 0; i < 32; ++i)
            {
                half2 coeff = (i & 1) ? coeff_alt : coeff_base;
                acc[i] = __hfma2(acc[i], x, coeff);
            }
        }

        // Cross-accumulator operations every 16 iterations
        if ((iter & 15) == 0)
        {
            #pragma unroll 16
            for (int i = 1; i < 32; i += 2)
            {
                acc[i] = __hfma2(acc[i], acc[i-1], __float2half2_rn(0.0001f));
            }
        }
    }

    // Reduction of all 32 accumulators
    half2 result = __float2half2_rn(0.0f);
    #pragma unroll 32
    for (int i = 0; i < 32; ++i)
    {
        result = __hadd2(result, acc[i]);
    }

    data[idx] = result;
}

// ============================================================================
// 💥 FIXED ULTIMATE REGISTER SATURATION (Target: 150-200 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(256, 1)
xOneUltimateRegisterSaturationKernel(float *__restrict__ data, int size, int iterations)
{
    const int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= size) return;

    // 💥 96 registers (balanced for stability) organized in 24x4 matrix
    register float r[24][4]; // 96 registers total

    const float base_val = data[tid];

    // Initialize all 96 registers
    #pragma unroll 24
    for (int i = 0; i < 24; i++)
    {
        const float scale_base = 0.042f + (i * 0.042f);
        r[i][0] = base_val * scale_base;
        r[i][1] = base_val * (scale_base + 0.25f);
        r[i][2] = base_val * (scale_base + 0.50f);
        r[i][3] = base_val * (scale_base + 0.75f);
    }

    // 96 precomputed FMA constants
    const float fma_constants[96] = {
        // First 48 constants (increasing)
        1.000001f, 1.000002f, 1.000003f, 1.000004f, 1.000005f, 1.000006f, 1.000007f, 1.000008f,
        1.000009f, 1.000010f, 1.000011f, 1.000012f, 1.000013f, 1.000014f, 1.000015f, 1.000016f,
        1.000017f, 1.000018f, 1.000019f, 1.000020f, 1.000021f, 1.000022f, 1.000023f, 1.000024f,
        1.000025f, 1.000026f, 1.000027f, 1.000028f, 1.000029f, 1.000030f, 1.000031f, 1.000032f,
        1.000033f, 1.000034f, 1.000035f, 1.000036f, 1.000037f, 1.000038f, 1.000039f, 1.000040f,
        1.000041f, 1.000042f, 1.000043f, 1.000044f, 1.000045f, 1.000046f, 1.000047f, 1.000048f,
        // Last 48 constants (decreasing)
        0.999999f, 0.999998f, 0.999997f, 0.999996f, 0.999995f, 0.999994f, 0.999993f, 0.999992f,
        0.999991f, 0.999990f, 0.999989f, 0.999988f, 0.999987f, 0.999986f, 0.999985f, 0.999984f,
        0.999983f, 0.999982f, 0.999981f, 0.999980f, 0.999979f, 0.999978f, 0.999977f, 0.999976f,
        0.999975f, 0.999974f, 0.999973f, 0.999972f, 0.999971f, 0.999970f, 0.999969f, 0.999968f,
        0.999967f, 0.999966f, 0.999965f, 0.999964f, 0.999963f, 0.999962f, 0.999961f, 0.999960f,
        0.999959f, 0.999958f, 0.999957f, 0.999956f, 0.999955f, 0.999954f, 0.999953f, 0.999952f
    };

    // 🔥 BEAST MODE: 3x iteration count with all 96 registers (reduced from 4x for stability)
    for (int iter = 0; iter < iterations * 3; iter++)
    {
        // 96 parallel FMA operations
        #pragma unroll 6
        for (int batch = 0; batch < 6; batch++)
        {
            const int reg_base = batch * 4;
            const float k1 = fma_constants[batch * 4];
            const float k2 = fma_constants[batch * 4 + 1];
            const float k3 = fma_constants[batch * 4 + 48];
            const float k4 = fma_constants[batch * 4 + 49];

            #pragma unroll 4
            for (int q = 0; q < 4 && (reg_base + q) < 24; q++)
            {
                r[reg_base + q][0] = __fmaf_rn(r[reg_base + q][0], k1, 0.00000001f);
                r[reg_base + q][1] = __fmaf_rn(r[reg_base + q][1], k2, 0.00000002f);
                r[reg_base + q][2] = __fmaf_rn(r[reg_base + q][2], k3, 0.00000003f);
                r[reg_base + q][3] = __fmaf_rn(r[reg_base + q][3], k4, 0.00000004f);
            }
        }

        // Cross-register dependencies every 4 iterations
        if ((iter & 0x3) == 0)
        {
            #pragma unroll 3
            for (int i = 1; i < 24; i += 8)
            {
                r[i][0] += r[i - 1][3] * 0.0000001f;
                r[i][1] += r[i - 1][2] * 0.0000001f;
                if (i + 1 < 24) {
                    r[i + 1][2] += r[i - 1][1] * 0.0000001f;
                    r[i + 1][3] += r[i - 1][0] * 0.0000001f;
                }
            }
        }

        // Complex operations every 8 iterations
        if ((iter & 0x7) == 0)
        {
            #pragma unroll 3
            for (int complex_batch = 0; complex_batch < 3; complex_batch++)
            {
                const int idx = complex_batch * 8 + 1;
                if (idx < 24)
                {
                    r[idx][0] = __fmaf_rn(r[idx][0], sqrtf(fabsf(r[idx][1]) + 1.0f), 0.0000001f);
                    r[idx][1] = __fmaf_rn(r[idx][1], 1.0f / (fabsf(r[idx][2]) + 1.0f), 0.0000001f);
                }
            }
        }
    }

    // Optimized reduction with all 96 registers
    float quad_sums[6] = {0.0f};

    #pragma unroll 6
    for (int quad = 0; quad < 6; quad++)
    {
        #pragma unroll 4
        for (int i = quad * 4; i < (quad + 1) * 4 && i < 24; i++)
        {
            quad_sums[quad] += r[i][0] + r[i][1] + r[i][2] + r[i][3];
        }
    }

    float result = (quad_sums[0] * quad_sums[1]) + (quad_sums[2] * quad_sums[3]) + (quad_sums[4] * quad_sums[5]);
    data[tid] = result;
}

// ============================================================================
// 🔥 FIXED ULTIMATE REGISTER OPTIMIZED (Target: 200-300 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(512, 1)
xOneUltimateRegisterOptimizedKernel(float *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    float x = data[idx];

    // 💥 48 register variables (balanced for stability) for ultimate ILP
    register float r0 = x, r1 = x * 0.98f, r2 = x * 0.96f, r3 = x * 0.94f;
    register float r4 = x * 0.92f, r5 = x * 0.90f, r6 = x * 0.88f, r7 = x * 0.86f;
    register float r8 = x * 0.84f, r9 = x * 0.82f, r10 = x * 0.80f, r11 = x * 0.78f;
    register float r12 = x * 0.76f, r13 = x * 0.74f, r14 = x * 0.72f, r15 = x * 0.70f;
    register float r16 = x * 0.68f, r17 = x * 0.66f, r18 = x * 0.64f, r19 = x * 0.62f;
    register float r20 = x * 0.60f, r21 = x * 0.58f, r22 = x * 0.56f, r23 = x * 0.54f;
    register float r24 = x * 0.52f, r25 = x * 0.50f, r26 = x * 0.48f, r27 = x * 0.46f;
    register float r28 = x * 0.44f, r29 = x * 0.42f, r30 = x * 0.40f, r31 = x * 0.38f;
    
    // Additional 16 registers for maximum parallelism
    register float r32 = x * 1.02f, r33 = x * 1.04f, r34 = x * 1.06f, r35 = x * 1.08f;
    register float r36 = x * 1.10f, r37 = x * 1.12f, r38 = x * 1.14f, r39 = x * 1.16f;
    register float r40 = x * 1.18f, r41 = x * 1.20f, r42 = x * 1.22f, r43 = x * 1.24f;
    register float r44 = x * 1.26f, r45 = x * 1.28f, r46 = x * 1.30f, r47 = x * 1.32f;

    // 🔥 BEAST MODE: 4x iteration count with all 48 registers (reduced from 6x for stability)
    #pragma unroll 16
    for (int i = 0; i < iterations * 4; ++i)
    {
        // 48 parallel FMA operations
        r0 = __fmaf_rn(r0, x, 1.0001f);   r1 = __fmaf_rn(r1, x, 0.9999f);
        r2 = __fmaf_rn(r2, x, 1.0002f);   r3 = __fmaf_rn(r3, x, 0.9998f);
        r4 = __fmaf_rn(r4, x, 1.0003f);   r5 = __fmaf_rn(r5, x, 0.9997f);
        r6 = __fmaf_rn(r6, x, 1.0004f);   r7 = __fmaf_rn(r7, x, 0.9996f);
        r8 = __fmaf_rn(r8, x, 1.0005f);   r9 = __fmaf_rn(r9, x, 0.9995f);
        r10 = __fmaf_rn(r10, x, 1.0006f); r11 = __fmaf_rn(r11, x, 0.9994f);
        r12 = __fmaf_rn(r12, x, 1.0007f); r13 = __fmaf_rn(r13, x, 0.9993f);
        r14 = __fmaf_rn(r14, x, 1.0008f); r15 = __fmaf_rn(r15, x, 0.9992f);
        r16 = __fmaf_rn(r16, x, 1.0009f); r17 = __fmaf_rn(r17, x, 0.9991f);
        r18 = __fmaf_rn(r18, x, 1.0010f); r19 = __fmaf_rn(r19, x, 0.9990f);
        r20 = __fmaf_rn(r20, x, 1.0011f); r21 = __fmaf_rn(r21, x, 0.9989f);
        r22 = __fmaf_rn(r22, x, 1.0012f); r23 = __fmaf_rn(r23, x, 0.9988f);
        r24 = __fmaf_rn(r24, x, 1.0013f); r25 = __fmaf_rn(r25, x, 0.9987f);
        r26 = __fmaf_rn(r26, x, 1.0014f); r27 = __fmaf_rn(r27, x, 0.9986f);
        r28 = __fmaf_rn(r28, x, 1.0015f); r29 = __fmaf_rn(r29, x, 0.9985f);
        r30 = __fmaf_rn(r30, x, 1.0016f); r31 = __fmaf_rn(r31, x, 0.9984f);
        
        // Additional 16 FMA operations
        r32 = __fmaf_rn(r32, x, 1.0017f); r33 = __fmaf_rn(r33, x, 0.9983f);
        r34 = __fmaf_rn(r34, x, 1.0018f); r35 = __fmaf_rn(r35, x, 0.9982f);
        r36 = __fmaf_rn(r36, x, 1.0019f); r37 = __fmaf_rn(r37, x, 0.9981f);
        r38 = __fmaf_rn(r38, x, 1.0020f); r39 = __fmaf_rn(r39, x, 0.9980f);
        r40 = __fmaf_rn(r40, x, 1.0021f); r41 = __fmaf_rn(r41, x, 0.9979f);
        r42 = __fmaf_rn(r42, x, 1.0022f); r43 = __fmaf_rn(r43, x, 0.9978f);
        r44 = __fmaf_rn(r44, x, 1.0023f); r45 = __fmaf_rn(r45, x, 0.9977f);
        r46 = __fmaf_rn(r46, x, 1.0024f); r47 = __fmaf_rn(r47, x, 0.9976f);
    }

    // Reduction tree with all 48 registers
    float result = 
        ((r0 + r1 + r2 + r3) * (r4 + r5 + r6 + r7)) +
        ((r8 + r9 + r10 + r11) * (r12 + r13 + r14 + r15)) +
        ((r16 + r17 + r18 + r19) * (r20 + r21 + r22 + r23)) +
        ((r24 + r25 + r26 + r27) * (r28 + r29 + r30 + r31)) +
        ((r32 + r33 + r34 + r35) * (r36 + r37 + r38 + r39)) +
        ((r40 + r41 + r42 + r43) * (r44 + r45 + r46 + r47));

    data[idx] = result;
}

// FIXED BENCHMARK FUNCTION with proper error handling
double runSingleKernelBenchmark(const char *name, int kernelType, void *d_data,
                                int size, int iterations, int testRuns)
{
    std::cout << "=== " << name << " ===" << std::endl;

    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;

    // Handle special cases with proper bounds checking
    if (kernelType == 1) {
        int matrices_count = size / 256; // Each WMMA matrix = 256 elements
        gridSize = (matrices_count + blockSize - 1) / blockSize;
        std::cout << "Matrices: " << matrices_count << std::endl;
    }
    else if (kernelType == 2) {
        blockSize = 512;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 3) {
        size = size / 2; // half2
        blockSize = 1024;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 4) {
        blockSize = 256;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 5) {
        blockSize = 512;
        gridSize = (size + blockSize - 1) / blockSize;
    }

    std::cout << "Elements: " << size << std::endl;
    std::cout << "Grid: " << gridSize << " blocks x " << blockSize << " threads" << std::endl;
    std::cout << std::endl;

    // Warmup
    switch (kernelType)
    {
    case 1:
        xOneTensorCoreKernel<<<gridSize, blockSize>>>((half *)d_data, size * 256, iterations);
        break;
    case 2:
    {
        int total_blocks = gridSize * 4;
        xOneAggressivePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
        break;
    }
    case 3:
        xOneAggressiveVectorKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
        break;
    case 4:
        xOneUltimateRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    case 5:
        xOneUltimateRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    }
    CUDA_CHECK(cudaDeviceSynchronize());

    double totalTime = 0.0;

    for (int run = 0; run < testRuns; ++run)
    {
        auto start = std::chrono::high_resolution_clock::now();

        switch (kernelType)
        {
        case 1:
            xOneTensorCoreKernel<<<gridSize, blockSize>>>((half *)d_data, size * 256, iterations);
            break;
        case 2:
        {
            int total_blocks = gridSize * 4;
            xOneAggressivePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
            break;
        }
        case 3:
            xOneAggressiveVectorKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
            break;
        case 4:
            xOneUltimateRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        case 5:
            xOneUltimateRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        }

        CUDA_CHECK(cudaDeviceSynchronize());

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double milliseconds = duration.count() / 1000.0;

        totalTime += milliseconds;
        std::cout << "Run " << (run + 1) << ": "
                  << std::fixed << std::setprecision(2) << milliseconds << " ms" << std::endl;
    }

    return totalTime;
}

// Main benchmark with FIXED memory allocation
int main()
{
    std::cout << "🚀 FIXED ULTRA-AGGRESSIVE CUDA Performance Benchmark Suite - RTX 4080" << std::endl;
    std::cout << "=======================================================================" << std::endl;
    std::cout << "TARGET: 200+ TFLOPS Performance Evaluation" << std::endl;
    std::cout << "=======================================================================" << std::endl;

    const int size = 128 * 1024 * 1024; // FIXED: 128M elements (512 MB) - more reasonable
    const int iterations = 1500;         // BALANCED: aggressive but stable
    const int testRuns = 3;

    std::cout << "\n🔥 FIXED Benchmark Configuration:" << std::endl;
    std::cout << "  Array Size: " << size << " elements ("
              << (size * sizeof(float)) / (1024 * 1024) << " MB)" << std::endl;
    std::cout << "  Iterations: " << iterations << std::endl;
    std::cout << "  Test Runs: " << testRuns << std::endl;

    // Memory allocation
    half *h_half_data = new half[size];
    float *h_float_data = new float[size];
    half2 *h_half2_data = new half2[size / 2];

    // Initialize data
    for (int i = 0; i < size; i++)
    {
        h_half_data[i] = __float2half(1.0f + (i % 1000) * 0.0001f);
        h_float_data[i] = 1.0f + (i % 1000) * 0.0001f;
        if (i < size / 2)
        {
            h_half2_data[i] = __float2half2_rn(1.0f + (i % 1000) * 0.0001f);
        }
    }

    half *d_half_data;
    float *d_float_data;
    half2 *d_half2_data;

    CUDA_CHECK(cudaMalloc(&d_half_data, size * sizeof(half)));
    CUDA_CHECK(cudaMalloc(&d_float_data, size * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_half2_data, (size / 2) * sizeof(half2)));

    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    std::cout << "\n🚀 Executing FIXED AGGRESSIVE kernel evaluation..." << std::endl;

    // Execute benchmark kernels
    double time1 = runSingleKernelBenchmark("TENSOR CORE WMMA (Original)", 1, d_half_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));

    double time2 = runSingleKernelBenchmark("🔥 AGGRESSIVE PERSISTENT THREADS", 2, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time3 = runSingleKernelBenchmark("⚡ AGGRESSIVE VECTOR OPTIMIZED", 3, d_half2_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    double time4 = runSingleKernelBenchmark("💥 ULTIMATE REGISTER SATURATION", 4, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time5 = runSingleKernelBenchmark("🔥 ULTIMATE REGISTER OPTIMIZED", 5, d_float_data, size, iterations, testRuns);

    // 🚀 FIXED TFLOPS CALCULATION 
    // Tensor Core: Keep original calculation (already optimized)
    int matrices_count = size / 256;
    int num_warps = matrices_count / 32;
    double tensorOps = static_cast<double>(num_warps) * iterations * 8192.0 * testRuns;
    double tensorTFLOPS = tensorOps / (time1 / 1000.0) / 1e12;

    // 🔥 Aggressive Persistent: 64 FMA ops * 3x iterations = 192 FLOPs per iteration per thread
    double persistentOps = static_cast<double>(size) * iterations * 192.0 * testRuns;
    double persistentTFLOPS = persistentOps / (time2 / 1000.0) / 1e12;

    // ⚡ Aggressive Vector: 32 FMA ops * 5x iterations = 160 FLOPs per iteration per thread  
    double vectorOps = static_cast<double>(size / 2) * iterations * 160.0 * testRuns;
    double vectorTFLOPS = vectorOps / (time3 / 1000.0) / 1e12;

    // 💥 Ultimate Register Saturation: 96 registers * 3x iterations = 288 FLOPs per iteration per thread
    double registerSatOps = static_cast<double>(size) * iterations * 288.0 * testRuns;
    double registerSatTFLOPS = registerSatOps / (time4 / 1000.0) / 1e12;

    // 🔥 Ultimate Register Optimized: 48 FMA ops * 4x iterations = 192 FLOPs per iteration per thread  
    double registerOptOps = static_cast<double>(size) * iterations * 192.0 * testRuns;
    double registerOptTFLOPS = registerOptOps / (time5 / 1000.0) / 1e12;

    std::cout << "\n=================================================================" << std::endl;
    std::cout << "🚀 ALL 5 FIXED AGGRESSIVE KERNEL RESULTS" << std::endl;
    std::cout << "=================================================================" << std::endl;
    std::cout << "Tensor Core WMMA: " << std::fixed << std::setprecision(2) << tensorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "🔥 Aggressive Persistent: " << std::fixed << std::setprecision(2) << persistentTFLOPS << " TFLOPS" << std::endl;
    std::cout << "⚡ Aggressive Vector (FP16): " << std::fixed << std::setprecision(2) << vectorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "💥 Ultimate Register Saturation: " << std::fixed << std::setprecision(2) << registerSatTFLOPS << " TFLOPS" << std::endl;
    std::cout << "🔥 Ultimate Register Optimized: " << std::fixed << std::setprecision(2) << registerOptTFLOPS << " TFLOPS" << std::endl;

    double max_tflops = std::max({tensorTFLOPS, persistentTFLOPS, vectorTFLOPS, registerSatTFLOPS, registerOptTFLOPS});
    std::cout << "\n🚀 MAXIMUM ACHIEVED: " << std::fixed << std::setprecision(2) << max_tflops << " TFLOPS" << std::endl;

    if (max_tflops >= 150.0)
    {
        std::cout << "\n💥 INCREDIBLE! 150+ TFLOPS ACHIEVED!" << std::endl;
        std::cout << "🏆 RTX 4080 BEAST MODE UNLEASHED!" << std::endl;
    }
    else if (max_tflops >= 100.0)
    {
        std::cout << "\n🔥 EXCELLENT! 100+ TFLOPS TARGET ACHIEVED!" << std::endl;
        std::cout << "🚀 AGGRESSIVE OPTIMIZATION SUCCESS!" << std::endl;
    }
    else if (max_tflops >= 50.0)
    {
        std::cout << "\n⚡ SOLID PERFORMANCE! 50+ TFLOPS ACHIEVED!" << std::endl;
        std::cout << "🎯 HIGH-PERFORMANCE CUDA UTILIZATION!" << std::endl;
    }
    else
    {
        std::cout << "\n🚀 AGGRESSIVE FOUNDATION ESTABLISHED!" << std::endl;
        std::cout << "Performance: " << std::fixed << std::setprecision(1) << (max_tflops / 150.0) * 100.0 << "% of 150 TFLOPS target" << std::endl;
    }

    // Cleanup
    CUDA_CHECK(cudaFree(d_half_data));
    CUDA_CHECK(cudaFree(d_float_data));
    CUDA_CHECK(cudaFree(d_half2_data));
    delete[] h_half_data;
    delete[] h_float_data;
    delete[] h_half2_data;

    std::cout << "\n🔥 FIXED AGGRESSIVE CUDA BENCHMARK COMPLETE!" << std::endl;

    return 0;
}
