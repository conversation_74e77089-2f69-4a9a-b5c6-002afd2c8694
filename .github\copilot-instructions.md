# Copilot Instructions for OmniCodex/OmniForge

## Project Overview
- **OmniCodex** is a compiler framework for zero-cost heterogeneous computing, focusing on static dispatch and metadata extraction from compiled artifacts.
- **HAAL Orchestrator** (in `haal/`) is a C++ hybrid acceleration layer that routes workloads between AVX2 (CPU) and CUDA (GPU) backends, using ML-driven and performance-adaptive scheduling.

## Architecture & Key Components
- **Rust Core**: Main logic in `src/` (see `main.rs`, `lib.rs`).
- **Binary Analyzer**: Parses PE/ELF/Mach-O binaries for metadata (see `src/binary_analyzer/`).
- **Metadata Extractor**: Extracts function signatures, memory layouts, etc. (see `src/metadata_extractor/`).
- **Code Generator**: Generates static dispatch tables and wrappers (see `src/codegen/`).
- **HAAL C++ Layer**: In `haal/`, provides AVX2 and CUDA compute modules, orchestrator logic, and build scripts.

## Build & Test Workflows
- **Rust**: Use `cargo build` and `cargo test` in the root for core framework.
- **HAAL C++**:
  - Use `make all` or `make demo` in `haal/` for Linux.
  - On Windows, use VS Code tasks:
    - `Build HAAL AVX2 (MinGW)` or `Build HAAL AVX2 (MSVC)`
    - `Build HAAL CUDA` for CUDA backend
    - `Test HAAL AVX2` and `Test HAAL CUDA` run the respective binaries
  - Clean with `make clean` (Linux) or the `Clean HAAL` task (Windows)

## Project-Specific Patterns & Conventions
- **Metadata-Driven Dispatch**: All cross-backend calls use statically generated tables (see `src/codegen/`, `haal/haal-orc.cpp`).
- **No Dynamic FFI at Runtime**: All FFI is resolved at build time; runtime uses static lookups only.
- **Hybrid Orchestration**: `haal/haal-orc.cpp` and `haal/cuda-wrappers.cu` coordinate CPU/GPU execution.
- **Thread Safety**: Lock-free data structures and zero-copy memory patterns are preferred in orchestrator code.
- **File Naming**: C++ files for AVX2 use `x-2.cpp`, CUDA uses `x-one.cu`.

## Integration Points
- **Rust <-> C++/CUDA**: Integration via generated static tables and metadata, not dynamic linking.
- **External Dependencies**: Requires CUDA Toolkit, AVX2-capable compiler, pthreads (Linux), and appropriate Windows toolchains (MSVC/MinGW).

## Examples
- See `haal/haal-orc-demo.cpp` for orchestrator usage.
- See `src/binary_analyzer/` for binary parsing logic.
- See `src/codegen/` for dispatch table generation patterns.

---

For any unclear or incomplete sections, please provide feedback or specify which workflows or patterns need more detail.
