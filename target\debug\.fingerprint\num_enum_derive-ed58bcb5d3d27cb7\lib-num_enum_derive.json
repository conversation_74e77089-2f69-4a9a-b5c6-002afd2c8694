{"rustc": 1842507548689473721, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 2225463790103693989, "path": 5909776974073169828, "deps": [[3060637413840920116, "proc_macro2", false, 15371664435471233259], [4974441333307933176, "syn", false, 12913975495916870891], [15203748914246919255, "proc_macro_crate", false, 17632145390873484662], [17990358020177143287, "quote", false, 15243228526347011181]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\num_enum_derive-ed58bcb5d3d27cb7\\dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}