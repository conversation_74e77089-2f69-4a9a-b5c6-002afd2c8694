{"rustc": 1842507548689473721, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2225463790103693989, "path": 13256437254231881463, "deps": [[2687729594444538932, "debug_unreachable", false, 2510415128379642964], [2828590642173593838, "cfg_if", false, 14616637904511339238], [3722963349756955755, "once_cell", false, 12403945523159540472], [4684437522915235464, "libc", false, 12491900048182881000], [5157631553186200874, "num_traits", false, 12338405551431000230], [5237962722597217121, "simd_helpers", false, 17065823336180113805], [5626665093607998638, "build_script_build", false, 14581084941773946484], [5986029879202738730, "log", false, 5542591549586353644], [6697151524989202978, "profiling", false, 1458694756917330123], [7074416887430417773, "av1_grain", false, 17727999282553597546], [8008191657135824715, "thiserror", false, 6333990087243712636], [11263754829263059703, "num_derive", false, 2090081150898029643], [12672448913558545127, "noop_proc_macro", false, 634457649611583305], [13847662864258534762, "arrayvec", false, 11976762736244033497], [14931062873021150766, "itertools", false, 11438426778207877617], [15325537792103828505, "v_frame", false, 10354718026959185359], [16507960196461048755, "rayon", false, 16422973943811562031], [17605717126308396068, "paste", false, 10624360197493180133], [17706129463675219700, "arg_enum_proc_macro", false, 17797659318899298912], [17933778289016427379, "bitstream_io", false, 15672647929993398798]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-13f139d796acf522\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}