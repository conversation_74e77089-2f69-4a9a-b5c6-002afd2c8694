{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 13272165778285307237, "deps": [[784494742817713399, "tower_service", false, 12916316145474672364], [1906322745568073236, "pin_project_lite", false, 16020836778749131380], [4121350475192885151, "iri_string", false, 6366846030908940968], [5695049318159433696, "tower", false, 3050555675338720946], [7712452662827335977, "tower_layer", false, 15303538659210596207], [7896293946984509699, "bitflags", false, 1464639016496175381], [9010263965687315507, "http", false, 11814873844220861533], [10629569228670356391, "futures_util", false, 3765771334406624922], [14084095096285906100, "http_body", false, 11211221175494870463], [16066129441945555748, "bytes", false, 10895004934231106729]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-171a02f88ee17904\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}