// haal-avx2-compare.cpp - Compare GFLOPS of two AVX2 benchmark binaries
/**
 * This utility runs both haal-avx2.exe and haal-avx2-2.exe, captures their GFLOPS results,
 * and prints a side-by-side comparison for all kernels.
 *
 * Usage: Compile and run this program in the same directory as the two benchmark executables.
 *
 * Compilation:
 *   g++ haal-avx2-compare.cpp -o haal-avx2-compare -O2
 *
 * Note: This program assumes both haal-avx2.exe and haal-avx2-2.exe print their results
 *       to stdout in the same format as provided in the original source.
 */

#include <iostream>
#include <cstdio>
#include <string>
#include <vector>
#include <regex>

// Helper to run a command and capture its stdout
std::string run_and_capture(const char* cmd) {
    std::string result;
    FILE* pipe = _popen(cmd, "r");
    if (!pipe) return "";
    char buffer[4096];
    while (fgets(buffer, sizeof(buffer), pipe)) {
        result += buffer;
    }
    _pclose(pipe);
    return result;
}

// Extract GFLOPS lines from output
std::vector<std::string> extract_gflops(const std::string& output) {
    std::vector<std::string> lines;
    std::regex gflops_regex(R"((.+AVX2: )([0-9.]+) GFLOPS)");
    std::smatch match;
    std::istringstream iss(output);
    std::string line;
    while (std::getline(iss, line)) {
        if (std::regex_search(line, match, gflops_regex)) {
            lines.push_back(line);
        }
    }
    return lines;
}

int main() {
    std::cout << "Running haal-avx2.exe...\n";
    std::string out1 = run_and_capture("haal-avx2.exe");
    std::cout << "Running haal-avx2-2.exe...\n";
    std::string out2 = run_and_capture("haal-avx2-2.exe");

    auto gflops1 = extract_gflops(out1);
    auto gflops2 = extract_gflops(out2);

    std::cout << "\n=== GFLOPS COMPARISON ===\n";
    size_t n = std::max(gflops1.size(), gflops2.size());
    for (size_t i = 0; i < n; ++i) {
        std::string l1 = (i < gflops1.size()) ? gflops1[i] : "(no result)";
        std::string l2 = (i < gflops2.size()) ? gflops2[i] : "(no result)";
        std::cout << "[A] " << l1 << "\n[B] " << l2 << "\n---\n";
    }
    return 0;
}
