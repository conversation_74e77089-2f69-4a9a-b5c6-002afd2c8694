// haal-avx2-compare.cpp - Compare GFLOPS of THREE AVX2 benchmark versions
/**
 * 🚀 This utility compares AVX2 benchmark performance between THREE implementations:
 *
 * 📊 COMPARISON TARGETS:
 * - haal-avx2.cpp: Main optimized version (4 MB allocation)
 * - haal-avx2-16.cpp: 16 MB memory allocation version
 * - haal-avx2-2.cpp: Intel OneAPI ultra-optimized version (16 MB + advanced optimizations)
 *
 * 🎯 FEATURES COMPARED:
 * - Memory allocation sizes (4 MB vs 16 MB)
 * - Kernel optimizations (FMA vs Simulated)
 * - Intel OneAPI optimizations (NUMA, prefetching, OpenMP SIMD)
 * - Performance scaling with memory size
 *
 * 🔧 COMPILATION INSTRUCTIONS:
 *   # Compile all three benchmark versions:
 *   g++ haal-avx2.cpp -o haal-avx2.exe -mavx2 -mfma -O3 -march=native -pthread
 *   g++ haal-avx2-16.cpp -o haal-avx2-16.exe -mavx2 -mfma -O3 -march=native -pthread
 *   g++ haal-avx2-2.cpp -o haal-avx2-2.exe -mavx2 -mfma -O3 -march=native -fopenmp -pthread
 *
 *   # Compile this comparison utility:
 *   g++ haal-avx2-compare.cpp -o haal-avx2-compare.exe -O2
 *
 * 🚀 EXECUTION:
 *   ./haal-avx2-compare.exe  # Run comprehensive 3-way comparison
 *
 * 📈 EXPECTED PERFORMANCE HIERARCHY:
 * 1. haal-avx2-2.cpp (Intel OneAPI + 16MB) - HIGHEST
 * 2. haal-avx2-16.cpp (16MB allocation) - MEDIUM-HIGH
 * 3. haal-avx2.cpp (4MB allocation) - BASELINE
 */

#include <iostream>
#include <cstdio>
#include <string>
#include <vector>
#include <regex>

// Helper to run a command and capture its stdout
std::string run_and_capture(const char* cmd) {
    std::string result;
    FILE* pipe = _popen(cmd, "r");
    if (!pipe) return "";
    char buffer[4096];
    while (fgets(buffer, sizeof(buffer), pipe)) {
        result += buffer;
    }
    _pclose(pipe);
    return result;
}

// Extract GFLOPS lines from output
std::vector<std::string> extract_gflops(const std::string& output) {
    std::vector<std::string> lines;
    std::regex gflops_regex(R"((.+AVX2: )([0-9.]+) GFLOPS)");
    std::smatch match;
    std::istringstream iss(output);
    std::string line;
    while (std::getline(iss, line)) {
        if (std::regex_search(line, match, gflops_regex)) {
            lines.push_back(line);
        }
    }
    return lines;
}

int main() {
    std::cout << "🚀 AVX2 THREE-WAY PERFORMANCE COMPARISON\n";
    std::cout << "=========================================\n\n";

    // Run all three versions
    std::cout << "📊 Running haal-avx2.exe (4 MB baseline)...\n";
    std::string out1 = run_and_capture("haal-avx2.exe");

    std::cout << "📊 Running haal-avx2-16.exe (16 MB version)...\n";
    std::string out2 = run_and_capture("haal-avx2-16.exe");

    std::cout << "📊 Running haal-avx2-2.exe (Intel OneAPI optimized)...\n";
    std::string out3 = run_and_capture("haal-avx2-2.exe");

    // Extract GFLOPS from all outputs
    auto gflops1 = extract_gflops(out1);
    auto gflops2 = extract_gflops(out2);
    auto gflops3 = extract_gflops(out3);

    std::cout << "\n🏆 === THREE-WAY GFLOPS COMPARISON ===\n";
    std::cout << "======================================\n";

    // Find the maximum number of results
    size_t n = std::max({gflops1.size(), gflops2.size(), gflops3.size()});

    std::cout << "\nKernel Performance Comparison:\n";
    std::cout << "------------------------------\n";

    for (size_t i = 0; i < n; ++i) {
        std::string l1 = (i < gflops1.size()) ? gflops1[i] : "(no result)";
        std::string l2 = (i < gflops2.size()) ? gflops2[i] : "(no result)";
        std::string l3 = (i < gflops3.size()) ? gflops3[i] : "(no result)";

        std::cout << "\n🔹 Kernel " << (i + 1) << ":\n";
        std::cout << "  [4MB ]  " << l1 << "\n";
        std::cout << "  [16MB]  " << l2 << "\n";
        std::cout << "  [OneAPI] " << l3 << "\n";
        std::cout << "  " << std::string(50, '-') << "\n";
    }

    std::cout << "\n🎯 PERFORMANCE ANALYSIS:\n";
    std::cout << "========================\n";
    std::cout << "• 4MB Version: Baseline performance with optimized memory usage\n";
    std::cout << "• 16MB Version: Larger dataset for better cache utilization\n";
    std::cout << "• Intel OneAPI: Advanced optimizations (NUMA, prefetching, OpenMP SIMD)\n";
    std::cout << "\n🏆 Compare the MAXIMUM ACHIEVED values to see the winner!\n";

    return 0;
}
