// haal-cuda-proper.cu - PROPER CUDA Performance Optimizations
/**
 * REAL optimizations that actually improve performance instead of destroying it
 * 
 * PROPER OPTIMIZATION PRINCIPLES:
 * - Keep same iteration counts as original (600)
 * - Focus on instruction-level parallelism, not more work
 * - Optimize memory access patterns
 * - Use proper register allocation
 * - Fix FLOP calculations to be realistic
 * 
 * TARGET: Beat original performance with SMART optimizations
 */

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <mma.h>
#include <cooperative_groups.h>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <cmath>

using namespace nvcuda;
namespace cg = cooperative_groups;

#define CUDA_CHECK(call)                                                  \
    do                                                                    \
    {                                                                     \
        cudaError_t error = call;                                         \
        if (error != cudaSuccess)                                         \
        {                                                                 \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__  \
                      << " - " << cudaGetErrorString(error) << std::endl; \
            exit(1);                                                      \
        }                                                                 \
    } while (0)

// ============================================================================
// KEEP ORIGINAL TENSOR CORE (already optimized at 103 TFLOPS)
// ============================================================================
__global__ void xOneTensorCoreKernel(half *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size / 16) return;

    wmma::fragment<wmma::matrix_a, 16, 16, 16, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, 16, 16, 16, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, 16, 16, 16, half> acc_frag;

    wmma::fill_fragment(acc_frag, __float2half(1.0f));
    wmma::fill_fragment(a_frag, __float2half(0.9f + static_cast<float>(idx % 100) / 1000.0f));
    wmma::fill_fragment(b_frag, __float2half(1.1f + static_cast<float>(idx % 97) / 1000.0f));

    #pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        for (int j = 0; j < a_frag.num_elements; ++j)
        {
            a_frag.x[j] = __hfma(a_frag.x[j], __float2half(1.0001f), __float2half(0.0001f));
        }
        for (int j = 0; j < b_frag.num_elements; ++j)
        {
            b_frag.x[j] = __hfma(b_frag.x[j], __float2half(0.9999f), __float2half(0.0001f));
        }
    }

    int matrix_offset = idx * 256;
    if (matrix_offset + 255 < size)
    {
        wmma::store_matrix_sync(data + matrix_offset, acc_frag, 16, wmma::mem_row_major);
    }
}

// ============================================================================
// OPTIMIZED PERSISTENT: Better warp utilization + memory coalescing
// ============================================================================
__global__ void __launch_bounds__(512, 4) // Higher occupancy
xOnePersistentKernelOptimized(float *__restrict__ data, int size, int iterations, int total_blocks)
{
    cg::thread_block block = cg::this_thread_block();
    cg::thread_block_tile<32> warp = cg::tiled_partition<32>(block);

    int block_id = blockIdx.x;
    int thread_id = threadIdx.x;
    int threads_per_block = blockDim.x;

    for (int persistent_block = block_id; persistent_block < total_blocks; persistent_block += gridDim.x)
    {
        int base_idx = persistent_block * threads_per_block + thread_id;
        if (base_idx >= size) continue;

        float x = data[base_idx];

        // SAME 16 accumulators as original - don't add more work
        float acc[16];
        #pragma unroll 16
        for (int i = 0; i < 16; ++i)
        {
            acc[i] = x * (0.9f + static_cast<float>(i) * 0.01f);
        }

        // SAME iteration count - optimize the inner work instead
        #pragma unroll 32
        for (int iter = 0; iter < iterations; ++iter)
        {
            // More frequent warp shuffles for better data sharing
            if (iter % 25 == 0) // vs 50 in original
            {
                float shared_val = warp.shfl_xor(x, 1);
                x = __fmaf_rn(x, 0.9999f, shared_val * 0.0001f);
            }

            // Better instruction scheduling with unrolled pairs
            #pragma unroll 8
            for (int i = 0; i < 16; i += 2)
            {
                float coeff1 = 1.0001f + static_cast<float>(i) * 0.0001f;
                float coeff2 = 1.0001f + static_cast<float>(i+1) * 0.0001f;
                acc[i] = __fmaf_rn(acc[i], x, coeff1);
                acc[i+1] = __fmaf_rn(acc[i+1], x, coeff2);
            }
        }

        // Better reduction with warp primitives
        float result = 0.0f;
        #pragma unroll 16
        for (int i = 0; i < 16; ++i)
        {
            result += acc[i];
        }

        // Optimized warp reduction
        #pragma unroll 5
        for (int mask = 16; mask > 0; mask >>= 1) 
        {
            result += warp.shfl_down(result, mask);
        }

        data[base_idx] = result;
    }
}

// ============================================================================
// OPTIMIZED VECTOR: Better memory throughput + instruction scheduling
// ============================================================================
__global__ void __launch_bounds__(512, 2) // Balanced occupancy for registers
xOneVectorOptimizedKernelImproved(half2 *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    half2 x = data[idx];
    half2 acc1 = x;
    half2 acc2 = __hmul2(x, __float2half2_rn(0.7071f));
    
    // Add 2 more accumulators for better ILP (vs just 2 in original)
    half2 acc3 = __hmul2(x, __float2half2_rn(0.5f));
    half2 acc4 = __hmul2(x, __float2half2_rn(0.25f));

    // SAME iteration count - optimize the work pattern
    #pragma unroll 32
    for (int i = 0; i < iterations; i += 4)
    {
        // Better instruction interleaving with 4 accumulators
        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0001f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9999f));
        acc3 = __hfma2(acc3, x, __float2half2_rn(1.0002f));
        acc4 = __hfma2(acc4, x, __float2half2_rn(0.9998f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0003f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9997f));
        acc3 = __hfma2(acc3, x, __float2half2_rn(1.0004f));
        acc4 = __hfma2(acc4, x, __float2half2_rn(0.9996f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0005f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9995f));
        acc3 = __hfma2(acc3, x, __float2half2_rn(1.0006f));
        acc4 = __hfma2(acc4, x, __float2half2_rn(0.9994f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0007f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9993f));
        acc3 = __hfma2(acc3, x, __float2half2_rn(1.0008f));
        acc4 = __hfma2(acc4, x, __float2half2_rn(0.9992f));
    }

    // Combine all 4 accumulators
    half2 result = __hmul2(__hadd2(acc1, acc2), __hadd2(acc3, acc4));
    data[idx] = result;
}

// ============================================================================
// OPTIMIZED REGISTER SATURATION: Better register allocation + scheduling
// ============================================================================
__global__ void __launch_bounds__(256, 2)
xOneRegisterSaturationKernelOptimized(float *__restrict__ data, int size, int iterations)
{
    const int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= size) return;

    // Optimize to 32 registers (2x original 16) - reasonable increase
    register float r[8][4]; // 32 registers total

    const float base_val = data[tid];

    // Initialize with better patterns
    #pragma unroll 8
    for (int i = 0; i < 8; i++)
    {
        const float scale_base = 0.125f + (i * 0.125f);
        r[i][0] = base_val * scale_base;
        r[i][1] = base_val * (scale_base + 0.25f);
        r[i][2] = base_val * (scale_base + 0.50f);
        r[i][3] = base_val * (scale_base + 0.75f);
    }

    // SAME iteration count - better scheduling
    for (int iter = 0; iter < iterations; iter++)
    {
        // Optimized FMA pattern with better dependency chains
        #pragma unroll 2
        for (int batch = 0; batch < 2; batch++)
        {
            const int reg_base = batch * 4;
            
            #pragma unroll 4
            for (int q = 0; q < 4 && (reg_base + q) < 8; q++)
            {
                r[reg_base + q][0] = __fmaf_rn(r[reg_base + q][0], 1.000001f, 0.00000001f);
                r[reg_base + q][1] = __fmaf_rn(r[reg_base + q][1], 1.000002f, 0.00000002f);
                r[reg_base + q][2] = __fmaf_rn(r[reg_base + q][2], 0.999999f, 0.00000003f);
                r[reg_base + q][3] = __fmaf_rn(r[reg_base + q][3], 0.999998f, 0.00000004f);
            }
        }

        // Better cross-register dependencies
        if ((iter & 0x7) == 0)
        {
            #pragma unroll 4
            for (int i = 1; i < 8; i += 2)
            {
                r[i][0] += r[i - 1][3] * 0.0000001f;
                r[i][1] += r[i - 1][2] * 0.0000001f;
            }
        }

        // Optimized complex ops
        if ((iter & 0xF) == 0)
        {
            r[1][0] = __fmaf_rn(r[1][0], sqrtf(fabsf(r[1][1]) + 1.0f), 0.0000001f);
            r[3][1] = __fmaf_rn(r[3][1], 1.0f / (fabsf(r[3][2]) + 1.0f), 0.0000001f);
        }
    }

    // Optimized reduction
    float quad_sums[2] = {0.0f};

    #pragma unroll 2
    for (int quad = 0; quad < 2; quad++)
    {
        #pragma unroll 4
        for (int i = quad * 4; i < (quad + 1) * 4 && i < 8; i++)
        {
            quad_sums[quad] += r[i][0] + r[i][1] + r[i][2] + r[i][3];
        }
    }

    float result = quad_sums[0] * quad_sums[1];
    data[tid] = result;
}

// ============================================================================
// OPTIMIZED REGISTER OPTIMIZED: Better ILP + dependency management
// ============================================================================
__global__ void __launch_bounds__(384, 2)
xOneRegisterOptimizedKernelImproved(float *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    float x = data[idx];

    // 40 registers (vs 32 original) - modest increase with better organization
    register float r0 = x, r1 = x * 0.975f, r2 = x * 0.95f, r3 = x * 0.925f;
    register float r4 = x * 0.90f, r5 = x * 0.875f, r6 = x * 0.85f, r7 = x * 0.825f;
    register float r8 = x * 0.80f, r9 = x * 0.775f, r10 = x * 0.75f, r11 = x * 0.725f;
    register float r12 = x * 0.70f, r13 = x * 0.675f, r14 = x * 0.65f, r15 = x * 0.625f;
    register float r16 = x * 0.60f, r17 = x * 0.575f, r18 = x * 0.55f, r19 = x * 0.525f;
    register float r20 = x * 0.50f, r21 = x * 0.475f, r22 = x * 0.45f, r23 = x * 0.425f;
    register float r24 = x * 0.40f, r25 = x * 0.375f, r26 = x * 0.35f, r27 = x * 0.325f;
    register float r28 = x * 0.30f, r29 = x * 0.275f, r30 = x * 0.25f, r31 = x * 0.225f;
    
    // Additional 8 registers for better ILP
    register float r32 = x * 1.025f, r33 = x * 1.05f, r34 = x * 1.075f, r35 = x * 1.10f;
    register float r36 = x * 1.125f, r37 = x * 1.15f, r38 = x * 1.175f, r39 = x * 1.20f;

    // SAME iteration count - better instruction scheduling
    #pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        // Optimized 40 parallel FMA operations with better dependency chains
        r0 = __fmaf_rn(r0, x, 1.0001f);   r1 = __fmaf_rn(r1, x, 0.9999f);
        r2 = __fmaf_rn(r2, x, 1.0002f);   r3 = __fmaf_rn(r3, x, 0.9998f);
        r4 = __fmaf_rn(r4, x, 1.0003f);   r5 = __fmaf_rn(r5, x, 0.9997f);
        r6 = __fmaf_rn(r6, x, 1.0004f);   r7 = __fmaf_rn(r7, x, 0.9996f);
        r8 = __fmaf_rn(r8, x, 1.0005f);   r9 = __fmaf_rn(r9, x, 0.9995f);
        r10 = __fmaf_rn(r10, x, 1.0006f); r11 = __fmaf_rn(r11, x, 0.9994f);
        r12 = __fmaf_rn(r12, x, 1.0007f); r13 = __fmaf_rn(r13, x, 0.9993f);
        r14 = __fmaf_rn(r14, x, 1.0008f); r15 = __fmaf_rn(r15, x, 0.9992f);
        r16 = __fmaf_rn(r16, x, 1.0009f); r17 = __fmaf_rn(r17, x, 0.9991f);
        r18 = __fmaf_rn(r18, x, 1.0010f); r19 = __fmaf_rn(r19, x, 0.9990f);
        r20 = __fmaf_rn(r20, x, 1.0011f); r21 = __fmaf_rn(r21, x, 0.9989f);
        r22 = __fmaf_rn(r22, x, 1.0012f); r23 = __fmaf_rn(r23, x, 0.9988f);
        r24 = __fmaf_rn(r24, x, 1.0013f); r25 = __fmaf_rn(r25, x, 0.9987f);
        r26 = __fmaf_rn(r26, x, 1.0014f); r27 = __fmaf_rn(r27, x, 0.9986f);
        r28 = __fmaf_rn(r28, x, 1.0015f); r29 = __fmaf_rn(r29, x, 0.9985f);
        r30 = __fmaf_rn(r30, x, 1.0016f); r31 = __fmaf_rn(r31, x, 0.9984f);
        
        // Additional 8 FMA operations
        r32 = __fmaf_rn(r32, x, 1.0017f); r33 = __fmaf_rn(r33, x, 0.9983f);
        r34 = __fmaf_rn(r34, x, 1.0018f); r35 = __fmaf_rn(r35, x, 0.9982f);
        r36 = __fmaf_rn(r36, x, 1.0019f); r37 = __fmaf_rn(r37, x, 0.9981f);
        r38 = __fmaf_rn(r38, x, 1.0020f); r39 = __fmaf_rn(r39, x, 0.9980f);
    }

    // Optimized reduction tree
    float result = 
        ((r0 + r1 + r2 + r3) * (r4 + r5 + r6 + r7)) +
        ((r8 + r9 + r10 + r11) * (r12 + r13 + r14 + r15)) +
        ((r16 + r17 + r18 + r19) * (r20 + r21 + r22 + r23)) +
        ((r24 + r25 + r26 + r27) * (r28 + r29 + r30 + r31)) +
        ((r32 + r33 + r34 + r35) * (r36 + r37 + r38 + r39));

    data[idx] = result;
}

// CORRECTED benchmark function with REALISTIC FLOP calculations
double runSingleKernelBenchmark(const char *name, int kernelType, void *d_data,
                                int size, int iterations, int testRuns)
{
    std::cout << "=== " << name << " ===" << std::endl;

    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;

    // Handle special cases
    if (kernelType == 1) {
        size = size / 16;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 2) {
        blockSize = 512; // Higher occupancy
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 3) {
        size = size / 2;
        blockSize = 512; // Balanced
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 4) {
        blockSize = 256;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 5) {
        blockSize = 384; // Optimized for 40 registers
        gridSize = (size + blockSize - 1) / blockSize;
    }

    std::cout << "Elements: " << size << std::endl;
    std::cout << "Grid size: " << gridSize << " blocks" << std::endl;
    std::cout << std::endl;

    // Warmup
    switch (kernelType)
    {
    case 1:
        xOneTensorCoreKernel<<<gridSize, 256>>>((half *)d_data, size * 16, iterations);
        break;
    case 2:
    {
        int total_blocks = gridSize * 4;
        xOnePersistentKernelOptimized<<<gridSize, 512>>>((float *)d_data, size, iterations, total_blocks);
        break;
    }
    case 3:
        xOneVectorOptimizedKernelImproved<<<gridSize, 512>>>((half2 *)d_data, size, iterations);
        break;
    case 4:
        xOneRegisterSaturationKernelOptimized<<<gridSize, 256>>>((float *)d_data, size, iterations);
        break;
    case 5:
        xOneRegisterOptimizedKernelImproved<<<gridSize, 384>>>((float *)d_data, size, iterations);
        break;
    }
    CUDA_CHECK(cudaDeviceSynchronize());

    double totalTime = 0.0;

    for (int run = 0; run < testRuns; ++run)
    {
        auto start = std::chrono::high_resolution_clock::now();

        switch (kernelType)
        {
        case 1:
            xOneTensorCoreKernel<<<gridSize, 256>>>((half *)d_data, size * 16, iterations);
            break;
        case 2:
        {
            int total_blocks = gridSize * 4;
            xOnePersistentKernelOptimized<<<gridSize, 512>>>((float *)d_data, size, iterations, total_blocks);
            break;
        }
        case 3:
            xOneVectorOptimizedKernelImproved<<<gridSize, 512>>>((half2 *)d_data, size, iterations);
            break;
        case 4:
            xOneRegisterSaturationKernelOptimized<<<gridSize, 256>>>((float *)d_data, size, iterations);
            break;
        case 5:
            xOneRegisterOptimizedKernelImproved<<<gridSize, 384>>>((float *)d_data, size, iterations);
            break;
        }

        CUDA_CHECK(cudaDeviceSynchronize());

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double milliseconds = duration.count() / 1000.0;

        totalTime += milliseconds;
        std::cout << "Run " << (run + 1) << ": "
                  << std::fixed << std::setprecision(2) << milliseconds << " ms" << std::endl;
    }

    return totalTime;
}

// Main benchmark with REALISTIC calculations
int main()
{
    std::cout << "PROPER CUDA Performance Benchmark Suite - RTX 4080" << std::endl;
    std::cout << "=====================================================" << std::endl;
    std::cout << "TARGET: Beat original performance with smart optimizations" << std::endl;
    std::cout << "=====================================================" << std::endl;

    const int size = 128 * 1024 * 1024; // Same as original
    const int iterations = 600;         // SAME as original
    const int testRuns = 3;

    std::cout << "\nBenchmark Configuration:" << std::endl;
    std::cout << "  Array Size: " << size << " elements ("
              << (size * sizeof(float)) / (1024 * 1024) << " MB)" << std::endl;
    std::cout << "  Iterations: " << iterations << " (SAME as original)" << std::endl;
    std::cout << "  Test Runs: " << testRuns << std::endl;

    // Memory allocation (same as original)
    half *h_half_data = new half[size];
    float *h_float_data = new float[size];
    half2 *h_half2_data = new half2[size / 2];

    for (int i = 0; i < size; i++)
    {
        h_half_data[i] = __float2half(1.0f + (i % 1000) * 0.0001f);
        h_float_data[i] = 1.0f + (i % 1000) * 0.0001f;
        if (i < size / 2)
        {
            h_half2_data[i] = __float2half2_rn(1.0f + (i % 1000) * 0.0001f);
        }
    }

    half *d_half_data;
    float *d_float_data;
    half2 *d_half2_data;

    CUDA_CHECK(cudaMalloc(&d_half_data, size * sizeof(half)));
    CUDA_CHECK(cudaMalloc(&d_float_data, size * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_half2_data, (size / 2) * sizeof(half2)));

    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    std::cout << "\nExecuting PROPER optimized kernel evaluation..." << std::endl;

    // Execute benchmark kernels
    double time1 = runSingleKernelBenchmark("TENSOR CORE WMMA (Original)", 1, d_half_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));

    double time2 = runSingleKernelBenchmark("OPTIMIZED PERSISTENT THREADS", 2, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time3 = runSingleKernelBenchmark("OPTIMIZED VECTOR (FP16)", 3, d_half2_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    double time4 = runSingleKernelBenchmark("OPTIMIZED REGISTER SATURATION", 4, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time5 = runSingleKernelBenchmark("OPTIMIZED REGISTER OPTIMIZED", 5, d_float_data, size, iterations, testRuns);

    // REALISTIC TFLOPS CALCULATION (same as original)
    // Tensor Core: Each warp does one 16x16x16 WMMA per iteration
    int num_warps = (size / 16) / 32;
    double tensorOps = static_cast<double>(num_warps) * iterations * 8192.0 * testRuns;
    double tensorTFLOPS = tensorOps / (time1 / 1000.0) / 1e12;

    // Persistent: 16 FMA ops per iteration = 32 FLOPs per iteration per thread
    double persistentOps = static_cast<double>(size) * iterations * 32.0 * testRuns;
    double persistentTFLOPS = persistentOps / (time2 / 1000.0) / 1e12;

    // Vector: 4 FMA ops per iteration on half2 = 8 FLOPs per iteration per thread (improved from 2)
    double vectorOps = static_cast<double>(size / 2) * iterations * 8.0 * testRuns;
    double vectorTFLOPS = vectorOps / (time3 / 1000.0) / 1e12;

    // Register Saturation: 32 registers = 32 FMA ops per iteration = 64 FLOPs per iteration per thread
    double registerSatOps = static_cast<double>(size) * iterations * 64.0 * testRuns;
    double registerSatTFLOPS = registerSatOps / (time4 / 1000.0) / 1e12;

    // Register Optimized: 40 FMA ops per iteration = 80 FLOPs per iteration per thread
    double registerOptOps = static_cast<double>(size) * iterations * 80.0 * testRuns;
    double registerOptTFLOPS = registerOptOps / (time5 / 1000.0) / 1e12;

    std::cout << "\n=================================================================" << std::endl;
    std::cout << "ALL 5 PROPERLY OPTIMIZED KERNEL RESULTS" << std::endl;
    std::cout << "=================================================================" << std::endl;
    std::cout << "Tensor Core WMMA: " << std::fixed << std::setprecision(2) << tensorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Optimized Persistent: " << std::fixed << std::setprecision(2) << persistentTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Optimized Vector (FP16): " << std::fixed << std::setprecision(2) << vectorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Optimized Register Saturation: " << std::fixed << std::setprecision(2) << registerSatTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Optimized Register Optimized: " << std::fixed << std::setprecision(2) << registerOptTFLOPS << " TFLOPS" << std::endl;

    double max_tflops = std::max({tensorTFLOPS, persistentTFLOPS, vectorTFLOPS, registerSatTFLOPS, registerOptTFLOPS});
    std::cout << "\nMAXIMUM ACHIEVED: " << std::fixed << std::setprecision(2) << max_tflops << " TFLOPS" << std::endl;

    std::cout << "\nCOMPARISON TO ORIGINAL:" << std::endl;
    std::cout << "Original Persistent:  16.12 TFLOPS vs Optimized: " << std::fixed << std::setprecision(2) << persistentTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Original Vector:      15.19 TFLOPS vs Optimized: " << std::fixed << std::setprecision(2) << vectorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Original Reg Sat:     39.33 TFLOPS vs Optimized: " << std::fixed << std::setprecision(2) << registerSatTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Original Reg Opt:     30.35 TFLOPS vs Optimized: " << std::fixed << std::setprecision(2) << registerOptTFLOPS << " TFLOPS" << std::endl;

    // Cleanup
    CUDA_CHECK(cudaFree(d_half_data));
    CUDA_CHECK(cudaFree(d_float_data));
    CUDA_CHECK(cudaFree(d_half2_data));
    delete[] h_half_data;
    delete[] h_float_data;
    delete[] h_half2_data;

    std::cout << "\nPROPER CUDA OPTIMIZATION BENCHMARK COMPLETE" << std::endl;

    return 0;
}
