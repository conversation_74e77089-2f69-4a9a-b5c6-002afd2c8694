// src/lib.rs
//! OmniForge: The OmniCodex Compiler Framework
//!
//! A revolutionary compiler framework that implements the OmniCodex architecture
//! for zero-cost heterogeneous computing. It extracts metadata from compiled artifacts
//! and generates static dispatch tables that eliminate the runtime complexity of
//! multi-backend computing while maintaining memory safety and zero-cost abstraction principles.
//!
//! # Core Architecture
//!
//! OmniForge consists of several key components:
//!
//! - [`binary_analyzer`]: Parses different binary formats (PE, ELF, Mach-O) and extracts metadata
//! - [`metadata_extractor`]: Extracts function signatures, memory layouts, etc. from compiled artifacts
//! - [`codegen`]: Generates static dispatch tables and type-safe wrappers
//! - [`compiler`]: Orchestrates the overall compilation process
//!
//! # Example Usage
//!
//! ```rust,no_run
//! use omni_forge::prelude::*;
//!
//! fn main() -> Result<(), OmniError> {
//!     let compiler = OmniCompiler::new(CompilerOptions::default());
//!     
//!     // Add input binaries/source files
//!     compiler.add_input("cuda_kernel.ptx")?;
//!     compiler.add_input("avx_functions.o")?;
//!     
//!     // Generate OmniCodex dispatch table
//!     compiler.generate_codex("output_codex.rs")?;
//!     
//!     Ok(())
//! }
//! ```
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

// Re-export all public items
pub use self::error::{OmniError, OmniResult};
pub use self::compiler::OmniCompiler;
pub use self::config::CompilerOptions;

// Define modules
pub mod ahaw;
pub mod binary_analyzer;
pub mod codegen;
pub mod compiler;
pub mod config;
pub mod error;
pub mod gui;
pub mod metadata_extractor;
pub mod util;

// Machine Learning & AI Inference (optional)
#[cfg(feature = "ml-inference")]
pub mod models;

/// Prelude module that exports commonly used types
pub mod prelude {
    pub use crate::ahaw::{VectorOperation, AccelerationHint, TaskCharacteristics, PerformanceTelemetry, AccelerationResult, OmniForgeAccelerator};
    pub use crate::OmniCompiler;
    pub use crate::CompilerOptions;
    pub use crate::error::{OmniError, OmniResult};
    pub use crate::binary_analyzer::BinaryType;
    pub use crate::metadata_extractor::ExtractedMetadata;
    pub use crate::codegen::CodexEntry;

    // ML inference exports (when feature is enabled)
    #[cfg(feature = "ml-inference")]
    pub use crate::models::{Umlaiie, LoadOptions, Device, ModelMetadata, load_model};
}
