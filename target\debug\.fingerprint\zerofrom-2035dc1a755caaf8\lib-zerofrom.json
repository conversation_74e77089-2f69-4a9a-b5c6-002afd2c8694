{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2225463790103693989, "path": 13127099320570531598, "deps": [[4022439902832367970, "zerofrom_derive", false, 15440445060447380424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-2035dc1a755caaf8\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}