{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `uuid`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78146,"byte_end":78150,"line_start":2019,"line_end":2019,"column_start":60,"column_end":64,"is_primary":true,"text":[{"text":"                    let project_id = format!(\"project_{}\", uuid::Uuid::new_v4());","highlight_start":60,"highlight_end":64}],"label":"use of unresolved module or unlinked crate `uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78146,"byte_end":78156,"line_start":2019,"line_end":2019,"column_start":60,"column_end":70,"is_primary":true,"text":[{"text":"                    let project_id = format!(\"project_{}\", uuid::Uuid::new_v4());","highlight_start":60,"highlight_end":70}],"label":null,"suggested_replacement":"goblin::mach::load_command::CommandVariant","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":78146,"byte_end":78156,"line_start":2019,"line_end":2019,"column_start":60,"column_end":70,"is_primary":true,"text":[{"text":"                    let project_id = format!(\"project_{}\", uuid::Uuid::new_v4());","highlight_start":60,"highlight_end":70}],"label":null,"suggested_replacement":"object::read::macho::LoadCommandVariant","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2019:60\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let project_id = format!(\"project_{}\", uuid::Uuid::new_v4());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                    let project_id = format!(\"project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4());\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                    let project_id = format!(\"project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mgoblin::mach::load_command::CommandVariant\u001b[0m\u001b[0m::new_v4());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                    let project_id = format!(\"project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4());\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2019\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                    let project_id = format!(\"project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mobject::read::macho::LoadCommandVariant\u001b[0m\u001b[0m::new_v4());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `uuid`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78473,"byte_end":78477,"line_start":2024,"line_end":2024,"column_start":52,"column_end":56,"is_primary":true,"text":[{"text":"                    Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":52,"highlight_end":56}],"label":"use of unresolved module or unlinked crate `uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78473,"byte_end":78483,"line_start":2024,"line_end":2024,"column_start":52,"column_end":62,"is_primary":true,"text":[{"text":"                    Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":52,"highlight_end":62}],"label":null,"suggested_replacement":"goblin::mach::load_command::CommandVariant","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":78473,"byte_end":78483,"line_start":2024,"line_end":2024,"column_start":52,"column_end":62,"is_primary":true,"text":[{"text":"                    Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":52,"highlight_end":62}],"label":null,"suggested_replacement":"object::read::macho::LoadCommandVariant","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2024:52\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                    Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                    Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mgoblin::mach::load_command::CommandVariant\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                    Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2024\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                    Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mobject::read::macho::LoadCommandVariant\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `uuid`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78743,"byte_end":78747,"line_start":2030,"line_end":2030,"column_start":48,"column_end":52,"is_primary":true,"text":[{"text":"                Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":48,"highlight_end":52}],"label":"use of unresolved module or unlinked crate `uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":78743,"byte_end":78753,"line_start":2030,"line_end":2030,"column_start":48,"column_end":58,"is_primary":true,"text":[{"text":"                Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":48,"highlight_end":58}],"label":null,"suggested_replacement":"goblin::mach::load_command::CommandVariant","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":78743,"byte_end":78753,"line_start":2030,"line_end":2030,"column_start":48,"column_end":58,"is_primary":true,"text":[{"text":"                Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))","highlight_start":48,"highlight_end":58}],"label":null,"suggested_replacement":"object::read::macho::LoadCommandVariant","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2030:48\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(format!(\"local_project_{}\", uuid::Uuid::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `uuid`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `uuid`, use `cargo add uuid` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `goblin::mach::load_command::CommandVariant::Uuid` and 1 other; try using the variant's enum\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mgoblin::mach::load_command::CommandVariant\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;9muuid::Uuid\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                Ok(format!(\"local_project_{}\", \u001b[0m\u001b[0m\u001b[38;5;10mobject::read::macho::LoadCommandVariant\u001b[0m\u001b[0m::new_v4()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `default` found for struct `gui::BinaryMetadata` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":79853,"byte_end":79860,"line_start":2057,"line_end":2057,"column_start":58,"column_end":65,"is_primary":true,"text":[{"text":"                        binary_metadata: BinaryMetadata::default(),","highlight_start":58,"highlight_end":65}],"label":"function or associated item not found in `BinaryMetadata`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":84339,"byte_end":84364,"line_start":2190,"line_end":2190,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"pub struct BinaryMetadata {","highlight_start":1,"highlight_end":26}],"label":"function or associated item `default` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following traits define an item `default`, perhaps you need to implement one of them:\ncandidate #1: `std::default::Default`\ncandidate #2: `tinyvec::array::Array`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `default` found for struct `gui::BinaryMetadata` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2057:58\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2057\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        binary_metadata: BinaryMetadata::default(),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `BinaryMetadata`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct BinaryMetadata {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfunction or associated item `default` not found for this struct\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following traits define an item `default`, perhaps you need to implement one of them:\u001b[0m\n\u001b[0m             candidate #1: `std::default::Default`\u001b[0m\n\u001b[0m             candidate #2: `tinyvec::array::Array`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `default` found for struct `PerformanceAnalysis` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":80238,"byte_end":80245,"line_start":2063,"line_end":2063,"column_start":68,"column_end":75,"is_primary":true,"text":[{"text":"                        performance_analysis: PerformanceAnalysis::default(),","highlight_start":68,"highlight_end":75}],"label":"function or associated item not found in `PerformanceAnalysis`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":29271,"byte_end":29301,"line_start":808,"line_end":808,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"pub struct PerformanceAnalysis {","highlight_start":1,"highlight_end":31}],"label":"function or associated item `default` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following traits define an item `default`, perhaps you need to implement one of them:\ncandidate #1: `std::default::Default`\ncandidate #2: `tinyvec::array::Array`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `default` found for struct `PerformanceAnalysis` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2063:68\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m808\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct PerformanceAnalysis {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfunction or associated item `default` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2063\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        performance_analysis: PerformanceAnalysis::default(),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `PerformanceAnalysis`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following traits define an item `default`, perhaps you need to implement one of them:\u001b[0m\n\u001b[0m             candidate #1: `std::default::Default`\u001b[0m\n\u001b[0m             candidate #2: `tinyvec::array::Array`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no function or associated item named `default` found for struct `DependencyGraph` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":80309,"byte_end":80316,"line_start":2064,"line_end":2064,"column_start":60,"column_end":67,"is_primary":true,"text":[{"text":"                        dependency_graph: DependencyGraph::default(),","highlight_start":60,"highlight_end":67}],"label":"function or associated item not found in `DependencyGraph`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":31039,"byte_end":31065,"line_start":855,"line_end":855,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"pub struct DependencyGraph {","highlight_start":1,"highlight_end":27}],"label":"function or associated item `default` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following traits define an item `default`, perhaps you need to implement one of them:\ncandidate #1: `std::default::Default`\ncandidate #2: `tinyvec::array::Array`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no function or associated item named `default` found for struct `DependencyGraph` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:2064:60\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m855\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DependencyGraph {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfunction or associated item `default` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2064\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        dependency_graph: DependencyGraph::default(),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfunction or associated item not found in `DependencyGraph`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following traits define an item `default`, perhaps you need to implement one of them:\u001b[0m\n\u001b[0m             candidate #1: `std::default::Default`\u001b[0m\n\u001b[0m             candidate #2: `tinyvec::array::Array`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 6 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 6 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0433, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0433, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0433`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0433`.\u001b[0m\n"}
