{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"convert\", \"coroutine_trait\", \"default\", \"fmt\", \"fn_traits\", \"futures01\", \"futures03\", \"generator_trait\", \"http_body1\", \"ops\", \"rayon\", \"serde\", \"std\", \"tokio01\", \"tokio02\", \"tokio03\", \"tokio1\", \"transpose_methods\", \"trusted_len\", \"type_analysis\", \"unstable\"]", "target": 11444723345087768922, "profile": 7238658530830823778, "path": 6413459946441734572, "deps": [[1179512766113065048, "derive_utils", false, 5757765746254740882], [3060637413840920116, "proc_macro2", false, 15371664435471233259], [4974441333307933176, "syn", false, 12913975495916870891], [17990358020177143287, "quote", false, 15243228526347011181]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\auto_enums-feda23bc608760a4\\dep-lib-auto_enums", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}