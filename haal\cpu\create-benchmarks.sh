#!/bin/bash
# create-benchmarks.sh - Creates all benchmark source files for Intel 13900H testing

echo "Creating Intel 13900H Benchmark Suite Files..."
echo "================================================="

# Create haal-avx2.cpp
echo "Creating haal-avx2.cpp..."
cat > haal-avx2.cpp << 'EOF'
#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>

const size_t N = 1024 * 1024; // 4 MB allocation

void avx2_fma_kernel(float* a, float* b, float* c, size_t n) {
    for (size_t i = 0; i < n; i += 8) {
        __m256 va = _mm256_load_ps(&a[i]);
        __m256 vb = _mm256_load_ps(&b[i]);
        __m256 vc = _mm256_load_ps(&c[i]);
        
        __m256 vfma = _mm256_fmadd_ps(va, vb, vc);
        
        _mm256_store_ps(&a[i], vfma);
    }
}

int main() {
    alignas(32) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f;
        c[i] = i * 0.3f;
    }

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        avx2_fma_kernel(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    double ops = 1000.0 * N * 2; // FMA = 2 ops per element
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "AVX2 FMA (4MB): " << gflops << " GFLOPS" << std::endl;
    return 0;
}
EOF

# Create haal-avx2-16.cpp
echo "Creating haal-avx2-16.cpp..."
cat > haal-avx2-16.cpp << 'EOF'
#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>

const size_t N = 4 * 1024 * 1024; // 16 MB allocation

void avx2_fma_kernel_16mb(float* a, float* b, float* c, size_t n) {
    for (size_t i = 0; i < n; i += 8) {
        __m256 va = _mm256_load_ps(&a[i]);
        __m256 vb = _mm256_load_ps(&b[i]);
        __m256 vc = _mm256_load_ps(&c[i]);
        
        __m256 vfma = _mm256_fmadd_ps(va, vb, vc);
        
        _mm256_store_ps(&a[i], vfma);
    }
}

int main() {
    alignas(32) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f;
        c[i] = i * 0.3f;
    }

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        avx2_fma_kernel_16mb(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    double ops = 1000.0 * N * 2;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "AVX2 FMA (16MB): " << gflops << " GFLOPS" << std::endl;
    return 0;
}
EOF

# Create haal-avx2-2.cpp
echo "Creating haal-avx2-2.cpp..."
cat > haal-avx2-2.cpp << 'EOF'
#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>
#include <omp.h>

const size_t N = 4 * 1024 * 1024; // 16 MB allocation

void oneapi_optimized_kernel(float* __restrict__ a, 
                           const float* __restrict__ b, 
                           const float* __restrict__ c, 
                           size_t n) {
    #pragma omp simd aligned(a,b,c:32) vector_length(8)
    for (size_t i = 0; i < n; i += 8) {
        // Prefetch next cache lines
        _mm_prefetch((char*)&a[i + 64], _MM_HINT_T0);
        _mm_prefetch((char*)&b[i + 64], _MM_HINT_T0);
        _mm_prefetch((char*)&c[i + 64], _MM_HINT_T0);
        
        __m256 va = _mm256_load_ps(&a[i]);
        __m256 vb = _mm256_load_ps(&b[i]);
        __m256 vc = _mm256_load_ps(&c[i]);
        
        __m256 vfma = _mm256_fmadd_ps(va, vb, vc);
        
        _mm256_store_ps(&a[i], vfma);
    }
}

int main() {
    alignas(32) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f;
        c[i] = i * 0.3f;
    }

    std::cout << "Intel OneAPI Optimized (16MB + prefetch + OpenMP SIMD)" << std::endl;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        oneapi_optimized_kernel(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    double ops = 1000.0 * N * 2;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "Intel OneAPI AVX2: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
EOF

# Create haal-sse.cpp
echo "Creating haal-sse.cpp..."
cat > haal-sse.cpp << 'EOF'
#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>

const size_t N = 1024 * 1024;

void sse_fma_simulate(float* a, float* b, float* c, size_t n) {
    for (size_t i = 0; i < n; i += 4) {
        __m128 va = _mm_load_ps(&a[i]);
        __m128 vb = _mm_load_ps(&b[i]);
        __m128 vc = _mm_load_ps(&c[i]);
        
        // Simulate FMA: a * b + c
        __m128 vmul = _mm_mul_ps(va, vb);
        __m128 vfma = _mm_add_ps(vmul, vc);
        
        _mm_store_ps(&a[i], vfma);
    }
}

int main() {
    alignas(16) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f; 
        c[i] = i * 0.3f;
    }

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        sse_fma_simulate(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    double ops = 1000.0 * N * 2;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "SSE 4.2 FMA Simulation: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
EOF

# Create haal-openmp.cpp
echo "Creating haal-openmp.cpp..."
cat > haal-openmp.cpp << 'EOF'
#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>
#include <omp.h>

const size_t N = 4 * 1024 * 1024; // Larger for parallel

void openmp_avx2_kernel(float* a, float* b, float* c, size_t n) {
    #pragma omp parallel for schedule(static) num_threads(20)
    for (size_t i = 0; i < n; i += 8) {
        __m256 va = _mm256_load_ps(&a[i]);
        __m256 vb = _mm256_load_ps(&b[i]);
        __m256 vc = _mm256_load_ps(&c[i]);
        
        __m256 vfma = _mm256_fmadd_ps(va, vb, vc);
        
        _mm256_store_ps(&a[i], vfma);
    }
}

int main() {
    alignas(32) std::vector<float> a(N), b(N), c(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
        b[i] = i * 0.2f;
        c[i] = i * 0.3f;
    }

    std::cout << "Using " << omp_get_max_threads() << " threads" << std::endl;

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        openmp_avx2_kernel(a.data(), b.data(), c.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    double ops = 1000.0 * N * 2;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "OpenMP AVX2 (20 threads): " << gflops << " GFLOPS" << std::endl;
    return 0;
}
EOF

# Create build script
echo "Creating build.sh..."
cat > build.sh << 'EOF'
#!/bin/bash
echo "Compiling Intel 13900H Benchmark Suite..."

# Essential benchmarks
echo "Building core AVX2 variants..."
g++ -o haal-avx2.exe haal-avx2.cpp -mavx2 -mfma -O3 -march=native -pthread
g++ -o haal-avx2-16.exe haal-avx2-16.cpp -mavx2 -mfma -O3 -march=native -pthread
g++ -o haal-avx2-2.exe haal-avx2-2.cpp -mavx2 -mfma -O3 -march=native -fopenmp

echo "Building SIMD comparison..."
g++ -o haal-sse.exe haal-sse.cpp -msse4.2 -O3 -march=native -pthread

echo "Building parallel test..."
g++ -o haal-openmp.exe haal-openmp.cpp -mavx2 -mfma -O3 -march=native -fopenmp

echo "Compilation complete!"
echo ""
echo "Run tests with:"
echo "./haal-avx2.exe"
echo "./haal-avx2-16.exe" 
echo "./haal-avx2-2.exe"
echo "./haal-sse.exe"
echo "./haal-openmp.exe"
EOF

# Create test script
echo "Creating test-all.sh..."
cat > test-all.sh << 'EOF'
#!/bin/bash
echo "=== Intel 13900H Performance Test ==="
echo ""

echo "SSE 4.2 Baseline:"
./haal-sse.exe
echo ""

echo "AVX2 Variants Comparison:"
./haal-avx2.exe
./haal-avx2-16.exe  
./haal-avx2-2.exe
echo ""

echo "Parallel Scaling (20 threads):"
./haal-openmp.exe
EOF

# Make scripts executable
chmod +x build.sh
chmod +x test-all.sh

echo ""
echo "================================================="
echo "ALL FILES CREATED SUCCESSFULLY!"
echo "================================================="
echo ""
echo "Files created:"
echo "- haal-avx2.cpp (4MB baseline)"
echo "- haal-avx2-16.cpp (16MB version)"
echo "- haal-avx2-2.cpp (Intel OneAPI optimized)"
echo "- haal-sse.cpp (SSE baseline)"
echo "- haal-openmp.cpp (20-thread parallel)"
echo "- build.sh (compilation script)"
echo "- test-all.sh (testing script)"
echo ""
echo "Next steps:"
echo "1. Run: ./build.sh"
echo "2. Run: ./test-all.sh"
echo ""
echo "Or test individually:"
echo "./haal-avx2.exe"
echo "./haal-sse.exe"
echo "./haal-openmp.exe"