{"rustc": 1842507548689473721, "features": "[\"accessibility\", \"default\", \"muda\", \"renderer-femtovg\", \"renderer-software\", \"wayland\", \"x11\"]", "declared_features": "[\"accessibility\", \"default\", \"i-slint-renderer-skia\", \"muda\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-femtovg-wgpu\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"unstable-wgpu-24\", \"wayland\", \"x11\"]", "target": 4810267537760664992, "profile": 2241668132362809309, "path": 14565678037594594704, "deps": [[376837177317575824, "softbuffer", false, 4246693160563719606], [1174809127243710627, "accesskit", false, 14231002342998125349], [1376236821951328207, "i_slint_renderer_femtovg", false, 5955377454199835160], [1580386997979624515, "scoped_tls_hkt", false, 1485184049909622334], [1961690427714978873, "glutin_winit", false, 7000756663347532327], [2828590642173593838, "cfg_if", false, 309483949851362009], [4143744114649553716, "raw_window_handle", false, 5354778983053800373], [4494683389616423722, "muda", false, 6928625280802889909], [5333633174709578079, "rgb", false, 13513943751190438444], [5719588853395619134, "copypasta", false, 5283313485456172370], [5877456377495598509, "winit", false, 9886403166916770809], [6052161001172026385, "accesskit_winit", false, 1226756057275188542], [6511429716036861196, "bytemuck", false, 14174369814943695942], [7611461696084798871, "lyon_path", false, 10080207585131608809], [8752287454037397770, "vtable", false, 8494226397952026967], [10058659651543567831, "glutin", false, 3602958869290178162], [11293676373856528358, "derive_more", false, 3268961361124396041], [12247523395782269768, "i_slint_common", false, 6374153422814039560], [13170356948319896346, "build_script_build", false, 10632119884562917067], [13690040694845223964, "pin_weak", false, 15858686561510836941], [14974289545650296072, "imgref", false, 2105497567174612187], [15358414700195712381, "scopeguard", false, 11101136691117582051], [15764579764801749776, "i_slint_core_macros", false, 18325846628304979310], [18113059991484317634, "i_slint_core", false, 15710703813073385180]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-winit-0e230d661bccc423\\dep-lib-i_slint_backend_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}