# Makefile for HAAL Orchestrator System
# Builds real hardware acceleration orchestrator with CUDA and AVX2 backends
# ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
# @gitHub  : https://github.com/arcmoonstudios
# @copyright (c) 2025 ArcMoon Studios
# @license : MIT OR Apache-2.0
# <AUTHOR> Lord Xyn

# Compiler settings
CXX := g++
NVCC := nvcc

# Directories
SRCDIR := .
INCDIR := include
OBJDIR := obj
BINDIR := bin

# Create directories
$(shell mkdir -p $(OBJDIR) $(BINDIR))

# Compiler flags
CXXFLAGS := -std=c++17 -O3 -mavx2 -mfma -march=native -fopenmp
CXXFLAGS += -I$(INCDIR) -DAVX2_ENABLED -fPIC

# NVCC flags for CUDA compilation
NVCCFLAGS := -std=c++17 -O3 -arch=sm_89 -use_fast_math --maxrregcount=64
NVCCFLAGS += -I$(INCDIR) -Xcompiler \"-fopenmp,-fPIC\"

# Warning flags
CXXFLAGS += -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable
NVCCFLAGS += -Xcompiler \"-Wall,-Wextra,-Wno-unused-parameter\"

# Debug flags (uncomment for debugging)
# CXXFLAGS += -g -DDEBUG
# NVCCFLAGS += -g -G -DDEBUG

# Libraries
LIBS := -lcuda -lcudart -lpthread -lm

# Check for CUDA installation
CUDA_PATH ?= $(shell which nvcc 2>/dev/null | xargs dirname 2>/dev/null | xargs dirname 2>/dev/null)
ifneq ($(CUDA_PATH),)
    CUDA_LIB_PATH := $(CUDA_PATH)/lib64
    CXXFLAGS += -I$(CUDA_PATH)/include
    LDFLAGS += -L$(CUDA_LIB_PATH) -Wl,-rpath,$(CUDA_LIB_PATH)
else
    $(warning CUDA not found, building CPU-only version)
    CXXFLAGS += -DCUDA_DISABLED
endif

# Source files
CXX_SOURCES := haal-orc.cpp haal-avx2.cpp haal-c-wrapper.cpp
CUDA_SOURCES := haal-cuda.cu cuda-wrappers.cu

# Object files
CXX_OBJECTS := $(patsubst %.cpp,$(OBJDIR)/%.o,$(CXX_SOURCES))
CUDA_OBJECTS := $(patsubst %.cu,$(OBJDIR)/%.o,$(CUDA_SOURCES))
ALL_OBJECTS := $(CXX_OBJECTS) $(CUDA_OBJECTS)

# Filter out main functions for library
LIB_OBJECTS := $(filter-out $(OBJDIR)/haal-cuda.o $(OBJDIR)/haal-avx2.o,$(ALL_OBJECTS))

# Targets
MAIN_TARGET := $(BINDIR)/haal-test
BENCH_TARGET := $(BINDIR)/haal-benchmark
CUDA_BENCH := $(BINDIR)/haal-cuda-gpu
AVX2_BENCH := $(BINDIR)/haal-avx2-cpu
LIB_TARGET := $(BINDIR)/libhaal.so
STATIC_LIB := $(BINDIR)/libhaal.a

# Default target
all: check-deps $(MAIN_TARGET) $(LIB_TARGET) $(STATIC_LIB)
	@echo \"✅ All HAAL targets built successfully!\"
	@echo \"   Real Test: $(MAIN_TARGET)\"
	@echo \"   Shared Lib: $(LIB_TARGET)\"
	@echo \"   Static Lib: $(STATIC_LIB)\"

# Build all including benchmarks
all-with-bench: all $(BENCH_TARGET) $(CUDA_BENCH) $(AVX2_BENCH)
	@echo \"✅ All targets including benchmarks built!\"
	@echo \"   Benchmark: $(BENCH_TARGET)\"
	@echo \"   CUDA Test: $(CUDA_BENCH)\"
	@echo \"   AVX2 Test: $(AVX2_BENCH)\"

# Check dependencies
check-deps:
	@echo \"🔍 Checking build dependencies...\"
	@command -v $(CXX) >/dev/null 2>&1 || (echo \"❌ g++ not found\"; exit 1)
	@echo \"✅ C++ compiler: $(CXX)\"
ifndef CUDA_DISABLED
	@command -v $(NVCC) >/dev/null 2>&1 || (echo \"❌ nvcc not found\"; exit 1)
	@echo \"✅ CUDA compiler: $(NVCC)\"
	@echo \"✅ CUDA path: $(CUDA_PATH)\"
else
	@echo \"⚠️  CUDA disabled - CPU-only build\"
endif
	@echo \"✅ Dependencies check complete\"

# Compile C++ source files
$(OBJDIR)/%.o: %.cpp
	@echo \"🔧 Compiling C++: $<\"
	@$(CXX) $(CXXFLAGS) -c $< -o $@

# Compile CUDA source files
$(OBJDIR)/%.o: %.cu
	@echo \"🚀 Compiling CUDA: $<\"
	@$(NVCC) $(NVCCFLAGS) -c $< -o $@

# Link the main real test
$(MAIN_TARGET): $(ALL_OBJECTS) $(OBJDIR)/real-haal-test.o
	@echo \"🔗 Linking real HAAL test...\"
	@$(CXX) $(CXXFLAGS) $(LDFLAGS) $^ -o $@ $(LIBS)
	@echo \"✅ Real HAAL test built: $@\"

# Compile the real test source
$(OBJDIR)/real-haal-test.o: haal-test.cpp
	@echo \"🔧 Compiling real test: $<\"
	@$(CXX) $(CXXFLAGS) -c $< -o $@

# Build shared library
$(LIB_TARGET): $(LIB_OBJECTS)
	@echo \"🔗 Linking shared library...\"
	@$(CXX) -shared $(CXXFLAGS) $(LDFLAGS) $^ -o $@ $(LIBS)
	@echo \"✅ Shared library built: $@\"

# Build static library
$(STATIC_LIB): $(LIB_OBJECTS)
	@echo \"📦 Creating static library...\"
	@ar rcs $@ $^
	@echo \"✅ Static library built: $@\"

# Build benchmark version (if needed)
$(BENCH_TARGET): $(ALL_OBJECTS)
	@echo \"🔗 Linking HAAL benchmark...\"
	@$(CXX) $(CXXFLAGS) $(LDFLAGS) $^ -o $@ $(LIBS)
	@echo \"✅ HAAL benchmark built: $@\"

# Build standalone CUDA benchmark
$(CUDA_BENCH): $(OBJDIR)/haal-cuda.o
	@echo \"🔗 Linking CUDA benchmark...\"
	@$(NVCC) $(NVCCFLAGS) $(LDFLAGS) $^ -o $@ $(LIBS)
	@echo \"✅ CUDA benchmark built: $@\"

# Build standalone AVX2 benchmark
$(AVX2_BENCH): $(OBJDIR)/haal-avx2.o
	@echo \"🔗 Linking AVX2 benchmark...\"
	@$(CXX) $(CXXFLAGS) $(LDFLAGS) $^ -o $@ -lpthread
	@echo \"✅ AVX2 benchmark built: $@\"

# Test targets
test: $(MAIN_TARGET)
	@echo \"🧪 Running real HAAL test...\"
	@$(MAIN_TARGET)

test-cuda: $(CUDA_BENCH)
	@echo \"🧪 Running CUDA benchmark...\"
	@$(CUDA_BENCH)

test-avx2: $(AVX2_BENCH)
	@echo \"🧪 Running AVX2 benchmark...\"
	@$(AVX2_BENCH)

test-all: test test-cuda test-avx2
	@echo \"✅ All tests completed\"

# Library test
test-lib: $(LIB_TARGET)
	@echo \"🧪 Testing shared library...\"
	@echo \"Library built at: $(LIB_TARGET)\"
	@file $(LIB_TARGET)
	@ldd $(LIB_TARGET) || echo \"Dependencies check complete\"

# Utility targets
clean:
	@echo \"🧹 Cleaning build artifacts...\"
	@rm -rf $(OBJDIR) $(BINDIR)
	@echo \"✅ Clean complete\"

rebuild: clean all
	@echo \"✅ Rebuild complete\"

# Show build information
info:
	@echo \"HAAL Build Information:\"
	@echo \"  C++ Compiler: $(CXX)\"
	@echo \"  CUDA Compiler: $(NVCC)\"
	@echo \"  C++ Flags: $(CXXFLAGS)\"
	@echo \"  NVCC Flags: $(NVCCFLAGS)\"
	@echo \"  Libraries: $(LIBS)\"
	@echo \"  CUDA Path: $(CUDA_PATH)\"
	@echo \"  Objects: $(ALL_OBJECTS)\"
	@echo \"  Main Target: $(MAIN_TARGET)\"
	@echo \"  Shared Lib: $(LIB_TARGET)\"
	@echo \"  Static Lib: $(STATIC_LIB)\"

# Install (copy to system location)
install: all
	@echo \"📦 Installing HAAL binaries and libraries...\"
	@cp $(MAIN_TARGET) /usr/local/bin/haal-test 2>/dev/null || echo \"⚠️  Install requires sudo\"
	@cp $(LIB_TARGET) /usr/local/lib/ 2>/dev/null || echo \"⚠️  Library install requires sudo\"
	@cp $(STATIC_LIB) /usr/local/lib/ 2>/dev/null || echo \"⚠️  Library install requires sudo\"
	@cp include/haal-orc.h /usr/local/include/ 2>/dev/null || echo \"⚠️  Header install requires sudo\"
	@ldconfig 2>/dev/null || echo \"⚠️  ldconfig requires sudo\"
	@echo \"✅ Install complete (if successful)\"

# Development helpers
format:
	@echo \"🎨 Formatting source code...\"
	@find . -name \"*.cpp\" -o -name \"*.cu\" -o -name \"*.h\" | xargs clang-format -i 2>/dev/null || echo \"clang-format not found\"
	@echo \"✅ Code formatting complete\"

analyze:
	@echo \"🔍 Running static analysis...\"
	@cppcheck --enable=all --inconclusive --std=c++17 *.cpp *.cu 2>/dev/null || echo \"cppcheck not found\"
	@echo \"✅ Static analysis complete\"

# Package for distribution
package: all
	@echo \"📦 Creating distribution package...\"
	@mkdir -p haal-dist/lib haal-dist/include haal-dist/bin
	@cp $(LIB_TARGET) $(STATIC_LIB) haal-dist/lib/
	@cp include/haal-orc.h haal-dist/include/
	@cp $(MAIN_TARGET) haal-dist/bin/
	@tar czf haal-dist.tar.gz haal-dist/
	@rm -rf haal-dist/
	@echo \"✅ Package created: haal-dist.tar.gz\"

# Help target
help:
	@echo \"HAAL Orchestrator Build System\"
	@echo \"==============================\"
	@echo \"Primary Targets:\"
	@echo \"  all              - Build main test and libraries\"
	@echo \"  all-with-bench   - Build everything including benchmarks\"
	@echo \"  test             - Run real HAAL test\"
	@echo \"  test-lib         - Test shared library\"
	@echo \"\"
	@echo \"Individual Targets:\"
	@echo \"  $(MAIN_TARGET)   - Real HAAL test executable\"
	@echo \"  $(LIB_TARGET)    - Shared library for FFI\"
	@echo \"  $(STATIC_LIB)    - Static library\"
	@echo \"  $(CUDA_BENCH)    - CUDA benchmark\"
	@echo \"  $(AVX2_BENCH)    - AVX2 benchmark\"
	@echo \"\"
	@echo \"Utility Targets:\"
	@echo \"  clean            - Clean build artifacts\"
	@echo \"  rebuild          - Clean and rebuild\"
	@echo \"  info             - Show build information\"
	@echo \"  check-deps       - Check build dependencies\"
	@echo \"  install          - Install binaries to system\"
	@echo \"  package          - Create distribution package\"
	@echo \"  format           - Format source code\"
	@echo \"  analyze          - Run static analysis\"
	@echo \"  help             - Show this help\"

# Mark phony targets
.PHONY: all all-with-bench check-deps test test-cuda test-avx2 test-all test-lib clean rebuild info install format analyze package help

# Dependency tracking
-include $(ALL_OBJECTS:.o=.d)
$(OBJDIR)/%.d: %.cpp
	@$(CXX) $(CXXFLAGS) -MM -MT $(OBJDIR)/$*.o $< > $@
$(OBJDIR)/%.d: %.cu
	@$(NVCC) $(NVCCFLAGS) -M -MT $(OBJDIR)/$*.o $< > $@
