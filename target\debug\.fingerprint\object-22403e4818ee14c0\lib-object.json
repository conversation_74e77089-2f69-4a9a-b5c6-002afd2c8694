{"rustc": 1842507548689473721, "features": "[\"archive\", \"coff\", \"compression\", \"default\", \"elf\", \"macho\", \"pe\", \"read\", \"read_core\", \"std\", \"unaligned\", \"xcoff\"]", "declared_features": "[\"all\", \"alloc\", \"archive\", \"build\", \"build_core\", \"cargo-all\", \"coff\", \"compression\", \"core\", \"default\", \"doc\", \"elf\", \"macho\", \"pe\", \"read\", \"read_core\", \"rustc-dep-of-std\", \"std\", \"unaligned\", \"unstable\", \"unstable-all\", \"wasm\", \"write\", \"write_core\", \"write_std\", \"xcoff\"]", "target": 5743048264439000431, "profile": 2241668132362809309, "path": 9938854818288484454, "deps": [[3215782898819282321, "build_script_build", false, 9855125352720847359], [15667726689128706326, "ruzstd", false, 3258896768876578419], [15932120279885307830, "memchr", false, 1273370510615625013], [17772299992546037086, "flate2", false, 2048491562201829377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\object-22403e4818ee14c0\\dep-lib-object", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}