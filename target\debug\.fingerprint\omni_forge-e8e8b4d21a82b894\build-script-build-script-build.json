{"rustc": 1842507548689473721, "features": "[\"default\", \"hardware-acceleration\", \"libc\", \"ml-inference\", \"ndarray\", \"once_cell\", \"safetensors\"]", "declared_features": "[\"bindgen\", \"bindings-generation\", \"default\", \"hardware-acceleration\", \"libc\", \"ml-inference\", \"ndarray\", \"once_cell\", \"safetensors\"]", "target": 5408242616063297496, "profile": 7409704062750675268, "path": 13767053534773805487, "deps": [[7657997172424569738, "slint_build", false, 16172951663683728647], [15056754423999335055, "cc", false, 2327187655751450028]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\omni_forge-e8e8b4d21a82b894\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}