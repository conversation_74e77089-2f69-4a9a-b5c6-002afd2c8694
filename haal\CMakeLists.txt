# CMakeLists.txt for HAAL Orchestrator System
# Real hardware acceleration orchestrator with CUDA and AVX2 backends
# Enhanced with Intel OneAPI support for maximum AVX2 performance
# ~=####====A===r===c===M===o===o===n====S===t===u===d===i===o===s====X|0|$>
# @gitHub  : https://github.com/arcmoonstudios
# @copyright (c) 2025 ArcMoon Studios
# @license : MIT OR Apache-2.0
# <AUTHOR> Lord Xyn

cmake_minimum_required(VERSION 3.18)
project(HAAL_Orchestrator LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# ============================================================================
# INTEL ONEAPI DETECTION AND SETUP
# ============================================================================

# Try to find Intel OneAPI
set(INTEL_ONEAPI_PATHS
    "C:/Program Files (x86)/Intel/oneAPI"
    "C:/Program Files/Intel/oneAPI"
    "/opt/intel/oneapi"
    "$ENV{INTEL_ONEAPI_ROOT}"
)

set(INTEL_ONEAPI_FOUND FALSE)
foreach(path ${INTEL_ONEAPI_PATHS})
    if(EXISTS "${path}/compiler/latest/bin/icx" OR EXISTS "${path}/compiler/latest/bin/icx.exe" OR EXISTS "${path}/compiler/2025.2/bin/icx.exe")
        set(INTEL_ONEAPI_ROOT ${path})
        set(INTEL_ONEAPI_FOUND TRUE)
        break()
    endif()
endforeach()

if(INTEL_ONEAPI_FOUND)
    message(STATUS "✅ Intel OneAPI found at: ${INTEL_ONEAPI_ROOT}")
    
    # Set Intel compiler paths
    if(WIN32)
        set(CMAKE_CXX_COMPILER "${INTEL_ONEAPI_ROOT}/compiler/latest/bin/icx.exe")
        if(NOT EXISTS ${CMAKE_CXX_COMPILER})
            set(CMAKE_CXX_COMPILER "${INTEL_ONEAPI_ROOT}/compiler/2025.2/bin/icx.exe")
        endif()
    else()
        set(CMAKE_CXX_COMPILER "${INTEL_ONEAPI_ROOT}/compiler/latest/bin/icx")
    endif()
    
    # Intel library paths
    set(INTEL_MKL_ROOT "${INTEL_ONEAPI_ROOT}/mkl/latest")
    set(INTEL_TBB_ROOT "${INTEL_ONEAPI_ROOT}/tbb/latest")
    set(INTEL_IPP_ROOT "${INTEL_ONEAPI_ROOT}/ipp/latest")
    
    message(STATUS "✅ Using Intel C++ Compiler: ${CMAKE_CXX_COMPILER}")
    message(STATUS "✅ Intel MKL: ${INTEL_MKL_ROOT}")
    message(STATUS "✅ Intel TBB: ${INTEL_TBB_ROOT}")
    message(STATUS "✅ Intel IPP: ${INTEL_IPP_ROOT}")
    
    set(USE_INTEL_ONEAPI TRUE)
else()
    message(WARNING "⚠️ Intel OneAPI not found, using default compiler")
    set(USE_INTEL_ONEAPI FALSE)
endif()

# ============================================================================
# CUDA DETECTION AND SETUP
# ============================================================================

# Enable CUDA if available
find_package(CUDAToolkit QUIET)
if(CUDAToolkit_FOUND)
    enable_language(CUDA)
    message(STATUS "✅ CUDA found: ${CUDAToolkit_VERSION}")
    set(CUDA_ENABLED TRUE)
else()
    message(WARNING "⚠️ CUDA not found, building CPU-only version")
    set(CUDA_ENABLED FALSE)
endif()

# ============================================================================
# COMPILER FLAGS CONFIGURATION
# ============================================================================

if(USE_INTEL_ONEAPI)
    # Intel OneAPI Compiler Flags
    if(MSVC OR WIN32)
        # Windows Intel ICX flags
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /std:c++17 /O3 /QxHOST /Qipo")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /fp:fast /Qopenmp /EHsc")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /Qopt-report:2 /W3 /wd4996")
        message(STATUS "✅ Using Intel ICX with Windows optimizations")
    else()
        # Linux Intel ICX flags
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -O3 -xHOST -ipo")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fp-model fast -fopenmp -fPIC")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -qopt-report=2 -Wall -Wextra")
        message(STATUS "✅ Using Intel ICX with Linux optimizations")
    endif()
    
    # Intel library include paths
    include_directories("${INTEL_MKL_ROOT}/include")
    include_directories("${INTEL_TBB_ROOT}/include")
    include_directories("${INTEL_IPP_ROOT}/include")
    
    # Intel library link directories
    if(WIN32)
        link_directories("${INTEL_MKL_ROOT}/lib")
        link_directories("${INTEL_TBB_ROOT}/lib/intel64")
        link_directories("${INTEL_IPP_ROOT}/lib")
    else()
        link_directories("${INTEL_MKL_ROOT}/lib/intel64")
        link_directories("${INTEL_TBB_ROOT}/lib/intel64/gcc4.8")
        link_directories("${INTEL_IPP_ROOT}/lib/intel64")
    endif()
    
elif(MSVC)
    # Microsoft Visual Studio
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /std:c++17 /O2 /arch:AVX2 /openmp /EHsc")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /fp:fast /W3 /wd4996")
    message(STATUS "✅ Using MSVC with AVX2 optimization (/arch:AVX2)")
else()
    # GCC/Clang
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -O3 -mavx2 -mfma -march=native")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp -fPIC -ffast-math")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wno-unused-parameter")
    message(STATUS "✅ Using GCC/Clang with AVX2 optimization (-mavx2 -mfma)")
endif()

# Add definitions
add_definitions(-DAVX2_ENABLED)

if(CUDA_ENABLED)
    add_definitions(-DCUDA_ENABLED)
    
    # CUDA compiler flags
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -std=c++17 -O3")
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_75,code=sm_75")   # RTX 20/30 series
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_86,code=sm_86")   # RTX 30 series
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -gencode arch=compute_89,code=sm_89")   # RTX 40 series
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -use_fast_math --maxrregcount=64")
    
    if(USE_INTEL_ONEAPI AND WIN32)
        set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -Xcompiler /EHsc")
    elseif(MSVC)
        set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -Xcompiler /EHsc")
    else()
        set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -Xcompiler -fopenmp,-fPIC")
    endif()
    
    message(STATUS "✅ CUDA compilation flags: ${CMAKE_CUDA_FLAGS}")
else()
    add_definitions(-DCUDA_DISABLED)
endif()

# ============================================================================
# SOURCE FILES AND TARGETS
# ============================================================================

# Include directories
include_directories(include)

# Source files
set(CXX_SOURCES
    haal-orc.cpp
    haal-avx2.cpp          # Your working AVX2 implementation
    haal-c-api.cpp
)

# Add simple CUDA stubs when CUDA is disabled
if(NOT CUDA_ENABLED)
    list(APPEND CXX_SOURCES haal-cuda-stubs-simple.cpp)
endif()

set(CUDA_SOURCES
    haal-cuda.cu
    haal-cuda-launchers.cu
)

# Create object library for C++ sources
add_library(haal_cpp_objects OBJECT ${CXX_SOURCES})
target_include_directories(haal_cpp_objects PRIVATE include)

# Create object library for CUDA sources (if CUDA is available)
if(CUDA_ENABLED)
    add_library(haal_cuda_objects OBJECT ${CUDA_SOURCES})
    target_include_directories(haal_cuda_objects PRIVATE include)
    set_property(TARGET haal_cuda_objects PROPERTY CUDA_SEPARABLE_COMPILATION ON)
    
    message(STATUS "✅ Building CUDA sources: ${CUDA_SOURCES}")
endif()

# ============================================================================
# EXECUTABLES AND LIBRARIES
# ============================================================================

# Main test executable
add_executable(haal-test test/simple-haal-test.cpp)
target_include_directories(haal-test PRIVATE include)

# Link object libraries
target_link_libraries(haal-test haal_cpp_objects)

if(CUDA_ENABLED)
    target_link_libraries(haal-test haal_cuda_objects)
    
    # Link CUDA libraries
    target_link_libraries(haal-test CUDA::cudart CUDA::cublas CUDA::curand)
    message(STATUS "✅ Linked CUDA libraries")
endif()

# Link Intel libraries if using OneAPI
if(USE_INTEL_ONEAPI)
    if(WIN32)
        target_link_libraries(haal-test mkl_intel_lp64 mkl_intel_thread mkl_core tbb)
    else()
        target_link_libraries(haal-test mkl_intel_lp64 mkl_intel_thread mkl_core tbb iomp5)
    endif()
    message(STATUS "✅ Linked Intel OneAPI libraries")
endif()

# Link system libraries
if(WIN32)
    # Windows libraries
    target_link_libraries(haal-test)
else()
    # Unix libraries
    target_link_libraries(haal-test pthread m)
    
    # OpenMP
    find_package(OpenMP)
    if(OpenMP_CXX_FOUND)
        target_link_libraries(haal-test OpenMP::OpenMP_CXX)
        message(STATUS "✅ OpenMP found and linked")
    endif()
endif()

# Shared library for Rust FFI
add_library(haal_shared SHARED ${CXX_SOURCES})
target_include_directories(haal_shared PRIVATE include)

if(CUDA_ENABLED)
    target_sources(haal_shared PRIVATE ${CUDA_SOURCES})
    set_property(TARGET haal_shared PROPERTY CUDA_SEPARABLE_COMPILATION ON)
    target_link_libraries(haal_shared CUDA::cudart CUDA::cublas CUDA::curand)
endif()

if(USE_INTEL_ONEAPI)
    if(WIN32)
        target_link_libraries(haal_shared mkl_intel_lp64 mkl_intel_thread mkl_core tbb)
    else()
        target_link_libraries(haal_shared mkl_intel_lp64 mkl_intel_thread mkl_core tbb iomp5)
    endif()
endif()

# Static library
add_library(haal_static STATIC ${CXX_SOURCES})
target_include_directories(haal_static PRIVATE include)

if(CUDA_ENABLED)
    target_sources(haal_static PRIVATE ${CUDA_SOURCES})
    set_property(TARGET haal_static PROPERTY CUDA_SEPARABLE_COMPILATION ON)
endif()

# Set output names
set_target_properties(haal_shared PROPERTIES OUTPUT_NAME haal)
set_target_properties(haal_static PROPERTIES OUTPUT_NAME haal)

# ============================================================================
# CUSTOM TARGETS AND TESTING
# ============================================================================

# Create a simple test file if it doesn't exist
set(TEST_SOURCE "${CMAKE_CURRENT_SOURCE_DIR}/test/simple-haal-test.cpp")
if(NOT EXISTS ${TEST_SOURCE})
    file(MAKE_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/test")
    file(WRITE ${TEST_SOURCE}
"#include \"../include/haal-orc.hpp\"
#include <iostream>

int main() {
    std::cout << \"HAAL Test Program\" << std::endl;
    
    HaalOrchestrator orchestrator;
    if (orchestrator.initialize()) {
        std::cout << \"✅ HAAL Orchestrator initialized successfully\" << std::endl;
        
        auto metrics = orchestrator.getSystemMetrics();
        std::cout << \"📊 System metrics: \" << metrics.size() << \" values\" << std::endl;
        
        orchestrator.cleanup();
        std::cout << \"🧹 Cleanup complete\" << std::endl;
    } else {
        std::cerr << \"❌ HAAL Orchestrator initialization failed\" << std::endl;
        return 1;
    }
    
    return 0;
}
")
endif()

# Custom targets for testing
add_custom_target(test-haal
    COMMAND haal-test
    DEPENDS haal-test
    COMMENT "Running HAAL performance test"
)

add_custom_target(test-verbose
    COMMAND haal-test --verbose
    DEPENDS haal-test
    COMMENT "Running HAAL performance test with verbose output"
)

# Installation
install(TARGETS haal-test DESTINATION bin)
install(TARGETS haal_shared haal_static DESTINATION lib)
install(FILES include/haal-orc.hpp DESTINATION include)

# ============================================================================
# BUILD INFORMATION
# ============================================================================

# Print build information
message(STATUS "")
message(STATUS "🚀 HAAL Build Configuration:")
message(STATUS "=============================")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")

if(USE_INTEL_ONEAPI)
    message(STATUS "🎯 Intel OneAPI: ENABLED")
    message(STATUS "   Compiler optimization: HOST-specific with IPO")
    message(STATUS "   Math libraries: Intel MKL")
    message(STATUS "   Threading: Intel TBB + OpenMP")
else()
    message(STATUS "🎯 Standard compiler: ${CMAKE_CXX_COMPILER_ID}")
    message(STATUS "   AVX2 optimization: ENABLED")
endif()

message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")

if(CUDA_ENABLED)
    message(STATUS "🚀 CUDA: ENABLED")
    message(STATUS "   CUDA compiler: ${CMAKE_CUDA_COMPILER}")
    message(STATUS "   CUDA flags: ${CMAKE_CUDA_FLAGS}")
    message(STATUS "   CUDA version: ${CUDAToolkit_VERSION}")
else()
    message(STATUS "🚀 CUDA: DISABLED")
endif()

message(STATUS "")
message(STATUS "📦 Targets:")
message(STATUS "  haal-test     - Main performance test executable")
message(STATUS "  haal_shared   - Shared library for Rust FFI")
message(STATUS "  haal_static   - Static library")
message(STATUS "  test-haal     - Run performance test")
message(STATUS "  test-verbose  - Run performance test with verbose output")
message(STATUS "")

if(USE_INTEL_ONEAPI)
    message(STATUS "🎯 PERFORMANCE OPTIMIZATIONS ACTIVE:")
    message(STATUS "   ✅ Intel OneAPI ICX compiler")
    message(STATUS "   ✅ Host-specific optimization (-xHOST)")
    message(STATUS "   ✅ Inter-procedural optimization (-ipo)")
    message(STATUS "   ✅ Intel MKL math library")
    message(STATUS "   ✅ Intel TBB threading")
    message(STATUS "   ✅ Your optimized haal-avx2.cpp kernels")
    if(CUDA_ENABLED)
        message(STATUS "   ✅ CUDA GPU acceleration")
    endif()
    message(STATUS "")
    message(STATUS "💥 READY FOR MAXIMUM PERFORMANCE!")
endif()
