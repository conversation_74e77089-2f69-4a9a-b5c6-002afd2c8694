
Directory: config
File: mod.rs
============
// src/config/mod.rs
//! Configuration for the OmniForge compiler.
//!
//! This module defines the configuration options for the OmniForge compiler.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::PathBuf;
use serde::{Serialize, Deserialize};

/// Options for configuring the OmniForge compiler
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompilerOptions {
    /// Target language for code generation (rust, c, cpp)
    pub target_language: String,
    
    /// Optional include paths for resolving dependencies
    pub include_paths: Vec<PathBuf>,
    
    /// Optional library paths for linking
    pub library_paths: Vec<PathBuf>,
    
    /// Enable optimizations
    pub optimize: bool,
    
    /// Generate debug information
    pub debug_info: bool,
    
    /// Generate test code
    pub generate_tests: bool,
    
    /// Custom backend configurations
    pub backend_configs: BackendConfigs,
}

impl Default for CompilerOptions {
    fn default() -> Self {
        Self {
            target_language: "rust".to_string(),
            include_paths: Vec::new(),
            library_paths: Vec::new(),
            optimize: true,
            debug_info: false,
            generate_tests: false,
            backend_configs: BackendConfigs::default(),
        }
    }
}

/// Configuration options for specific backends
#[derive(Debug, Clone, Serialize, Deserialize)]
#[derive(Default)]
pub struct BackendConfigs {
    /// CUDA backend configuration
    pub cuda: CudaConfig,
    
    /// CPU SIMD backend configuration
    pub simd: SimdConfig,
}


/// Configuration options for the CUDA backend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CudaConfig {
    /// CUDA compute capability (e.g., "7.5")
    pub compute_capability: String,
    
    /// Path to CUDA toolkit
    pub cuda_path: Option<PathBuf>,
    
    /// Use fast math
    pub fast_math: bool,
}

impl Default for CudaConfig {
    fn default() -> Self {
        Self {
            compute_capability: "7.5".to_string(),
            cuda_path: None,
            fast_math: false,
        }
    }
}

/// Configuration options for the CPU SIMD backend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimdConfig {
    /// Enable AVX
    pub avx: bool,
    
    /// Enable AVX2
    pub avx2: bool,
    
    /// Enable AVX512
    pub avx512: bool,
    
    /// Enable NEON (ARM)
    pub neon: bool,
}

impl Default for SimdConfig {
    fn default() -> Self {
        Self {
            avx: true,
            avx2: true,
            avx512: false,
            neon: false,
        }
    }
}


