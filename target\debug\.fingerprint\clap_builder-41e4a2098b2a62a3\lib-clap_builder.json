{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 2963997777145268897, "deps": [[5820056977320921005, "anstream", false, 2551882730950347289], [9394696648929125047, "anstyle", false, 15621167410048084525], [11166530783118767604, "strsim", false, 8874474253676214565], [11649982696571033535, "clap_lex", false, 11944564910762341292]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-41e4a2098b2a62a3\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}