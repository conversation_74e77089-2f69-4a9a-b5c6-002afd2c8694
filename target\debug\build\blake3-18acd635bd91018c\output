cargo:rustc-check-cfg=cfg(blake3_sse2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx512_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_neon, values(none()))
cargo:rustc-check-cfg=cfg(blake3_wasm32_simd, values(none()))
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
OUT_DIR = Some(C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\_Repos\OmniCodex\target\debug\deps;C:\_Repos\OmniCodex\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\_Repos\._0Scripts;C:\_Repos\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
OUT_DIR = Some(C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rustc-cfg=blake3_sse2_ffi
cargo:rustc-cfg=blake3_sse41_ffi
cargo:rustc-cfg=blake3_avx2_ffi
OUT_DIR = Some(C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\_Repos\OmniCodex\target\debug\deps;C:\_Repos\OmniCodex\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\_Repos\._0Scripts;C:\_Repos\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: c/blake3_sse2_x86-64_windows_msvc.asm
 Assembling: c/blake3_sse41_x86-64_windows_msvc.asm
 Assembling: c/blake3_avx2_x86-64_windows_msvc.asm
AR_x86_64-pc-windows-msvc = None
AR_x86_64_pc_windows_msvc = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_pc_windows_msvc = None
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=blake3_sse2_sse41_avx2_assembly
cargo:rustc-link-search=native=C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_PREFER_INTRINSICS
cargo:rustc-cfg=blake3_avx512_ffi
OUT_DIR = Some(C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
VCINSTALLDIR = None
VSTEL_MSBuildProjectFullPath = None
VSCMD_ARG_VCVARS_SPECTRE = None
WindowsSdkDir = None
WindowsSDKVersion = None
LIB = None
PATH = Some(C:\_Repos\OmniCodex\target\debug\deps;C:\_Repos\OmniCodex\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\_Repos\._0Scripts;C:\_Repos\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
CC_x86_64-pc-windows-msvc = None
CC_x86_64_pc_windows_msvc = None
HOST_CC = None
CC = None
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_x86_64_pc_windows_msvc = None
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: c/blake3_avx512_x86-64_windows_msvc.asm
AR_x86_64-pc-windows-msvc = None
AR_x86_64_pc_windows_msvc = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_x86_64_pc_windows_msvc = None
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=blake3_avx512_assembly
cargo:rustc-link-search=native=C:\_Repos\OmniCodex\target\debug\build\blake3-18acd635bd91018c\out
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CC
cargo:rerun-if-env-changed=CFLAGS
cargo:rerun-if-changed=c\.gitignore
cargo:rerun-if-changed=c\blake3-config.cmake.in
cargo:rerun-if-changed=c\blake3.c
cargo:rerun-if-changed=c\blake3.h
cargo:rerun-if-changed=c\blake3_avx2.c
cargo:rerun-if-changed=c\blake3_avx2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_avx512.c
cargo:rerun-if-changed=c\blake3_avx512_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_avx512_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_dispatch.c
cargo:rerun-if-changed=c\blake3_impl.h
cargo:rerun-if-changed=c\blake3_neon.c
cargo:rerun-if-changed=c\blake3_portable.c
cargo:rerun-if-changed=c\blake3_sse2.c
cargo:rerun-if-changed=c\blake3_sse2_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_sse41.c
cargo:rerun-if-changed=c\blake3_sse41_x86-64_unix.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_gnu.S
cargo:rerun-if-changed=c\blake3_sse41_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c\blake3_tbb.cpp
cargo:rerun-if-changed=c\cmake
cargo:rerun-if-changed=c\CMakeLists.txt
cargo:rerun-if-changed=c\CMakePresets.json
cargo:rerun-if-changed=c\dependencies
cargo:rerun-if-changed=c\example.c
cargo:rerun-if-changed=c\example_tbb.c
cargo:rerun-if-changed=c\libblake3.pc.in
cargo:rerun-if-changed=c\main.c
cargo:rerun-if-changed=c\Makefile.testing
cargo:rerun-if-changed=c\README.md
cargo:rerun-if-changed=c\test.py
