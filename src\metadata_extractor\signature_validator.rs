// src/metadata_extractor/signature_validator.rs
//! Signature validator for the OmniForge compiler.
//!
//! This module provides functionality for validating function signatures
//! and ensuring they are compatible across different backends.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use crate::error::{OmniError, OmniResult};
use super::{ExtractedSignature, ExtractedType};

/// Signature validator
pub struct SignatureValidator {
    // Configuration options can be added here
}

impl Default for SignatureValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl SignatureValidator {
    /// Create a new signature validator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Validate a function signature
    pub fn validate_signature(&self, signature: &ExtractedSignature) -> OmniResult<()> {
        // Validate return type
        self.validate_type(&signature.return_type)?;
        
        // Validate parameter types
        for param in &signature.parameter_types {
            self.validate_type(&param.type_info)?;
        }
        
        Ok(())
    }
    
    /// Validate a type
    fn validate_type(&self, type_info: &ExtractedType) -> OmniResult<()> {
        // Check for unsupported types
        if type_info.name.contains("void") && type_info.is_pointer {
            // void* is a special case
            return Ok(());
        }
        
        if type_info.name.contains("void") && !type_info.is_pointer {
            // void is only valid as a return type
            return Ok(());
        }
        
        // Ensure size is known for non-void types
        if !type_info.name.contains("void") && type_info.size.is_none() {
            return Err(OmniError::MetadataExtraction(
                format!("Type size unknown for {}", type_info.name)
            ));
        }
        
        // Check array dimensions
        if type_info.is_array && type_info.array_dimensions.is_empty() {
            return Err(OmniError::MetadataExtraction(
                format!("Array type {} has no dimensions", type_info.name)
            ));
        }
        
        Ok(())
    }
    
    /// Check if two signatures are compatible
    pub fn are_signatures_compatible(&self, sig1: &ExtractedSignature, sig2: &ExtractedSignature) -> bool {
        // Check return type compatibility
        if !self.are_types_compatible(&sig1.return_type, &sig2.return_type) {
            return false;
        }
        
        // Check parameter count
        if sig1.parameter_types.len() != sig2.parameter_types.len() {
            return false;
        }
        
        // Check parameter type compatibility
        for (param1, param2) in sig1.parameter_types.iter().zip(sig2.parameter_types.iter()) {
            if !self.are_types_compatible(&param1.type_info, &param2.type_info) {
                return false;
            }
        }
        
        // Check variadic compatibility
        if sig1.is_variadic != sig2.is_variadic {
            return false;
        }
        
        true
    }
    
    /// Check if two types are compatible
    fn are_types_compatible(&self, type1: &ExtractedType, type2: &ExtractedType) -> bool {
        // Check basic compatibility
        if type1.is_pointer != type2.is_pointer {
            return false;
        }
        
        if type1.is_array != type2.is_array {
            return false;
        }
        
        // Special case for void
        if type1.name.contains("void") && type2.name.contains("void") {
            return true;
        }
        
        // Check array dimensions
        if type1.is_array && type2.is_array
            && type1.array_dimensions != type2.array_dimensions {
                return false;
            }
        
        // Check size compatibility
        if let (Some(size1), Some(size2)) = (type1.size, type2.size) {
            if size1 != size2 {
                return false;
            }
        }
        
        // Check const and volatile compatibility
        // In most cases, const/volatile on parameters doesn't affect binary compatibility
        // but they can affect semantics
        
        true
    }
}
