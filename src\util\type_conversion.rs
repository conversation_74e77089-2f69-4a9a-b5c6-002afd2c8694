// src/util/type_conversion.rs
//! Type conversion utilities for the OmniForge compiler.
//!
//! This module provides type mapping and conversion functionality between
//! different programming languages, supporting the code generation process
//! for various target languages in the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use std::sync::OnceLock;

use crate::codegen::ArgType;
use crate::error::{OmniError, OmniResult};

/// Type conversion maps are lazily initialized to avoid initialization overhead
static RUST_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static C_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static CPP_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static PYTHON_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static TS_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();

/// Initialize the Rust type map
fn get_rust_type_map() -> &'static HashMap<ArgType, &'static str> {
    RUST_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "()");
        map.insert(ArgType::I8, "i8");
        map.insert(ArgType::I16, "i16");
        map.insert(ArgType::I32, "i32");
        map.insert(ArgType::I64, "i64");
        map.insert(ArgType::U8, "u8");
        map.insert(ArgType::U16, "u16");
        map.insert(ArgType::U32, "u32");
        map.insert(ArgType::U64, "u64");
        map.insert(ArgType::F32, "f32");
        map.insert(ArgType::F64, "f64");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "*mut i8");
        map.insert(ArgType::I16Ptr, "*mut i16");
        map.insert(ArgType::I32Ptr, "*mut i32");
        map.insert(ArgType::I64Ptr, "*mut i64");
        map.insert(ArgType::U8Ptr, "*mut u8");
        map.insert(ArgType::U16Ptr, "*mut u16");
        map.insert(ArgType::U32Ptr, "*mut u32");
        map.insert(ArgType::U64Ptr, "*mut u64");
        map.insert(ArgType::F32Ptr, "*mut f32");
        map.insert(ArgType::F64Ptr, "*mut f64");
        map.insert(ArgType::BoolPtr, "*mut bool");
        map.insert(ArgType::VoidPtr, "*mut std::ffi::c_void");
        
        map
    })
}

/// Initialize the C type map
fn get_c_type_map() -> &'static HashMap<ArgType, &'static str> {
    C_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "int8_t");
        map.insert(ArgType::I16, "int16_t");
        map.insert(ArgType::I32, "int32_t");
        map.insert(ArgType::I64, "int64_t");
        map.insert(ArgType::U8, "uint8_t");
        map.insert(ArgType::U16, "uint16_t");
        map.insert(ArgType::U32, "uint32_t");
        map.insert(ArgType::U64, "uint64_t");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "double");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "int8_t*");
        map.insert(ArgType::I16Ptr, "int16_t*");
        map.insert(ArgType::I32Ptr, "int32_t*");
        map.insert(ArgType::I64Ptr, "int64_t*");
        map.insert(ArgType::U8Ptr, "uint8_t*");
        map.insert(ArgType::U16Ptr, "uint16_t*");
        map.insert(ArgType::U32Ptr, "uint32_t*");
        map.insert(ArgType::U64Ptr, "uint64_t*");
        map.insert(ArgType::F32Ptr, "float*");
        map.insert(ArgType::F64Ptr, "double*");
        map.insert(ArgType::BoolPtr, "bool*");
        map.insert(ArgType::VoidPtr, "void*");
        
        map
    })
}

/// Initialize the C++ type map
fn get_cpp_type_map() -> &'static HashMap<ArgType, &'static str> {
    CPP_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "std::int8_t");
        map.insert(ArgType::I16, "std::int16_t");
        map.insert(ArgType::I32, "std::int32_t");
        map.insert(ArgType::I64, "std::int64_t");
        map.insert(ArgType::U8, "std::uint8_t");
        map.insert(ArgType::U16, "std::uint16_t");
        map.insert(ArgType::U32, "std::uint32_t");
        map.insert(ArgType::U64, "std::uint64_t");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "double");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "std::int8_t*");
        map.insert(ArgType::I16Ptr, "std::int16_t*");
        map.insert(ArgType::I32Ptr, "std::int32_t*");
        map.insert(ArgType::I64Ptr, "std::int64_t*");
        map.insert(ArgType::U8Ptr, "std::uint8_t*");
        map.insert(ArgType::U16Ptr, "std::uint16_t*");
        map.insert(ArgType::U32Ptr, "std::uint32_t*");
        map.insert(ArgType::U64Ptr, "std::uint64_t*");
        map.insert(ArgType::F32Ptr, "float*");
        map.insert(ArgType::F64Ptr, "double*");
        map.insert(ArgType::BoolPtr, "bool*");
        map.insert(ArgType::VoidPtr, "void*");
        
        map
    })
}

/// Initialize the Python type map
fn get_python_type_map() -> &'static HashMap<ArgType, &'static str> {
    PYTHON_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "None");
        map.insert(ArgType::I8, "int");
        map.insert(ArgType::I16, "int");
        map.insert(ArgType::I32, "int");
        map.insert(ArgType::I64, "int");
        map.insert(ArgType::U8, "int");
        map.insert(ArgType::U16, "int");
        map.insert(ArgType::U32, "int");
        map.insert(ArgType::U64, "int");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "float");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "np.ndarray");
        map.insert(ArgType::I16Ptr, "np.ndarray");
        map.insert(ArgType::I32Ptr, "np.ndarray");
        map.insert(ArgType::I64Ptr, "np.ndarray");
        map.insert(ArgType::U8Ptr, "np.ndarray");
        map.insert(ArgType::U16Ptr, "np.ndarray");
        map.insert(ArgType::U32Ptr, "np.ndarray");
        map.insert(ArgType::U64Ptr, "np.ndarray");
        map.insert(ArgType::F32Ptr, "np.ndarray");
        map.insert(ArgType::F64Ptr, "np.ndarray");
        map.insert(ArgType::BoolPtr, "np.ndarray");
        map.insert(ArgType::VoidPtr, "ctypes.c_void_p");
        
        map
    })
}

/// Initialize the TypeScript type map
fn get_ts_type_map() -> &'static HashMap<ArgType, &'static str> {
    TS_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "number");
        map.insert(ArgType::I16, "number");
        map.insert(ArgType::I32, "number");
        map.insert(ArgType::I64, "bigint");
        map.insert(ArgType::U8, "number");
        map.insert(ArgType::U16, "number");
        map.insert(ArgType::U32, "number");
        map.insert(ArgType::U64, "bigint");
        map.insert(ArgType::F32, "number");
        map.insert(ArgType::F64, "number");
        map.insert(ArgType::Bool, "boolean");
        map.insert(ArgType::I8Ptr, "Int8Array");
        map.insert(ArgType::I16Ptr, "Int16Array");
        map.insert(ArgType::I32Ptr, "Int32Array");
        map.insert(ArgType::I64Ptr, "BigInt64Array");
        map.insert(ArgType::U8Ptr, "Uint8Array");
        map.insert(ArgType::U16Ptr, "Uint16Array");
        map.insert(ArgType::U32Ptr, "Uint32Array");
        map.insert(ArgType::U64Ptr, "BigUint64Array");
        map.insert(ArgType::F32Ptr, "Float32Array");
        map.insert(ArgType::F64Ptr, "Float64Array");
        map.insert(ArgType::BoolPtr, "Uint8Array");
        map.insert(ArgType::VoidPtr, "ArrayBuffer");
        
        map
    })
}

/// Convert an ArgType to a Rust type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
///
/// # Returns
///
/// * `OmniResult<String>` - The Rust type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_rust_type;
///
/// let rust_type = convert_to_rust_type(&ArgType::I32, false).unwrap();
/// assert_eq!(rust_type, "i32");
///
/// let const_ptr = convert_to_rust_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(const_ptr, "*const f32");
/// ```
pub fn convert_to_rust_type(arg_type: &ArgType, const_qualifier: bool) -> OmniResult<String> {
    let type_map = get_rust_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier for pointers
        if const_qualifier && arg_type_name.contains("mut") {
            Ok(arg_type_name.replace("*mut", "*const"))
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for Rust: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a C type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
///
/// # Returns
///
/// * `OmniResult<String>` - The C type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_c_type;
///
/// let c_type = convert_to_c_type(&ArgType::I32, false).unwrap();
/// assert_eq!(c_type, "int32_t");
///
/// let const_ptr = convert_to_c_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(const_ptr, "const float*");
/// ```
pub fn convert_to_c_type(arg_type: &ArgType, const_qualifier: bool) -> OmniResult<String> {
    let type_map = get_c_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier for pointers
        if const_qualifier && arg_type_name.contains('*') {
            Ok(format!("const {arg_type_name}"))
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for C: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a C++ type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
/// * `use_references` - Whether to use references instead of pointers
///
/// # Returns
///
/// * `OmniResult<String>` - The C++ type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_cpp_type;
///
/// let cpp_type = convert_to_cpp_type(&ArgType::I32, false, false).unwrap();
/// assert_eq!(cpp_type, "std::int32_t");
///
/// let const_ref = convert_to_cpp_type(&ArgType::F32Ptr, true, true).unwrap();
/// assert_eq!(const_ref, "const float&");
/// ```
pub fn convert_to_cpp_type(arg_type: &ArgType, const_qualifier: bool, use_references: bool) -> OmniResult<String> {
    let type_map = get_cpp_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier and references for pointers
        if arg_type_name.contains('*') {
            if use_references {
                // Convert pointer to reference
                let base_type = arg_type_name.replace('*', "");
                if const_qualifier {
                    Ok(format!("const {base_type}&"))
                } else {
                    Ok(format!("{base_type}&"))
                }
            } else if const_qualifier {
                // Use const pointer
                Ok(format!("const {arg_type_name}"))
            } else {
                Ok(arg_type_name.to_string())
            }
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for C++: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a Python type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `use_type_hints` - Whether to generate type hints
///
/// # Returns
///
/// * `OmniResult<String>` - The Python type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_python_type;
///
/// let py_type = convert_to_python_type(&ArgType::I32, true).unwrap();
/// assert_eq!(py_type, "int");
///
/// let array_type = convert_to_python_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(array_type, "np.ndarray");
/// ```
pub fn convert_to_python_type(arg_type: &ArgType, use_type_hints: bool) -> OmniResult<String> {
    if !use_type_hints {
        return Ok(String::new());
    }
    
    let type_map = get_python_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        Ok(arg_type_name.to_string())
    } else if let ArgType::Custom(name) = arg_type {
        Ok(format!("Any # {name}"))
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for Python: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a TypeScript type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
///
/// # Returns
///
/// * `OmniResult<String>` - The TypeScript type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_ts_type;
///
/// let ts_type = convert_to_ts_type(&ArgType::I32).unwrap();
/// assert_eq!(ts_type, "number");
///
/// let array_type = convert_to_ts_type(&ArgType::F32Ptr).unwrap();
/// assert_eq!(array_type, "Float32Array");
/// ```
pub fn convert_to_ts_type(arg_type: &ArgType) -> OmniResult<String> {
    let type_map = get_ts_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        Ok(arg_type_name.to_string())
    } else if let ArgType::Custom(name) = arg_type {
        Ok(format!("any /* {name} */"))
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for TypeScript: {arg_type:?}"
        )))
    }
}

/// Get the size of a type in bytes
///
/// # Arguments
///
/// * `arg_type` - The ArgType to get the size of
///
/// # Returns
///
/// * `OmniResult<usize>` - The size in bytes
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::get_type_size;
///
/// let size = get_type_size(&ArgType::I32).unwrap();
/// assert_eq!(size, 4);
/// ```
#[allow(dead_code)]
pub fn get_type_size(arg_type: &ArgType) -> OmniResult<usize> {
    match arg_type {
        ArgType::Void => Ok(0),
        ArgType::I8 | ArgType::U8 | ArgType::Bool => Ok(1),
        ArgType::I16 | ArgType::U16 => Ok(2),
        ArgType::I32 | ArgType::U32 | ArgType::F32 => Ok(4),
        ArgType::I64 | ArgType::U64 | ArgType::F64 => Ok(8),
        ArgType::I8Ptr | ArgType::I16Ptr | ArgType::I32Ptr | ArgType::I64Ptr |
        ArgType::U8Ptr | ArgType::U16Ptr | ArgType::U32Ptr | ArgType::U64Ptr |
        ArgType::F32Ptr | ArgType::F64Ptr | ArgType::BoolPtr | ArgType::VoidPtr => {
            #[cfg(target_pointer_width = "64")]
            return Ok(8);
            #[cfg(target_pointer_width = "32")]
            return Ok(4);
        }
        ArgType::Custom(_) => {
            Err(OmniError::General(format!(
                "Cannot determine size of custom type: {arg_type:?}"
            )))
        }
    }
}

/// Get the alignment of a type in bytes
///
/// # Arguments
///
/// * `arg_type` - The ArgType to get the alignment of
///
/// # Returns
///
/// * `OmniResult<usize>` - The alignment in bytes
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::get_type_alignment;
///
/// let alignment = get_type_alignment(&ArgType::I32).unwrap();
/// assert_eq!(alignment, 4);
/// ```
#[allow(dead_code)]
pub fn get_type_alignment(arg_type: &ArgType) -> OmniResult<usize> {
    match arg_type {
        ArgType::Void => Ok(1), // Void has alignment 1 by convention
        ArgType::I8 | ArgType::U8 | ArgType::Bool => Ok(1),
        ArgType::I16 | ArgType::U16 => Ok(2),
        ArgType::I32 | ArgType::U32 | ArgType::F32 => Ok(4),
        ArgType::I64 | ArgType::U64 | ArgType::F64 => Ok(8),
        ArgType::I8Ptr | ArgType::I16Ptr | ArgType::I32Ptr | ArgType::I64Ptr |
        ArgType::U8Ptr | ArgType::U16Ptr | ArgType::U32Ptr | ArgType::U64Ptr |
        ArgType::F32Ptr | ArgType::F64Ptr | ArgType::BoolPtr | ArgType::VoidPtr => {
            #[cfg(target_pointer_width = "64")]
            return Ok(8);
            #[cfg(target_pointer_width = "32")]
            return Ok(4);
        }
        ArgType::Custom(_) => {
            Err(OmniError::General(format!(
                "Cannot determine alignment of custom type: {arg_type:?}"
            )))
        }
    }
}

/// Parse a type string into an ArgType
///
/// # Arguments
///
/// * `type_string` - The type string to parse
/// * `language` - The programming language of the type string
///
/// # Returns
///
/// * `OmniResult<ArgType>` - The parsed ArgType
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::parse_type_string;
///
/// let arg_type = parse_type_string("int32_t", "c").unwrap();
/// assert_eq!(arg_type, ArgType::I32);
///
/// let ptr_type = parse_type_string("float*", "c").unwrap();
/// assert_eq!(ptr_type, ArgType::F32Ptr);
/// ```
#[allow(dead_code)]
pub fn parse_type_string(type_string: &str, language: &str) -> OmniResult<ArgType> {
    // Remove const qualifier and trim whitespace
    let type_string = type_string
        .replace("const ", "")
        .replace("&", "")
        .trim()
        .to_string();
    
    // Check if it's a pointer type
    let is_pointer = type_string.contains('*');
    let base_type = type_string.replace('*', "").trim().to_string();
    
    match language.to_lowercase().as_str() {
        "c" | "cpp" => {
            // Parse C/C++ type string
            match base_type.as_str() {
                "void" => {
                    if is_pointer {
                        Ok(ArgType::VoidPtr)
                    } else {
                        Ok(ArgType::Void)
                    }
                }
                "char" | "int8_t" | "std::int8_t" => {
                    if is_pointer {
                        Ok(ArgType::I8Ptr)
                    } else {
                        Ok(ArgType::I8)
                    }
                }
                "short" | "int16_t" | "std::int16_t" => {
                    if is_pointer {
                        Ok(ArgType::I16Ptr)
                    } else {
                        Ok(ArgType::I16)
                    }
                }
                "int" | "int32_t" | "std::int32_t" => {
                    if is_pointer {
                        Ok(ArgType::I32Ptr)
                    } else {
                        Ok(ArgType::I32)
                    }
                }
                "long long" | "int64_t" | "std::int64_t" => {
                    if is_pointer {
                        Ok(ArgType::I64Ptr)
                    } else {
                        Ok(ArgType::I64)
                    }
                }
                "unsigned char" | "uint8_t" | "std::uint8_t" => {
                    if is_pointer {
                        Ok(ArgType::U8Ptr)
                    } else {
                        Ok(ArgType::U8)
                    }
                }
                "unsigned short" | "uint16_t" | "std::uint16_t" => {
                    if is_pointer {
                        Ok(ArgType::U16Ptr)
                    } else {
                        Ok(ArgType::U16)
                    }
                }
                "unsigned int" | "uint32_t" | "std::uint32_t" => {
                    if is_pointer {
                        Ok(ArgType::U32Ptr)
                    } else {
                        Ok(ArgType::U32)
                    }
                }
                "unsigned long long" | "uint64_t" | "std::uint64_t" => {
                    if is_pointer {
                        Ok(ArgType::U64Ptr)
                    } else {
                        Ok(ArgType::U64)
                    }
                }
                "float" => {
                    if is_pointer {
                        Ok(ArgType::F32Ptr)
                    } else {
                        Ok(ArgType::F32)
                    }
                }
                "double" => {
                    if is_pointer {
                        Ok(ArgType::F64Ptr)
                    } else {
                        Ok(ArgType::F64)
                    }
                }
                "bool" => {
                    if is_pointer {
                        Ok(ArgType::BoolPtr)
                    } else {
                        Ok(ArgType::Bool)
                    }
                }
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "rust" => {
            // Parse Rust type string
            match base_type.as_str() {
                "()" => Ok(ArgType::Void),
                "i8" => {
                    if is_pointer {
                        Ok(ArgType::I8Ptr)
                    } else {
                        Ok(ArgType::I8)
                    }
                }
                "i16" => {
                    if is_pointer {
                        Ok(ArgType::I16Ptr)
                    } else {
                        Ok(ArgType::I16)
                    }
                }
                "i32" => {
                    if is_pointer {
                        Ok(ArgType::I32Ptr)
                    } else {
                        Ok(ArgType::I32)
                    }
                }
                "i64" => {
                    if is_pointer {
                        Ok(ArgType::I64Ptr)
                    } else {
                        Ok(ArgType::I64)
                    }
                }
                "u8" => {
                    if is_pointer {
                        Ok(ArgType::U8Ptr)
                    } else {
                        Ok(ArgType::U8)
                    }
                }
                "u16" => {
                    if is_pointer {
                        Ok(ArgType::U16Ptr)
                    } else {
                        Ok(ArgType::U16)
                    }
                }
                "u32" => {
                    if is_pointer {
                        Ok(ArgType::U32Ptr)
                    } else {
                        Ok(ArgType::U32)
                    }
                }
                "u64" => {
                    if is_pointer {
                        Ok(ArgType::U64Ptr)
                    } else {
                        Ok(ArgType::U64)
                    }
                }
                "f32" => {
                    if is_pointer {
                        Ok(ArgType::F32Ptr)
                    } else {
                        Ok(ArgType::F32)
                    }
                }
                "f64" => {
                    if is_pointer {
                        Ok(ArgType::F64Ptr)
                    } else {
                        Ok(ArgType::F64)
                    }
                }
                "bool" => {
                    if is_pointer {
                        Ok(ArgType::BoolPtr)
                    } else {
                        Ok(ArgType::Bool)
                    }
                }
                "c_void" => {
                    if is_pointer {
                        Ok(ArgType::VoidPtr)
                    } else {
                        Ok(ArgType::Void)
                    }
                }
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "python" => {
            // Parse Python type string
            match base_type.as_str() {
                "None" => Ok(ArgType::Void),
                "int" => {
                    // Default to 32-bit integer for Python int
                    Ok(ArgType::I32)
                }
                "float" => {
                    // Default to 64-bit float for Python float
                    Ok(ArgType::F64)
                }
                "bool" => Ok(ArgType::Bool),
                "np.ndarray" | "ndarray" => {
                    // Default to float32 array for NumPy arrays
                    Ok(ArgType::F32Ptr)
                }
                "ctypes.c_void_p" | "c_void_p" => Ok(ArgType::VoidPtr),
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "typescript" | "ts" => {
            // Parse TypeScript type string
            match base_type.as_str() {
                "void" => Ok(ArgType::Void),
                "number" => {
                    // Default to 32-bit float for TypeScript number
                    Ok(ArgType::F32)
                }
                "bigint" => Ok(ArgType::I64),
                "boolean" => Ok(ArgType::Bool),
                "Int8Array" => Ok(ArgType::I8Ptr),
                "Int16Array" => Ok(ArgType::I16Ptr),
                "Int32Array" => Ok(ArgType::I32Ptr),
                "BigInt64Array" => Ok(ArgType::I64Ptr),
                "Uint8Array" => Ok(ArgType::U8Ptr),
                "Uint16Array" => Ok(ArgType::U16Ptr),
                "Uint32Array" => Ok(ArgType::U32Ptr),
                "BigUint64Array" => Ok(ArgType::U64Ptr),
                "Float32Array" => Ok(ArgType::F32Ptr),
                "Float64Array" => Ok(ArgType::F64Ptr),
                "ArrayBuffer" => Ok(ArgType::VoidPtr),
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        _ => Err(OmniError::General(format!(
            "Unsupported language for type parsing: {language}"
        ))),
    }
}
