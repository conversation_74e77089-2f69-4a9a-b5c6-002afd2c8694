{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"libm\", \"no-std-float\", \"std\"]", "target": 15590299274629163093, "profile": 2225463790103693989, "path": 9677305344683606778, "deps": [[6060572338472977546, "strict_num", false, 7608510183732204317], [6511429716036861196, "bytemuck", false, 5951158175048559049], [9529943735784919782, "arrayref", false, 3078833423296867223]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tiny-skia-path-5e688278aa7492a1\\dep-lib-tiny_skia_path", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}