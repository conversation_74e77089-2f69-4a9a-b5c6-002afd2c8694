{"rustc": 1842507548689473721, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 15034364054385175746, "deps": [[5230392855116717286, "equivalent", false, 17974223323067518117], [9150530836556604396, "allocator_api2", false, 15653621677333136682], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 13380374535859572736]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-9ee53d3ab06abb10\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}