{"rustc": 1842507548689473721, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 15034364054385175746, "deps": [[5230392855116717286, "equivalent", false, 8170834011093646544], [9150530836556604396, "allocator_api2", false, 11298044515897866285], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 14427297551508565386]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-33aac186b642ee74\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}