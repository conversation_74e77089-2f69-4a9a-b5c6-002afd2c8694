{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16115957479182443466, "deps": [[1988483478007900009, "unicode_ident", false, 4812429966834244250], [3060637413840920116, "proc_macro2", false, 16584593718369059566], [17990358020177143287, "quote", false, 9129625013016073763]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-da4b71f7252aed08\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}