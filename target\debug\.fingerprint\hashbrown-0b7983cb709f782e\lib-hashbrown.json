{"rustc": 1842507548689473721, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\", \"rayon\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2225463790103693989, "path": 15034364054385175746, "deps": [[5230392855116717286, "equivalent", false, 12807643278499743999], [9150530836556604396, "allocator_api2", false, 4302046486808427930], [10697383615564341592, "rayon", false, 13297138483076549648], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 7136756019726486440]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-0b7983cb709f782e\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}