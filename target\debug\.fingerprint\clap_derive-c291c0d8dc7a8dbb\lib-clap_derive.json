{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 5896785871467616221, "path": 10195803239538842869, "deps": [[3060637413840920116, "proc_macro2", false, 16584593718369059566], [4974441333307933176, "syn", false, 12478268728860239467], [13077543566650298139, "heck", false, 12138543680695971572], [17990358020177143287, "quote", false, 9129625013016073763]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_derive-c291c0d8dc7a8dbb\\dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}