{"rustc": 1842507548689473721, "features": "[\"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "target": 1600304521922079521, "profile": 2241668132362809309, "path": 5877689423011159055, "deps": [[3722963349756955755, "once_cell", false, 4683668061861388514], [4143744114649553716, "raw_window_handle", false, 5354778983053800373], [6763978947554154845, "windows_sys", false, 2435190740221580436], [7191709312698686449, "glutin_egl_sys", false, 2914513935537944428], [7478509218540386589, "glutin_wgl_sys", false, 3039621911371321738], [7896293946984509699, "bitflags", false, 14117725511662906093], [10058659651543567831, "build_script_build", false, 5104078229347816000], [13587469111750606423, "libloading", false, 6394928164136643032]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\glutin-36180668e166d1bd\\dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}