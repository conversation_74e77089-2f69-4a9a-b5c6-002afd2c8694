{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 15657897354478470176, "path": 678108482320985702, "deps": [[5157631553186200874, "num_traits", false, 12338405551431000230], [12319020793864570031, "num_complex", false, 2679412271528647336], [15709748443193639506, "rawpointer", false, 16593576879022197881], [15826188163127377936, "matrixmultiply", false, 16116583601979591313], [16795989132585092538, "num_integer", false, 12230615043584646442]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-6a4b292073105fa0\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}