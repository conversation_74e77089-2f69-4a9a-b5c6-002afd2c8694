cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=haal/
cargo:rerun-if-changed=ui/
cargo:rerun-if-changed=src/gui/
cargo:warning=Building HAAL (Hardware Acceleration Abstraction Layer)
cargo:rustc-env=CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8
cargo:warning=CUDA detected - enabling GPU acceleration
🚀 Building HAAL with your optimized kernels
🚀 Building with CUDA support
   CUDA Path: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8
🔧 Compiling CUDA files with nvcc
✅ CUDA compilation successful
OUT_DIR = Some(C:\_Repos\OmniCodex\target\debug\build\omni_forge-d61bca2850656879\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\_Repos\OmniCodex\target\debug\deps;C:\_Repos\OmniCodex\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\WindowsApps\Microsoft.PowerShell_7.5.2.0_x64__8wekyb3d8bbwe;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\libnvvp;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;C:\Program Files\NVIDIA Corporation\Nsight Compute 2025.1.0\;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\.console-ninja\.bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\mingw64\bin;C:\tools;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\_Repos\._0Scripts;C:\_Repos\;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
haal-orc.cpp
haal-avx2.cpp
haal-c-api.cpp
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.44.35207\atlmfc\lib\x64
cargo:rustc-link-lib=static=haal_cpp
cargo:rustc-link-search=native=C:\_Repos\OmniCodex\target\debug\build\omni_forge-d61bca2850656879\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-search=native=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8/lib/x64
cargo:rustc-link-lib=cudart
cargo:rustc-link-lib=cublas
cargo:rustc-link-search=native=C:\_Repos\OmniCodex\target\debug\build\omni_forge-d61bca2850656879\out
cargo:rustc-link-lib=static=haal_cuda
✅ HAAL build completed successfully
cargo:warning=Compiling Slint UI files
cargo:warning=Exported component 'CodeEditor' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:785:18
cargo:warning=    |
cargo:warning=785 | export component CodeEditor inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberButton' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:64:18
cargo:warning=    |
cargo:warning=64  | export component CyberButton inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberInputField' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:610:18
cargo:warning=    |
cargo:warning=610 | export component CyberInputField inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberPanel' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:467:18
cargo:warning=    |
cargo:warning=467 | export component CyberPanel inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'CyberProgressBar' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:551:18
cargo:warning=    |
cargo:warning=551 | export component CyberProgressBar inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'DragDropZone' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:363:18
cargo:warning=    |
cargo:warning=363 | export component DragDropZone inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'FileSearchPanel' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:224:18
cargo:warning=    |
cargo:warning=224 | export component FileSearchPanel inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'FileTreeItem' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:706:18
cargo:warning=    |
cargo:warning=706 | export component FileTreeItem inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Exported component 'StatusIndicator' doesn't inherit Window. No code will be generated for it
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:653:18
cargo:warning=    |
cargo:warning=653 | export component StatusIndicator inherits Rectangle {
cargo:warning=    |                  ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:210:22
cargo:warning=    |
cargo:warning=210 |     in-out property <easing> chrome-smooth: cubic-bezier(0.23, 1, 0.32, 1);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:207:22
cargo:warning=    |
cargo:warning=207 |     in-out property <easing> cyber-ease:    cubic-bezier(0.25, 0.1, 0.25, 1);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:209:22
cargo:warning=    |
cargo:warning=209 |     in-out property <easing> glitch-snap:   cubic-bezier(0.68, -0.55, 0.265, 1.55);
cargo:warning=    |                      ^
cargo:warning=Properties of type easing are not supported yet for public API. The property will not be exposed
cargo:warning=   --> C:\_Repos\OmniCodex\ui\omniforge.slint:208:22
cargo:warning=    |
cargo:warning=208 |     in-out property <easing> neon-pulse:    cubic-bezier(0.4, 0, 0.6, 1);
cargo:warning=    |                      ^
cargo:rerun-if-changed=C:\_Repos\OmniCodex\target\debug\build\SLINT_DEFAULT_STYLE.txt
cargo:rerun-if-changed=C:\_Repos\OmniCodex\ui/omniforge.slint
cargo:rerun-if-env-changed=SLINT_STYLE
cargo:rerun-if-env-changed=SLINT_FONT_SIZES
cargo:rerun-if-env-changed=SLINT_SCALE_FACTOR
cargo:rerun-if-env-changed=SLINT_ASSET_SECTION
cargo:rerun-if-env-changed=SLINT_EMBED_RESOURCES
cargo:rerun-if-env-changed=SLINT_EMIT_DEBUG_INFO
cargo:rustc-env=SLINT_INCLUDE_GENERATED=C:\_Repos\OmniCodex\target\debug\build\omni_forge-d61bca2850656879\out\omniforge.rs
cargo:warning=Slint UI compilation completed successfully
