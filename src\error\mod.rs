// src/error/mod.rs
//! Error handling for the OmniForge compiler framework.
//!
//! This module defines the error types and results used throughout the OmniForge
//! compiler framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::PathBuf;
use thiserror::Error;

/// Result type for OmniForge operations
pub type OmniResult<T> = Result<T, OmniError>;

/// Error type for OmniForge operations
#[derive(Error, Debug)]
pub enum OmniError {
    /// IO errors
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    /// Binary format errors
    #[error("Binary format error: {0}")]
    BinaryFormat(String),
    
    /// Metadata extraction errors
    #[error("Metadata extraction error: {0}")]
    MetadataExtraction(String),
    
    /// Code generation errors
    #[error("Code generation error: {0}")]
    CodeGeneration(String),
    
    /// JSON serialization/deserialization errors
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
    
    /// Unsupported file type
    #[error("Unsupported file type: {0}")]
    UnsupportedFileType(PathBuf),
    
    /// Unsupported target language
    #[error("Unsupported target language: {0}")]
    UnsupportedLanguage(String),
    
    /// Missing required information
    #[error("Missing required information: {0}")]
    MissingRequiredInfo(String),
    
    /// Configuration error
    #[error("Configuration error: {0}")]
    Configuration(String),
    
    /// General errors
    #[error("{0}")]
    General(String),

    /// Anyhow errors
    #[error("Anyhow error: {0}")]
    Anyhow(#[from] anyhow::Error),

    /// Slint platform errors
    #[error("Slint platform error: {0}")]
    SlintPlatform(String),

    /// Other errors
    #[error("{0}")]
    Other(String),
}

impl From<slint::PlatformError> for OmniError {
    fn from(err: slint::PlatformError) -> Self {
        OmniError::SlintPlatform(format!("{}", err))
    }
}
