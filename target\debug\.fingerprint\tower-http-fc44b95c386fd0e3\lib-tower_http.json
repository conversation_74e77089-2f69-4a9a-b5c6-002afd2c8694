{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 13272165778285307237, "deps": [[784494742817713399, "tower_service", false, 11625666450275203965], [1906322745568073236, "pin_project_lite", false, 14600727308814208973], [4121350475192885151, "iri_string", false, 7137238589998941208], [5695049318159433696, "tower", false, 3877462737396213606], [7712452662827335977, "tower_layer", false, 16406263968290871012], [7896293946984509699, "bitflags", false, 14117725511662906093], [9010263965687315507, "http", false, 7084140426755917902], [10629569228670356391, "futures_util", false, 16827525334488113068], [14084095096285906100, "http_body", false, 13118722167181395879], [16066129441945555748, "bytes", false, 5059946848050862186]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-fc44b95c386fd0e3\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}