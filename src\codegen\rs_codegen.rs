// src/codegen/rs_codegen.rs
//! Rust code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating Rust code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// Rust code generator
pub struct RustCodeGenerator {
    // Configuration options can be added here
}

impl Default for RustCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[allow(dead_code)]
impl RustCodeGenerator {
    /// Create a new Rust code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate Rust type from argument type
    fn generate_rust_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "()".to_string(),
            super::ArgType::I8 => "i8".to_string(),
            super::ArgType::I16 => "i16".to_string(),
            super::ArgType::I32 => "i32".to_string(),
            super::ArgType::I64 => "i64".to_string(),
            super::ArgType::U8 => "u8".to_string(),
            super::ArgType::U16 => "u16".to_string(),
            super::ArgType::U32 => "u32".to_string(),
            super::ArgType::U64 => "u64".to_string(),
            super::ArgType::F32 => "f32".to_string(),
            super::ArgType::F64 => "f64".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "*mut i8".to_string(),
            super::ArgType::I16Ptr => "*mut i16".to_string(),
            super::ArgType::I32Ptr => "*mut i32".to_string(),
            super::ArgType::I64Ptr => "*mut i64".to_string(),
            super::ArgType::U8Ptr => "*mut u8".to_string(),
            super::ArgType::U16Ptr => "*mut u16".to_string(),
            super::ArgType::U32Ptr => "*mut u32".to_string(),
            super::ArgType::U64Ptr => "*mut u64".to_string(),
            super::ArgType::F32Ptr => "*mut f32".to_string(),
            super::ArgType::F64Ptr => "*mut f64".to_string(),
            super::ArgType::BoolPtr => "*mut bool".to_string(),
            super::ArgType::VoidPtr => "*mut std::ffi::c_void".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate Rust function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let rust_type = self.generate_rust_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if rust_type == "()" {
                "".to_string()
            } else {
                format!(" -> {rust_type}")
            }
        } else {
            "".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            signature
                .parameter_types
                .iter()
                .enumerate()
                .map(|(i, param)| {
                    let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                    let rust_type = self.generate_rust_type(&arg_type);
                    format!("arg{i}: {rust_type}")
                })
                .collect::<Vec<_>>()
                .join(", ")
        } else {
            String::new()
        };
        
        Ok(format!("fn {}({}){}", function.name, params, return_type))
    }
    
    /// Generate extern "C" block for FFI functions
    fn generate_extern_block(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        result.push_str("extern \"C\" {\n");
        
        for function in functions {
            // Skip non-host functions
            if function.function_type != crate::metadata_extractor::FunctionType::Host {
                continue;
            }
            
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add extern declaration
            result.push_str(&format!("    {signature};\n"));
        }
        
        result.push_str("}\n");
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            signature
                .parameter_types
                .iter()
                .enumerate()
                .map(|(i, param)| {
                    let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                    let rust_type = self.generate_rust_type(&arg_type);
                    format!("arg{i}: {rust_type}")
                })
                .collect::<Vec<_>>()
                .join(", ")
        } else {
            String::new()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("[{}, {}, {}]", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("[{}, {}, {}]", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "[1, 1, 1]".to_string(),
                "[256, 1, 1]".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/// Launch the CUDA kernel `{function_name}`
///
/// # Safety
///
/// This function is unsafe because it launches a CUDA kernel, which involves
/// raw pointers and FFI calls.
pub unsafe fn launch_{function_name}({params}) {{
    extern "C" {{
        fn {function_name}({params});
    }}
    
    // Launch parameters
    const GRID_DIM: [u32; 3] = {grid_dim};
    const BLOCK_DIM: [u32; 3] = {block_dim};
    const SHARED_MEM: usize = {shared_mem};
    
    // Advanced CUDA kernel launch implementation with comprehensive error handling
    #[cfg(feature = "cuda")]
    {
        use cudarc::driver::{CudaDevice, LaunchAsync, LaunchConfig, MemoryPool};
        use cudarc::driver::sys::{CUdeviceptr, CUfunction};
        use std::time::Instant;

        // Initialize CUDA device with error handling
        let device = match CudaDevice::new(0) {
            Ok(dev) => dev,
            Err(e) => {
                panic!("Failed to initialize CUDA device 0: {:?}. Check if CUDA is properly installed and device is available.", e);
            }
        };

        // Get device properties for validation
        let device_props = device.device_properties()
            .expect("Failed to get device properties");

        // Comprehensive launch parameter validation
        if GRID_DIM[0] > device_props.max_grid_dim_x as u32 ||
           GRID_DIM[1] > device_props.max_grid_dim_y as u32 ||
           GRID_DIM[2] > device_props.max_grid_dim_z as u32 {
            panic!("Grid dimensions ({}, {}, {}) exceed device limits ({}, {}, {})",
                   GRID_DIM[0], GRID_DIM[1], GRID_DIM[2],
                   device_props.max_grid_dim_x, device_props.max_grid_dim_y, device_props.max_grid_dim_z);
        }

        if BLOCK_DIM[0] > device_props.max_block_dim_x as u32 ||
           BLOCK_DIM[1] > device_props.max_block_dim_y as u32 ||
           BLOCK_DIM[2] > device_props.max_block_dim_z as u32 {
            panic!("Block dimensions ({}, {}, {}) exceed device limits ({}, {}, {})",
                   BLOCK_DIM[0], BLOCK_DIM[1], BLOCK_DIM[2],
                   device_props.max_block_dim_x, device_props.max_block_dim_y, device_props.max_block_dim_z);
        }

        let total_threads = BLOCK_DIM[0] * BLOCK_DIM[1] * BLOCK_DIM[2];
        if total_threads > device_props.max_threads_per_block as u32 {
            panic!("Total threads per block ({}) exceeds device limit ({})",
                   total_threads, device_props.max_threads_per_block);
        }

        if SHARED_MEM > device_props.shared_memory_per_block {
            panic!("Shared memory usage ({} bytes) exceeds device limit ({} bytes)",
                   SHARED_MEM, device_props.shared_memory_per_block);
        }

        // Create optimized launch configuration
        let launch_config = LaunchConfig {
            grid_dim: (GRID_DIM[0], GRID_DIM[1], GRID_DIM[2]),
            block_dim: (BLOCK_DIM[0], BLOCK_DIM[1], BLOCK_DIM[2]),
            shared_mem_bytes: SHARED_MEM as u32,
        };

        // Prepare kernel arguments with proper memory management
        let kernel_args = vec![args];

        // Record start time for performance monitoring
        let start_time = Instant::now();

        // Create CUDA events for precise timing
        let start_event = device.create_event()
            .expect("Failed to create start event");
        let stop_event = device.create_event()
            .expect("Failed to create stop event");

        // Record start event
        device.record_event(&start_event)
            .expect("Failed to record start event");

        // Launch kernel with comprehensive error handling
        let launch_result = unsafe {
            device.launch_kernel(
                function_name as CUfunction,
                launch_config,
                &kernel_args,
            )
        };

        // Record stop event
        device.record_event(&stop_event)
            .expect("Failed to record stop event");

        match launch_result {
            Ok(_) => {
                // Synchronize to ensure kernel completion
                if let Err(sync_err) = device.synchronize() {
                    panic!("CUDA kernel execution failed for {}: {:?}", 
                           stringify!(function_name), sync_err);
                }

                // Calculate precise execution time
                device.wait_for_event(&stop_event)
                    .expect("Failed to wait for stop event");
                
                let execution_time = device.elapsed_time_between_events(&start_event, &stop_event)
                    .unwrap_or(0.0);

                #[cfg(feature = "omni-debug")]
                {
                    let total_time = start_time.elapsed();
                    println!("🚀 Kernel {} executed successfully:", stringify!(function_name));
                    println!("   GPU execution time: {:.3} ms", execution_time);
                    println!("   Total time (including overhead): {:.3} ms", total_time.as_secs_f32() * 1000.0);
                    println!("   Grid: ({}, {}, {}), Block: ({}, {}, {})", 
                            GRID_DIM[0], GRID_DIM[1], GRID_DIM[2],
                            BLOCK_DIM[0], BLOCK_DIM[1], BLOCK_DIM[2]);
                    println!("   Shared memory: {} bytes", SHARED_MEM);
                }

                // Cleanup events
                device.destroy_event(start_event).ok();
                device.destroy_event(stop_event).ok();
            },
            Err(launch_err) => {
                // Cleanup events before panicking
                device.destroy_event(start_event).ok();
                device.destroy_event(stop_event).ok();
                panic!("CUDA kernel launch failed for {}: {:?}", 
                       stringify!(function_name), launch_err);
            }
        }
    }
    #[cfg(not(feature = "cuda"))]
    {
        // Advanced fallback implementation with detailed logging
        log::warn!("CUDA feature not enabled, using fallback implementation for kernel: {}", 
                   stringify!(function_name));

        // Validate that we have a runtime fallback available
        if !cuda_runtime::is_available() {
            panic!("CUDA runtime not available and CUDA feature not enabled. Cannot execute kernel: {}", 
                   stringify!(function_name));
        }

        // Enhanced fallback launch with validation
        let launch_result = cuda_runtime::launch_kernel_advanced(
            function_name as *const std::ffi::c_void,
            GRID_DIM,
            BLOCK_DIM,
            SHARED_MEM,
            &[args],
            &cuda_runtime::LaunchOptions {
                validate_params: true,
                enable_timing: cfg!(feature = "omni-debug"),
                timeout_ms: Some(30000), // 30 second timeout
            }
        );

        match launch_result {
            Ok(timing_info) => {
                #[cfg(feature = "omni-debug")]
                {
                    if let Some(time_ms) = timing_info.execution_time_ms {
                        println!("📱 Kernel {} executed via runtime fallback in {:.3} ms", 
                                stringify!(function_name), time_ms);
                    }
                }
            },
            Err(e) => {
                panic!("CUDA runtime kernel launch failed for {}: {}", 
                       stringify!(function_name), e);
            }
        }
    }}
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = (0..params.split(", ").count())
                .map(|i| format!("&arg{i} as *const _ as *const std::ffi::c_void"))
                .collect::<Vec<_>>()
                .join(", "),
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/// Entry in the OmniCodex dispatch table
#[derive(Debug, Clone)]
pub struct OmniCodexEntry {
    /// Function name
    pub name: &'static str,
    
    /// Target type (CPU, GPU, etc.)
    pub target: OmniTarget,
    
    /// Metadata for the function
    pub metadata: ComputeMetadata,
}

/// Target type
#[derive(Debug, Clone)]
pub enum OmniTarget {
    /// CPU function
    CPU(unsafe fn()),
    
    /// GPU function (CUDA kernel)
    GPU(unsafe fn()),
    
    /// CPU with SIMD
    CPUSIMD(unsafe fn()),
}

/// Compute metadata
#[derive(Debug, Clone)]
pub struct ComputeMetadata {
    /// Grid size (for CUDA kernels)
    pub grid_size: (u32, u32, u32),
    
    /// Block size (for CUDA kernels)
    pub block_size: (u32, u32, u32),
    
    /// Shared memory size (for CUDA kernels)
    pub shared_mem: usize,
    
    /// Argument types
    pub args_layout: &'static [ArgType],
}

/// Argument type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ArgType {
    /// Void
    Void,
    /// 8-bit integer
    I8,
    /// 16-bit integer
    I16,
    /// 32-bit integer
    I32,
    /// 64-bit integer
    I64,
    /// 8-bit unsigned integer
    U8,
    /// 16-bit unsigned integer
    U16,
    /// 32-bit unsigned integer
    U32,
    /// 64-bit unsigned integer
    U64,
    /// 32-bit float
    F32,
    /// 64-bit float
    F64,
    /// Boolean
    Bool,
    /// Pointer to 8-bit integer
    I8Ptr,
    /// Pointer to 16-bit integer
    I16Ptr,
    /// Pointer to 32-bit integer
    I32Ptr,
    /// Pointer to 64-bit integer
    I64Ptr,
    /// Pointer to 8-bit unsigned integer
    U8Ptr,
    /// Pointer to 16-bit unsigned integer
    U16Ptr,
    /// Pointer to 32-bit unsigned integer
    U32Ptr,
    /// Pointer to 64-bit unsigned integer
    U64Ptr,
    /// Pointer to 32-bit float
    F32Ptr,
    /// Pointer to 64-bit float
    F64Ptr,
    /// Pointer to boolean
    BoolPtr,
    /// Pointer to void
    VoidPtr,
}

impl ComputeMetadata {
    /// Create CPU metadata
    pub const fn cpu() -> Self {
        Self {
            grid_size: (1, 1, 1),
            block_size: (1, 1, 1),
            shared_mem: 0,
            args_layout: &[],
        }
    }
    
    /// Create CPU SIMD metadata
    pub const fn cpu_simd() -> Self {
        Self {
            grid_size: (1, 1, 1),
            block_size: (1, 1, 1),
            shared_mem: 0,
            args_layout: &[],
        }
    }
    
    /// Create GPU metadata
    pub const fn gpu(grid_size: (u32, u32, u32), block_size: (u32, u32, u32), shared_mem: usize, args_layout: &'static [ArgType]) -> Self {
        Self {
            grid_size,
            block_size,
            shared_mem,
            args_layout,
        }
    }
}
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> String {
        if entry.metadata.arg_layout.is_empty() {
            return "&[]".to_string();
        }
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| format!("ArgType::{arg}"))
            .collect::<Vec<_>>()
            .join(", ");
        
        format!("&[{arg_types}]")
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        result.push_str("/// OmniCodex dispatch table\npub static OMNI_CODEX: &[OmniCodexEntry] = &[\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("({}, {}, {})", grid[0], grid[1], grid[2]),
                    format!("({}, {}, {})", block[0], block[1], block[2]),
                )
            } else {
                ("(1, 1, 1)".to_string(), "(256, 1, 1)".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = self.generate_arg_types(entry);
            
            // Generate entry
            result.push_str(&format!(
                r#"    OmniCodexEntry {{
        name: "{}",
        target: OmniTarget::{}({}),
        metadata: ComputeMetadata {{
            grid_size: {},
            block_size: {},
            shared_mem: {},
            args_layout: {},
        }},
    }},
"#,
                entry.name,
                entry.target_type,
                entry.function_pointer,
                grid_size,
                block_size,
                shared_mem,
                args_layout,
            ));
        }
        
        result.push_str("];\n");
        
        result
    }
    
    /// Generate module documentation
    fn generate_module_doc(&self) -> String {
        r#"//! OmniCodex dispatch table generated by OmniForge.
//!
//! This module contains the OmniCodex dispatch table, which provides a zero-cost
//! abstraction for heterogeneous computing. It allows calling functions on
//! different backends (CPU, GPU, etc.) with a unified interface.
//!
//! # Example
//!
//! ```
//! use omnicodex::execute;
//!
//! fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Execute a function by name
//!     let result = unsafe { execute::<f32>("dot_product", &[&a, &b, &c])? };
//!     println!("Result: {}", result);
//!     Ok(())
//! }
//! ```
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/// Execute a function by name
///
/// # Safety
///
/// This function is unsafe because it involves FFI calls and raw pointers.
///
/// # Arguments
///
/// * `name` - Function name
/// * `args` - Function arguments
///
/// # Returns
///
/// * `Result<T, OmniCodexError>` - Function result
pub unsafe fn execute<T>(name: &str, args: &[&dyn std::any::Any]) -> Result<T, OmniCodexError> {
    // Find the function in the dispatch table
    let entry = OMNI_CODEX
        .iter()
        .find(|e| e.name == name)
        .ok_or(OmniCodexError::FunctionNotFound(name.to_string()))?;
    
    // Check argument count
    if args.len() != entry.metadata.args_layout.len() {
        return Err(OmniCodexError::ArgumentCountMismatch {
            expected: entry.metadata.args_layout.len(),
            actual: args.len(),
        });
    }
    
    // Advanced function execution implementation with comprehensive error handling and optimization
    use std::time::Instant;
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    // Global execution counter for debugging and monitoring
    static EXECUTION_COUNTER: AtomicUsize = AtomicUsize::new(0);
    
    let execution_id = EXECUTION_COUNTER.fetch_add(1, Ordering::Relaxed);
    let start_time = Instant::now();
    
    // Pre-execution validation
    if args.len() != entry.metadata.args_layout.len() {
        return Err(OmniCodexError::ArgumentCountMismatch {
            expected: entry.metadata.args_layout.len(),
            actual: args.len(),
        });
    }

    let result: T = match &entry.target {
        OmniTarget::CPU(func) => {
            // Advanced CPU function execution with optimization hints
            #[cfg(target_arch = "x86_64")]
            {
                // Prefetch function code for better performance
                unsafe {
                    std::arch::x86_64::_mm_prefetch(
                        *func as *const i8,
                        std::arch::x86_64::_MM_HINT_T0
                    );
                }
            }

            // Set thread priority for CPU-intensive tasks
            #[cfg(target_os = "linux")]
            {
                unsafe {
                    let mut param = libc::sched_param { sched_priority: 10 };
                    if libc::sched_setscheduler(0, libc::SCHED_FIFO, &param) != 0 {
                        log::warn!("Failed to set high priority scheduling for CPU execution");
                    }
                }
            }

            // Optimized argument handling based on count
            let execution_result = match args.len() {
                0 => {
                    // No arguments - direct function call
                    let cpu_func: unsafe extern "C" fn() -> T = 
                        unsafe { std::mem::transmute(*func) };
                    unsafe { cpu_func() }
                },
                1..=4 => {
                    // Small number of arguments - use typed function pointers for better performance
                    match args.len() {
                        1 => {
                            let cpu_func: unsafe extern "C" fn(*const std::ffi::c_void) -> T = 
                                unsafe { std::mem::transmute(*func) };
                            let arg_ptr = args[0] as *const dyn std::any::Any as *const std::ffi::c_void;
                            unsafe { cpu_func(arg_ptr) }
                        },
                        2 => {
                            let cpu_func: unsafe extern "C" fn(*const std::ffi::c_void, *const std::ffi::c_void) -> T = 
                                unsafe { std::mem::transmute(*func) };
                            let arg0 = args[0] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg1 = args[1] as *const dyn std::any::Any as *const std::ffi::c_void;
                            unsafe { cpu_func(arg0, arg1) }
                        },
                        3 => {
                            let cpu_func: unsafe extern "C" fn(*const std::ffi::c_void, *const std::ffi::c_void, *const std::ffi::c_void) -> T = 
                                unsafe { std::mem::transmute(*func) };
                            let arg0 = args[0] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg1 = args[1] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg2 = args[2] as *const dyn std::any::Any as *const std::ffi::c_void;
                            unsafe { cpu_func(arg0, arg1, arg2) }
                        },
                        4 => {
                            let cpu_func: unsafe extern "C" fn(*const std::ffi::c_void, *const std::ffi::c_void, *const std::ffi::c_void, *const std::ffi::c_void) -> T = 
                                unsafe { std::mem::transmute(*func) };
                            let arg0 = args[0] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg1 = args[1] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg2 = args[2] as *const dyn std::any::Any as *const std::ffi::c_void;
                            let arg3 = args[3] as *const dyn std::any::Any as *const std::ffi::c_void;
                            unsafe { cpu_func(arg0, arg1, arg2, arg3) }
                        },
                        _ => unreachable!()
                    }
                },
                _ => {
                    // Large number of arguments - use array-based approach
                    let arg_ptrs: Vec<*const std::ffi::c_void> = args.iter()
                        .map(|arg| *arg as *const dyn std::any::Any as *const std::ffi::c_void)
                        .collect();

                    let cpu_func: unsafe extern "C" fn(*const *const std::ffi::c_void, usize) -> T = 
                        unsafe { std::mem::transmute(*func) };
                    unsafe { cpu_func(arg_ptrs.as_ptr(), args.len()) }
                }
            };

            // Reset thread priority
            #[cfg(target_os = "linux")]
            {
                unsafe {
                    let mut param = libc::sched_param { sched_priority: 0 };
                    libc::sched_setscheduler(0, libc::SCHED_OTHER, &param);
                }
            }

            execution_result
        },

        OmniTarget::GPU(func) => {
            // Advanced GPU function execution with comprehensive memory management
            #[cfg(feature = "cuda")]
            {
                use cudarc::driver::{CudaDevice, DevicePtr, DeviceSlice};

                // Get or create CUDA device
                let device = CudaDevice::new(0)
                    .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to initialize CUDA device: {:?}", e)))?;

                // Allocate device memory for arguments
                let mut device_ptrs: Vec<DevicePtr<u8>> = Vec::new();
                let mut host_buffers: Vec<Vec<u8>> = Vec::new();

                for (i, arg) in args.iter().enumerate() {
                    let arg_type = &entry.metadata.args_layout[i];
                    let type_size = get_arg_type_size(arg_type);

                    // Serialize argument to bytes
                    let mut buffer = vec![0u8; type_size];
                    serialize_argument_to_bytes(*arg, &mut buffer, arg_type)
                        .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to serialize argument {}: {}", i, e)))?;

                    // Allocate device memory
                    let device_ptr: DevicePtr<u8> = device.alloc_zeros(type_size)
                        .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to allocate device memory for argument {}: {:?}", i, e)))?;

                    // Copy to device if it's an input argument
                    if is_input_argument(arg_type) {
                        device.htod_sync_copy_into(&buffer, &device_ptr)
                            .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to copy argument {} to device: {:?}", i, e)))?;
                    }

                    device_ptrs.push(device_ptr);
                    host_buffers.push(buffer);
                }

                // Launch kernel using raw CUDA function call
                let kernel_func = *func as cudarc::driver::sys::CUfunction;
                let launch_config = cudarc::driver::LaunchConfig {
                    grid_dim: entry.metadata.grid_size,
                    block_dim: entry.metadata.block_size,
                    shared_mem_bytes: entry.metadata.shared_mem as u32,
                };

                // Prepare kernel arguments as device pointers
                let kernel_args: Vec<*mut std::ffi::c_void> = device_ptrs.iter()
                    .map(|ptr| ptr.device_ptr() as *mut std::ffi::c_void)
                    .collect();

                // Execute kernel
                unsafe {
                    device.launch_kernel(kernel_func, launch_config, &kernel_args)
                        .map_err(|e| OmniCodexError::ExecutionError(format!("GPU kernel launch failed: {:?}", e)))?;
                }

                // Synchronize
                device.synchronize()
                    .map_err(|e| OmniCodexError::ExecutionError(format!("GPU synchronization failed: {:?}", e)))?;

                // Copy results back for output arguments
                for (i, (device_ptr, host_buffer)) in device_ptrs.iter().zip(host_buffers.iter_mut()).enumerate() {
                    let arg_type = &entry.metadata.args_layout[i];
                    if is_output_argument(arg_type) {
                        device.dtoh_sync_copy_into(device_ptr, host_buffer)
                            .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to copy result {} from device: {:?}", i, e)))?;
                        
                        // Deserialize result back to original argument
                        deserialize_argument_from_bytes(host_buffer, args[i], arg_type)
                            .map_err(|e| OmniCodexError::ExecutionError(format!("Failed to deserialize result {}: {}", i, e)))?;
                    }
                }

                // Return default value for GPU kernels (typically void)
                unsafe { std::mem::zeroed() }
            }
            #[cfg(not(feature = "cuda"))]
            {
                return Err(OmniCodexError::ExecutionError(
                    "GPU execution requested but CUDA feature not enabled".to_string()
                ));
            }
        },

        OmniTarget::CPUSIMD(func) => {
            // Advanced CPU SIMD execution with instruction set detection
            #[cfg(target_arch = "x86_64")]
            {
                // Check available SIMD instruction sets
                if !std::arch::is_x86_feature_detected!("avx2") {
                    return Err(OmniCodexError::ExecutionError(
                        "AVX2 instruction set required but not available".to_string()
                    ));
                }

                // Set optimal floating-point control state for SIMD
                unsafe {
                    let old_mxcsr = std::arch::x86_64::_mm_getcsr();
                    let new_mxcsr = old_mxcsr | 0x8040; // Set FTZ and DAZ flags
                    std::arch::x86_64::_mm_setcsr(new_mxcsr);

                    // Execute SIMD function
                    let arg_ptrs: Vec<*const std::ffi::c_void> = args.iter()
                        .map(|arg| *arg as *const dyn std::any::Any as *const std::ffi::c_void)
                        .collect();

                    let simd_func: extern "C" fn(*const *const std::ffi::c_void, usize) -> T = 
                        std::mem::transmute(*func);
                    let result = simd_func(arg_ptrs.as_ptr(), args.len());

                    // Restore original MXCSR
                    std::arch::x86_64::_mm_setcsr(old_mxcsr);
                    result
                }
            }
            #[cfg(not(target_arch = "x86_64"))]
            {
                // Fallback to regular CPU execution on non-x86_64 platforms
                let arg_ptrs: Vec<*const std::ffi::c_void> = args.iter()
                    .map(|arg| *arg as *const dyn std::any::Any as *const std::ffi::c_void)
                    .collect();

                let cpu_func: unsafe extern "C" fn(*const *const std::ffi::c_void, usize) -> T = 
                    unsafe { std::mem::transmute(*func) };
                unsafe { cpu_func(arg_ptrs.as_ptr(), args.len()) }
            }
        }
    };

    // Calculate execution time and log performance metrics
    let execution_time = start_time.elapsed();
    
    #[cfg(feature = "omni-debug")]
    {
        log::info!("🎯 Execution #{} completed:", execution_id);
        log::info!("   Function: {}", name);
        log::info!("   Target: {:?}", entry.target);
        log::info!("   Duration: {:.3} ms", execution_time.as_secs_f64() * 1000.0);
        log::info!("   Arguments: {} args", args.len());
        
        if execution_time.as_millis() > 100 {
            log::warn!("   ⚠️ Slow execution detected (>100ms)");
        }
    }

    Ok(result)
}

/// OmniCodex error
#[derive(Debug, thiserror::Error)]
pub enum OmniCodexError {
    /// Function not found
    #[error("Function not found: {0}")]
    FunctionNotFound(String),
    
    /// Argument count mismatch
    #[error("Argument count mismatch: expected {expected}, got {actual}")]
    ArgumentCountMismatch {
        /// Expected argument count
        expected: usize,
        
        /// Actual argument count
        actual: usize,
    },
    
    /// Argument type mismatch
    #[error("Argument type mismatch at index {index}: expected {expected}, got {actual}")]
    ArgumentTypeMismatch {
        /// Argument index
        index: usize,
        
        /// Expected type
        expected: String,
        
        /// Actual type
        actual: String,
    },
    
    /// Not implemented
    #[error("Not implemented")]
    NotImplemented,
}

/// Simple CUDA runtime module for launching kernels
///
/// This is a placeholder for the actual CUDA runtime implementation.
#[doc(hidden)]
pub mod cuda_runtime {
    /// Launch a CUDA kernel
    ///
    /// # Safety
    ///
    /// This function is unsafe because it launches a CUDA kernel, which involves
    /// raw pointers and FFI calls.
    pub unsafe fn launch_kernel(
        kernel: *const std::ffi::c_void,
        grid_dim: [u32; 3],
        block_dim: [u32; 3],
        shared_mem: usize,
        args: &[*const std::ffi::c_void],
    ) {
        // This is a placeholder for the actual CUDA kernel launch
        // In a real implementation, this would call the CUDA runtime API
    }
}
"#.to_string()
    }
    
    /// Generate imports section
    fn generate_imports(&self) -> String {
        "// No imports needed for generated code\n".to_string()
    }
}

impl CodeGenerator for RustCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating Rust OmniCodex");
        
        // Generate header
        let mut code = String::new();
        code.push_str(&self.generate_module_doc());
        code.push('\n');
        code.push_str(&self.generate_imports());
        code.push('\n');
        
        // Generate codex entry struct
        code.push_str(&self.generate_codex_entry_struct());
        code.push('\n');
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating Rust wrappers");
        
        let mut code = String::new();
        
        // Generate header
        code.push_str("//! OmniCodex wrapper functions generated by OmniForge.\n");
        code.push_str("//!\n");
        code.push_str("//! This module contains wrapper functions for the OmniCodex dispatch table.\n");
        code.push_str("//! These wrappers provide a type-safe interface to the functions in the\n");
        code.push_str("//! OmniCodex dispatch table.\n");
        code.push('\n');
        
        // Generate imports
        code.push_str("use super::{execute, OmniCodexError};\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let rust_return_type = self.generate_rust_type(&return_type);
                    
                    // Extract parameter types
                    let params = signature
                        .parameter_types
                        .iter()
                        .enumerate()
                        .map(|(i, param)| {
                            let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                            let rust_type = self.generate_rust_type(&arg_type);
                            format!("arg{i}: {rust_type}")
                        })
                        .collect::<Vec<_>>()
                        .join(", ");
                    
                    // Generate wrapper function
                    let func_name = function.name.to_lowercase();
                    
                    code.push_str(&format!(
                        r#"/// Wrapper for the `{}` function
///
/// # Safety
///
/// This function is unsafe because it involves FFI calls and raw pointers.
pub unsafe fn {}({}) -> Result<{}, OmniCodexError> {{
    execute::<{}>("{}", &[{}])
}}

"#,
                        function.name,
                        func_name,
                        params,
                        rust_return_type,
                        rust_return_type,
                        function.name,
                        (0..signature.parameter_types.len())
                            .map(|i| format!("&arg{i}"))
                            .collect::<Vec<_>>()
                            .join(", "),
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        Ok(code)
    }
}
