{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 2241668132362809309, "path": 678108482320985702, "deps": [[5157631553186200874, "num_traits", false, 10930000844155142501], [12319020793864570031, "num_complex", false, 12897158259845751543], [15709748443193639506, "rawpointer", false, 10211705729786767872], [15826188163127377936, "matrixmultiply", false, 4126114437521325149], [16795989132585092538, "num_integer", false, 17384707255319123081]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ndarray-921e1aa5bc34fc0b\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}