#!/bin/bash
echo "Building AGGRESSIVE Intel 13900H Benchmarks..."

# Compile with MAXIMUM aggression
echo "Building ultra-aggressive benchmarks..."
g++ -o haal-sse-aggressive.exe haal-sse-aggressive.cpp -msse4.2 -O3 -march=native -ffast-math -funroll-loops -pthread
g++ -o haal-avx2-aggressive.exe haal-avx2-aggressive.cpp -mavx2 -mfma -O3 -march=native -ffast-math -funroll-loops -pthread
g++ -o haal-avx2-2.exe haal-avx2-2.cpp -mavx2 -mfma -O3 -march=native -fopenmp -ffast-math -funroll-loops
g++ -o haal-openmp-monster.exe haal-openmp-monster.cpp -mavx2 -mfma -O3 -march=native -fopenmp -ffast-math -funroll-loops

echo "AGGRESSIVE compilation complete!"
echo ""
echo "Run with:"
echo "./haal-sse-aggressive.exe"
echo "./haal-avx2-aggressive.exe" 
echo "./haal-avx2-2.exe"
echo "./haal-openmp-monster.exe"
