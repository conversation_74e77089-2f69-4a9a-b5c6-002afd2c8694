{"$message_type":"diagnostic","message":"unresolved import `tch`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":1839,"byte_end":1842,"line_start":41,"line_end":41,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"use tch::{CModule, Tensor, Device as TchDevice, Kind};","highlight_start":5,"highlight_end":8}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `tch`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:41:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tch::{CModule, Tensor, Device as TchDevice, Kind};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":7682,"byte_end":7685,"line_start":178,"line_end":178,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":23}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":7682,"byte_end":7691,"line_start":178,"line_end":178,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"crate::prelude::Device","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:178:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if tch::Cuda::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;9mtch::Cuda\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;10mcrate::prelude::Device\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":7945,"byte_end":7948,"line_start":186,"line_end":186,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":23}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":7945,"byte_end":7954,"line_start":186,"line_end":186,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"crate::prelude::Device","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:186:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if tch::Cuda::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;9mtch::Cuda\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;10mcrate::prelude::Device\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":8459,"byte_end":8462,"line_start":201,"line_end":201,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":23}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":8459,"byte_end":8468,"line_start":201,"line_end":201,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"crate::prelude::Device","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:201:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if tch::Cuda::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;9mtch::Cuda\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;10mcrate::prelude::Device\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":13752,"byte_end":13755,"line_start":342,"line_end":342,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":23}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":13752,"byte_end":13761,"line_start":342,"line_end":342,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                if tch::Cuda::is_available() {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"crate::prelude::Device","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:342:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if tch::Cuda::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;9mtch::Cuda\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                if \u001b[0m\u001b[0m\u001b[38;5;10mcrate::prelude::Device\u001b[0m\u001b[0m::is_available() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":17530,"byte_end":17533,"line_start":453,"line_end":453,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        tch::Cuda::device_count()","highlight_start":9,"highlight_end":12}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":17530,"byte_end":17539,"line_start":453,"line_end":453,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        tch::Cuda::device_count()","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"crate::prelude::Device","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:453:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m453\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        tch::Cuda::device_count()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is an enum variant `crate::prelude::Device::Cuda`; try using the variant's enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m453\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;9mtch::Cuda\u001b[0m\u001b[0m::device_count()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m453\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;10mcrate::prelude::Device\u001b[0m\u001b[0m::device_count()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Device`, `UmlaiieError`, and `UmlaiieResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":197,"byte_end":209,"line_start":8,"line_end":8,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\onnx.rs","byte_start":211,"byte_end":224,"line_start":8,"line_end":8,"column_start":72,"column_end":85,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":72,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\onnx.rs","byte_start":226,"byte_end":232,"line_start":8,"line_end":8,"column_start":87,"column_end":93,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":87,"highlight_end":93}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":195,"byte_end":232,"line_start":8,"line_end":8,"column_start":56,"column_end":93,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":93}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Device`, `UmlaiieError`, and `UmlaiieResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\onnx.rs:8:58\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AccelerationHint`, `TaskCharacteristics`, `VectorOperation`, and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":253,"byte_end":257,"line_start":9,"line_end":9,"column_start":19,"column_end":23,"is_primary":true,"text":[{"text":"use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};","highlight_start":19,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\onnx.rs","byte_start":259,"byte_end":275,"line_start":9,"line_end":9,"column_start":25,"column_end":41,"is_primary":true,"text":[{"text":"use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};","highlight_start":25,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\onnx.rs","byte_start":277,"byte_end":296,"line_start":9,"line_end":9,"column_start":43,"column_end":62,"is_primary":true,"text":[{"text":"use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};","highlight_start":43,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\onnx.rs","byte_start":298,"byte_end":313,"line_start":9,"line_end":9,"column_start":64,"column_end":79,"is_primary":true,"text":[{"text":"use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};","highlight_start":64,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":235,"byte_end":316,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};","highlight_start":1,"highlight_end":81},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AccelerationHint`, `TaskCharacteristics`, `VectorOperation`, and `self`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\onnx.rs:9:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::ahaw::{self, AccelerationHint, TaskCharacteristics, VectorOperation};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":1950,"byte_end":1962,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":1948,"byte_end":1962,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\tensorflow.rs","byte_start":1872,"byte_end":1884,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\tensorflow.rs","byte_start":1870,"byte_end":1884,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorflow.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":1878,"byte_end":1890,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":1876,"byte_end":1890,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorflow_lite.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\keras.rs","byte_start":1797,"byte_end":1809,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\keras.rs","byte_start":1795,"byte_end":1809,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\keras.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\pickle.rs","byte_start":1833,"byte_end":1845,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\pickle.rs","byte_start":1831,"byte_end":1845,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pickle.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\coreml.rs","byte_start":1864,"byte_end":1876,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\coreml.rs","byte_start":1862,"byte_end":1876,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\coreml.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UmlaiieError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\openvino.rs","byte_start":1888,"byte_end":1900,"line_start":43,"line_end":43,"column_start":58,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":58,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\models\\openvino.rs","byte_start":1886,"byte_end":1900,"line_start":43,"line_end":43,"column_start":56,"column_end":70,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":56,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UmlaiieError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\openvino.rs:43:58\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `cuda`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\tensorrt.rs","byte_start":21198,"byte_end":21214,"line_start":575,"line_end":575,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"        cfg!(feature = \"cuda\")","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected values for `feature` are: `bindgen`, `bindings-generation`, `default`, `hardware-acceleration`, `libc`, `ml-inference`, `ndarray`, `once_cell`, and `safetensors`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `cuda` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unexpected `cfg` condition value: `cuda`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorrt.rs:575:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m575\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        cfg!(feature = \"cuda\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected values for `feature` are: `bindgen`, `bindings-generation`, `default`, `hardware-acceleration`, `libc`, `ml-inference`, `ndarray`, `once_cell`, and `safetensors`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: consider adding `cuda` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `row_count` found for struct `ModelRc` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":40015,"byte_end":40024,"line_start":1109,"line_end":1109,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"                    for i in 0..file_paths.row_count() {","highlight_start":44,"highlight_end":53}],"label":"method not found in `ModelRc<SharedString>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs","byte_start":4440,"byte_end":4449,"line_start":125,"line_end":125,"column_start":8,"column_end":17,"is_primary":false,"text":[{"text":"    fn row_count(&self) -> usize;","highlight_start":8,"highlight_end":17}],"label":"the method is available for `ModelRc<slint::SharedString>` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"trait `Model` which provides `row_count` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":2256,"byte_end":2256,"line_start":43,"line_end":43,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use slint::{ComponentHandle, ModelRc, SharedString, VecModel};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use slint::Model;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `row_count` found for struct `ModelRc` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:1109:44\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    for i in 0..file_paths.row_count() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ModelRc<SharedString>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs:125:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m125\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn row_count(&self) -> usize;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthe method is available for `ModelRc<slint::SharedString>` here\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Model` which provides `row_count` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use slint::Model;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `row_data` found for struct `ModelRc` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs","byte_start":4817,"byte_end":4825,"line_start":132,"line_end":132,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn row_data(&self, row: usize) -> Option<Self::Data>;","highlight_start":8,"highlight_end":16}],"label":"the method is available for `ModelRc<slint::SharedString>` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\gui\\mod.rs","byte_start":40089,"byte_end":40097,"line_start":1110,"line_end":1110,"column_start":61,"column_end":69,"is_primary":true,"text":[{"text":"                        if let Some(file_path) = file_paths.row_data(i) {","highlight_start":61,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"there is a method `set_row_data` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs","byte_start":5323,"byte_end":5377,"line_start":142,"line_end":142,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"    fn set_row_data(&self, _row: usize, _data: Self::Data) {","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"trait `Model` which provides `row_data` is implemented but not in scope; perhaps you want to import it","code":null,"level":"help","spans":[{"file_name":"src\\gui\\mod.rs","byte_start":2256,"byte_end":2256,"line_start":43,"line_end":43,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use slint::{ComponentHandle, ModelRc, SharedString, VecModel};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use slint::Model;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `row_data` found for struct `ModelRc` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\gui\\mod.rs:1110:61\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if let Some(file_path) = file_paths.row_data(i) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs:132:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn row_data(&self, row: usize) -> Option<Self::Data>;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthe method is available for `ModelRc<slint::SharedString>` here\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is in scope\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `set_row_data` with a similar name, but with different arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\i-slint-core-1.12.1\\model.rs:142:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn set_row_data(&self, _row: usize, _data: Self::Data) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: trait `Model` which provides `row_data` is implemented but not in scope; perhaps you want to import it\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use slint::Model;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":12080,"byte_end":12092,"line_start":288,"line_end":288,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = output.as_slice_mut() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&mut [f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\pytorch.rs","byte_start":12095,"byte_end":12116,"line_start":288,"line_end":288,"column_start":35,"column_end":56,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = output.as_slice_mut() {","highlight_start":35,"highlight_end":56}],"label":"this expression has type `std::option::Option<&mut [f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&mut [f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:288:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = output.as_slice_mut() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&mut [f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&mut [f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&mut [f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `tch`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\models\\pytorch.rs","byte_start":17675,"byte_end":17678,"line_start":458,"line_end":458,"column_start":9,"column_end":12,"is_primary":true,"text":[{"text":"        tch::set_num_threads(num_threads);","highlight_start":9,"highlight_end":12}],"label":"use of unresolved module or unlinked crate `tch`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pytorch.rs:458:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m458\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        tch::set_num_threads(num_threads);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `tch`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `tch`, use `cargo add tch` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `collect` found for struct `Vec<(std::string::String, TensorView<'_>)>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":5494,"byte_end":5501,"line_start":118,"line_end":118,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"        let tensor_list: Vec<_> = tensors.tensors().collect();","highlight_start":53,"highlight_end":60}],"label":"`Vec<(std::string::String, TensorView<'_>)>` is not an iterator","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"call `.into_iter()` first","code":null,"level":"help","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":5494,"byte_end":5494,"line_start":118,"line_end":118,"column_start":53,"column_end":53,"is_primary":true,"text":[{"text":"        let tensor_list: Vec<_> = tensors.tensors().collect();","highlight_start":53,"highlight_end":53}],"label":null,"suggested_replacement":"into_iter().","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `collect` found for struct `Vec<(std::string::String, TensorView<'_>)>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\safetensors.rs:118:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let tensor_list: Vec<_> = tensors.tensors().collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`Vec<(std::string::String, TensorView<'_>)>` is not an iterator\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: call `.into_iter()` first\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let tensor_list: Vec<_> = tensors.tensors().\u001b[0m\u001b[0m\u001b[38;5;10minto_iter().\u001b[0m\u001b[0mcollect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 3 arguments but 2 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":12310,"byte_end":12337,"line_start":279,"line_end":279,"column_start":46,"column_end":73,"is_primary":false,"text":[{"text":"        let metadata = Self::extract_metadata(&tensors, file_data.len());","highlight_start":46,"highlight_end":73}],"label":"argument #3 of type `&std::path::Path` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\safetensors.rs","byte_start":12288,"byte_end":12310,"line_start":279,"line_end":279,"column_start":24,"column_end":46,"is_primary":true,"text":[{"text":"        let metadata = Self::extract_metadata(&tensors, file_data.len());","highlight_start":24,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":2716,"byte_end":2727,"line_start":63,"line_end":63,"column_start":66,"column_end":77,"is_primary":false,"text":[{"text":"    fn extract_metadata(tensors: &SafeTensors, file_size: usize, path: &Path) -> ModelMetadata {","highlight_start":66,"highlight_end":77}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\safetensors.rs","byte_start":2658,"byte_end":2674,"line_start":63,"line_end":63,"column_start":8,"column_end":24,"is_primary":true,"text":[{"text":"    fn extract_metadata(tensors: &SafeTensors, file_size: usize, path: &Path) -> ModelMetadata {","highlight_start":8,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":12310,"byte_end":12337,"line_start":279,"line_end":279,"column_start":46,"column_end":73,"is_primary":true,"text":[{"text":"        let metadata = Self::extract_metadata(&tensors, file_data.len());","highlight_start":46,"highlight_end":73}],"label":null,"suggested_replacement":"(&tensors, file_data.len(), /* &std::path::Path */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this function takes 3 arguments but 2 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\safetensors.rs:279:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let metadata = Self::extract_metadata(&tensors, file_data.len());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14margument #3 of type `&std::path::Path` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\safetensors.rs:63:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn extract_metadata(tensors: &SafeTensors, file_size: usize, path: &Path) -> ModelMetadata {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let metadata = Self::extract_metadata(&tensors, file_data.len()\u001b[0m\u001b[0m\u001b[38;5;10m, /* &std::path::Path */\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\tensorflow.rs","byte_start":7553,"byte_end":7565,"line_start":179,"line_end":179,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\tensorflow.rs","byte_start":7568,"byte_end":7584,"line_start":179,"line_end":179,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorflow.rs:179:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":8835,"byte_end":8847,"line_start":224,"line_end":224,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":8850,"byte_end":8866,"line_start":224,"line_end":224,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorflow_lite.rs:224:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\keras.rs","byte_start":10582,"byte_end":10594,"line_start":280,"line_end":280,"column_start":24,"column_end":36,"is_primary":true,"text":[{"text":"                if let Ok(mut data) = output.as_slice() {","highlight_start":24,"highlight_end":36}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\keras.rs","byte_start":10597,"byte_end":10614,"line_start":280,"line_end":280,"column_start":39,"column_end":56,"is_primary":false,"text":[{"text":"                if let Ok(mut data) = output.as_slice() {","highlight_start":39,"highlight_end":56}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\keras.rs:280:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Ok(mut data) = output.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\pickle.rs","byte_start":12337,"byte_end":12349,"line_start":307,"line_end":307,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\pickle.rs","byte_start":12352,"byte_end":12368,"line_start":307,"line_end":307,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\pickle.rs:307:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\coreml.rs","byte_start":11961,"byte_end":11973,"line_start":351,"line_end":351,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\coreml.rs","byte_start":11976,"byte_end":11992,"line_start":351,"line_end":351,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\coreml.rs:351:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\openvino.rs","byte_start":11570,"byte_end":11582,"line_start":311,"line_end":311,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\openvino.rs","byte_start":11585,"byte_end":11601,"line_start":311,"line_end":311,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\openvino.rs:311:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\models\\tensorrt.rs","byte_start":12304,"byte_end":12316,"line_start":327,"line_end":327,"column_start":20,"column_end":32,"is_primary":true,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":20,"highlight_end":32}],"label":"expected `Option<&[f32]>`, found `Result<_, _>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\models\\tensorrt.rs","byte_start":12319,"byte_end":12335,"line_start":327,"line_end":327,"column_start":35,"column_end":51,"is_primary":false,"text":[{"text":"            if let Ok(mut data) = input.as_slice() {","highlight_start":35,"highlight_end":51}],"label":"this expression has type `std::option::Option<&[f32]>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<&[f32]>`\n   found enum `Result<_, _>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorrt.rs:327:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(mut data) = input.as_slice() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis expression has type `std::option::Option<&[f32]>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<&[f32]>`, found `Result<_, _>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<&[f32]>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mResult<_, _>\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `XynKore`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\safetensors.rs","byte_start":1800,"byte_end":1807,"line_start":42,"line_end":42,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"use crate::models::{XynKore, Umlaiie, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `XynKore`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\safetensors.rs:42:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{XynKore, Umlaiie, LoadOptions, ModelMetadata, UmlaiieError, UmlaiieResult, Device};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `inputs`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":862,"byte_end":868,"line_start":31,"line_end":31,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"    fn infer(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<Vec<ArrayD<f32>>> {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\onnx.rs","byte_start":862,"byte_end":868,"line_start":31,"line_end":31,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"    fn infer(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<Vec<ArrayD<f32>>> {","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":"_inputs","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `inputs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\onnx.rs:31:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn infer(&self, inputs: &[ArrayD<f32>]) -> anyhow::Result<Vec<ArrayD<f32>>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_inputs`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `buffer`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":5389,"byte_end":5395,"line_start":135,"line_end":135,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"    fn extract_tensor_info(buffer: &[u8]) -> (Vec<TensorInfo>, Vec<TensorInfo>) {","highlight_start":28,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\tensorflow_lite.rs","byte_start":5389,"byte_end":5395,"line_start":135,"line_end":135,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"    fn extract_tensor_info(buffer: &[u8]) -> (Vec<TensorInfo>, Vec<TensorInfo>) {","highlight_start":28,"highlight_end":34}],"label":null,"suggested_replacement":"_buffer","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `buffer`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorflow_lite.rs:135:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn extract_tensor_info(buffer: &[u8]) -> (Vec<TensorInfo>, Vec<TensorInfo>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_buffer`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\tensorrt.rs","byte_start":9499,"byte_end":9505,"line_start":253,"line_end":253,"column_start":76,"column_end":82,"is_primary":true,"text":[{"text":"    fn accelerate_tensor_ops(data: &mut [f32], operation: VectorOperation, device: &Device) -> anyhow::Result<()> {","highlight_start":76,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\tensorrt.rs","byte_start":9499,"byte_end":9505,"line_start":253,"line_end":253,"column_start":76,"column_end":82,"is_primary":true,"text":[{"text":"    fn accelerate_tensor_ops(data: &mut [f32], operation: VectorOperation, device: &Device) -> anyhow::Result<()> {","highlight_start":76,"highlight_end":82}],"label":null,"suggested_replacement":"_device","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `device`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\tensorrt.rs:253:76\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn accelerate_tensor_ops(data: &mut [f32], operation: VectorOperation, device: &Device) -> anyhow::Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_device`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":4894,"byte_end":4898,"line_start":137,"line_end":137,"column_start":37,"column_end":41,"is_primary":true,"text":[{"text":"    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {","highlight_start":37,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":4894,"byte_end":4898,"line_start":137,"line_end":137,"column_start":37,"column_end":41,"is_primary":true,"text":[{"text":"    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {","highlight_start":37,"highlight_end":41}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\mod.rs:137:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `format`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":4903,"byte_end":4909,"line_start":137,"line_end":137,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":4903,"byte_end":4909,"line_start":137,"line_end":137,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":"_format","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `format`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\mod.rs:137:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn write<P: AsRef<Path>>(&self, path: P, format: Option<&str>) -> anyhow::Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_format`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":5276,"byte_end":5280,"line_start":146,"line_end":146,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"    fn read_additional(&mut self, data: &[u8]) -> anyhow::Result<()> {","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":5276,"byte_end":5280,"line_start":146,"line_end":146,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"    fn read_additional(&mut self, data: &[u8]) -> anyhow::Result<()> {","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":"_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `data`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\mod.rs:146:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn read_additional(&mut self, data: &[u8]) -> anyhow::Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\models\\mod.rs","byte_start":6273,"byte_end":6279,"line_start":178,"line_end":178,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"    fn optimize_for_device(&mut self, device: &Device) -> anyhow::Result<()> {","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\models\\mod.rs","byte_start":6273,"byte_end":6279,"line_start":178,"line_end":178,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"    fn optimize_for_device(&mut self, device: &Device) -> anyhow::Result<()> {","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":"_device","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `device`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\models\\mod.rs:178:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn optimize_for_device(&mut self, device: &Device) -> anyhow::Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_device`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 19 previous errors; 18 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 19 previous errors; 18 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0308, E0432, E0433, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0061, E0308, E0432, E0433, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
