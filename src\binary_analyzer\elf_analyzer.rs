// src/binary_analyzer/elf_analyzer.rs
//! ELF binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing ELF (Executable and Linkable Format)
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::elf::{Elf, sym::{STT_FUNC, STB_GLOBAL, STB_WEAK}};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// ELF binary analyzer
pub struct ELFAnalyzer {
    // Configuration options can be added here
}

impl Default for ELFAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl ELFAnalyzer {
    /// Create a new ELF analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze an ELF file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing ELF file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the ELF file
        let elf = Elf::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse ELF file: {e}")))?;
        
        // Extract exports (symbols)
        let exports = self.extract_exports(&elf)?;
        
        // Extract imports
        let imports = self.extract_imports(&elf)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(&elf)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(&elf)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::ELF,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the ELF file
    fn extract_exports(&self, elf: &Elf) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for sym in elf.syms.iter() {
            // Only consider function symbols that are defined and global/weak
            if sym.st_type() == STT_FUNC && (sym.st_bind() == STB_GLOBAL || sym.st_bind() == STB_WEAK) && !sym.is_import() {
                if let Some(name) = elf.strtab.get_at(sym.st_name) {
                    exports.push(ExportedFunction {
                        name: name.to_string(),
                        address: sym.st_value,
                        signature: None, // Cannot determine signature from ELF alone
                        calling_convention: Some(CallingConvention::C),
                        metadata: serde_json::json!({
                            "size": sym.st_size,
                            "visibility": if sym.st_bind() == STB_GLOBAL { "global" } else { "weak" },
                        }),
                    });
                }
            }
        }
        
        Ok(exports)
    }
    
    /// Extract imported functions from the ELF file
    fn extract_imports(&self, elf: &Elf) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for sym in elf.syms.iter() {
            // Only consider function symbols that are undefined (imported)
            if sym.st_type() == STT_FUNC && sym.is_import() {
                if let Some(name) = elf.strtab.get_at(sym.st_name) {
                    // Try to determine the library name from the dynamic section
                    let library = self.determine_library_for_import(elf, name);
                    
                    imports.push(ImportedFunction {
                        name: name.to_string(),
                        library,
                        signature: None, // Cannot determine signature from ELF alone
                    });
                }
            }
        }
        
        Ok(imports)
    }
    
    /// Determine the library name for an imported symbol
    fn determine_library_for_import(&self, elf: &Elf, symbol_name: &str) -> String {
        // This is a simplified implementation. A full implementation requires analyzing
        // the .dynamic section for DT_NEEDED entries and potentially the version definitions
        // in .gnu.version_r to map symbols to specific libraries.

        // For now, return the first DT_NEEDED entry if available, or "unknown".
        if let Some(library) = elf.libraries.first() {
            log::warn!(
                "Unable to definitively map import '{}' to a specific library. Guessing '{}'.", 
                symbol_name, 
                library
            );
            return library.to_string();
        }

        log::warn!("Could not determine library for imported symbol '{}'.", symbol_name);
        "unknown".to_string()
    }
    
    /// Extract dependencies from the ELF file
    fn extract_dependencies(&self, elf: &Elf) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        for dyn_lib in &elf.libraries {
            dependencies.push(dyn_lib.to_string());
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the ELF file
    fn extract_additional_metadata(&self, elf: &Elf) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "machine": elf.header.e_machine,
            "class": if elf.is_64 { "ELF64" } else { "ELF32" },
            "endianness": if elf.little_endian { "little" } else { "big" },
            "entry_point": elf.entry,
            "is_executable": elf.header.e_type == goblin::elf::header::ET_EXEC,
            "is_dynamic": elf.header.e_type == goblin::elf::header::ET_DYN,
        }))
    }
}
