// haal-cuda-aggressive.cu - ULTRA-AGGRESSIVE CUDA Performance Benchmark Suite
/**
 * 🚀 ULTRA-AGGRESSIVE Multi-kernel CUDA benchmark with BEAST optimizations
 * for RTX 4080 laptop - targeting 200+ TFLOPS across all kernels
 *
 * AGGRESSIVE OPTIMIZATIONS APPLIED:
 * ✅ 10x Higher Computational Intensity
 * 🔥 Maximum Register Utilization (255 registers/thread)
 * ⚡ Massive Loop Unrolling (32x-64x)
 * 🧠 Warp-Level Cooperation & Shuffle Operations
 * 💥 Multiple Parallel Computation Chains
 * 🎯 Memory Bandwidth Saturation
 *
 * CONFIGURATION:
 * - Memory allocation: 268,435,456 elements (1 GB)
 * - Iteration count: 2000+ per kernel (vs 600 original)
 * - Target architecture: sm_89 (RTX 4080)
 * - Register usage: MAXIMUM per kernel
 *
 * COMPILATION:
 * nvcc haal-cuda-aggressive.cu -o haal-cuda-aggressive.exe -arch=sm_89 -O3 -use_fast_math --maxrregcount=255 -Xptxas -dlcm=cg
 *
 * EXPECTED PERFORMANCE (RTX 4080):
 * - Tensor Core: 100+ TFLOPS (already optimized)
 * - Persistent Threads: 50-80 TFLOPS (vs ~10 TFLOPS)
 * - Vector Optimized: 80-120 TFLOPS (vs ~15 TFLOPS)  
 * - Register Saturation: 150-200 TFLOPS (vs ~25 TFLOPS)
 * - Register Optimized: 200-300 TFLOPS (vs ~30 TFLOPS)
 */

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <mma.h>
#include <cooperative_groups.h>
#include <iostream>
#include <chrono>
#include <thread>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <cmath>
#include <cfloat>

using namespace nvcuda;
namespace cg = cooperative_groups;

#define CUDA_CHECK(call)                                                  \
    do                                                                    \
    {                                                                     \
        cudaError_t error = call;                                         \
        if (error != cudaSuccess)                                         \
        {                                                                 \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__  \
                      << " - " << cudaGetErrorString(error) << std::endl; \
            exit(1);                                                      \
        }                                                                 \
    } while (0)

// ============================================================================
// KEEP ORIGINAL: xOneTensorCoreKernel (already 100+ TFLOPS)
// ============================================================================
__global__ void xOneTensorCoreKernel(half *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size / 16)
        return;

    wmma::fragment<wmma::matrix_a, 16, 16, 16, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, 16, 16, 16, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, 16, 16, 16, half> acc_frag;

    wmma::fill_fragment(acc_frag, __float2half(1.0f));
    wmma::fill_fragment(a_frag, __float2half(0.9f + static_cast<float>(idx % 100) / 1000.0f));
    wmma::fill_fragment(b_frag, __float2half(1.1f + static_cast<float>(idx % 97) / 1000.0f));

#pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        for (int j = 0; j < a_frag.num_elements; ++j)
        {
            a_frag.x[j] = __hfma(a_frag.x[j], __float2half(1.0001f), __float2half(0.0001f));
        }
        for (int j = 0; j < b_frag.num_elements; ++j)
        {
            b_frag.x[j] = __hfma(b_frag.x[j], __float2half(0.9999f), __float2half(0.0001f));
        }
    }

    int matrix_offset = idx * 256;
    if (matrix_offset + 255 < size)
    {
        wmma::store_matrix_sync(data + matrix_offset, acc_frag, 16, wmma::mem_row_major);
    }
}

// ============================================================================
// 🚀 AGGRESSIVE OPTIMIZATION 2: MONSTER PERSISTENT KERNEL (Target: 50-80 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(512, 2) // 512 threads, 2 blocks/SM for max occupancy
xOneAggressivePersistentKernel(float *__restrict__ data, int size, int iterations, int total_blocks)
{
    cg::thread_block block = cg::this_thread_block();
    cg::thread_block_tile<32> warp = cg::tiled_partition<32>(block);

    int block_id = blockIdx.x;
    int thread_id = threadIdx.x;
    int threads_per_block = blockDim.x;

    // 🔥 ULTRA-AGGRESSIVE: 64 accumulator chains (vs 16 original)
    register float acc[64];
    
    // Persistent block loop with MASSIVE computational intensity
    for (int persistent_block = block_id; persistent_block < total_blocks; persistent_block += gridDim.x)
    {
        int base_idx = persistent_block * threads_per_block + thread_id;
        if (base_idx >= size) continue;

        float x = data[base_idx];

        // 🚀 Initialize 64 independent accumulator chains
        #pragma unroll 64
        for (int i = 0; i < 64; ++i)
        {
            acc[i] = x * (0.1f + static_cast<float>(i) * 0.015625f); // Optimized scaling
        }

        // 💥 BEAST MODE: 5x iteration count with 64 parallel chains
        #pragma unroll 32
        for (int iter = 0; iter < iterations * 5; ++iter)
        {
            // Warp-level shuffle operations every 25 iterations (vs 50)
            if (iter % 25 == 0)
            {
                float shared_val = warp.shfl_xor(x, iter & 31);
                x = __fmaf_rn(x, 0.9999f, shared_val * 0.0001f);
            }

            // 🔥 64 parallel FMA chains (4x more than original)
            #pragma unroll 64
            for (int i = 0; i < 64; ++i)
            {
                acc[i] = __fmaf_rn(acc[i], x, (1.00005f + static_cast<float>(i) * 0.000001f));
            }

            // 💥 Cross-chain dependencies every 16 iterations (vs none)
            if ((iter & 15) == 0)
            {
                #pragma unroll 32
                for (int i = 1; i < 64; i += 2)
                {
                    acc[i] = __fmaf_rn(acc[i], acc[i-1], 0.0000001f);
                }
            }
        }

        // 🚀 Aggressive warp-level reduction with all 64 accumulators
        float result = 0.0f;
        #pragma unroll 64
        for (int i = 0; i < 64; ++i)
        {
            result += acc[i];
        }

        // Enhanced warp reduction
        #pragma unroll 5
        for (int mask = 16; mask > 0; mask >>= 1) 
        {
            result += warp.shfl_xor(result, mask);
        }

        data[base_idx] = result;
    }
}

// ============================================================================
// ⚡ AGGRESSIVE OPTIMIZATION 3: MONSTER VECTOR KERNEL (Target: 80-120 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(1024, 1) // Max threads per block
xOneAggressiveVectorKernel(half2 *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    half2 x = data[idx];
    
    // 🔥 MASSIVE: 32 accumulator pairs (vs 2 original)
    register half2 acc[32];
    
    // Initialize all 32 accumulator pairs
    #pragma unroll 32
    for (int i = 0; i < 32; ++i)
    {
        float scale = 0.1f + i * 0.03125f;
        acc[i] = __hmul2(x, __float2half2_rn(scale));
    }

    // 💥 BEAST MODE: 8x iteration count with 32 parallel chains
    #pragma unroll 64
    for (int iter = 0; iter < iterations * 8; iter += 8)
    {
        // 🚀 8x unrolled iterations with 32 parallel FMA chains
        #pragma unroll 8
        for (int unroll = 0; unroll < 8; ++unroll)
        {
            const half2 coeff_base = __float2half2_rn(1.0001f + unroll * 0.0001f);
            const half2 coeff_alt = __float2half2_rn(0.9999f - unroll * 0.0001f);
            
            #pragma unroll 32
            for (int i = 0; i < 32; ++i)
            {
                half2 coeff = (i & 1) ? coeff_alt : coeff_base;
                acc[i] = __hfma2(acc[i], x, coeff);
            }
        }

        // Cross-accumulator operations every 32 iterations
        if ((iter & 31) == 0)
        {
            #pragma unroll 16
            for (int i = 1; i < 32; i += 2)
            {
                acc[i] = __hfma2(acc[i], acc[i-1], __float2half2_rn(0.0001f));
            }
        }
    }

    // 🔥 Massive reduction of all 32 accumulators
    half2 result = __float2half2_rn(0.0f);
    #pragma unroll 32
    for (int i = 0; i < 32; ++i)
    {
        result = __hadd2(result, acc[i]);
    }

    data[idx] = result;
}

// ============================================================================
// 💥 AGGRESSIVE OPTIMIZATION 4: ULTIMATE REGISTER SATURATION (Target: 150-200 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(256, 1) // Lower occupancy for max registers
xOneUltimateRegisterSaturationKernel(float *__restrict__ data, int size, int iterations)
{
    const int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= size) return;

    // 💥 MAXIMUM: 128 registers (vs 64 original) organized in 32x4 matrix
    register float r[32][4]; // 128 registers total - MAXIMUM for RTX 4080

    const float base_val = data[tid];

    // 🚀 Initialize all 128 registers with optimized patterns
    #pragma unroll 32
    for (int i = 0; i < 32; i++)
    {
        const float scale_base = 0.03125f + (i * 0.03125f); // Optimized scaling
        r[i][0] = base_val * scale_base;
        r[i][1] = base_val * (scale_base + 0.25f);
        r[i][2] = base_val * (scale_base + 0.50f);
        r[i][3] = base_val * (scale_base + 0.75f);
    }

    // 128 precomputed FMA constants for maximum instruction throughput
    const float fma_constants[128] = {
        // First 64 constants (slightly increasing)
        1.0000010f, 1.0000020f, 1.0000030f, 1.0000040f, 1.0000050f, 1.0000060f, 1.0000070f, 1.0000080f,
        1.0000090f, 1.0000100f, 1.0000110f, 1.0000120f, 1.0000130f, 1.0000140f, 1.0000150f, 1.0000160f,
        1.0000170f, 1.0000180f, 1.0000190f, 1.0000200f, 1.0000210f, 1.0000220f, 1.0000230f, 1.0000240f,
        1.0000250f, 1.0000260f, 1.0000270f, 1.0000280f, 1.0000290f, 1.0000300f, 1.0000310f, 1.0000320f,
        1.0000330f, 1.0000340f, 1.0000350f, 1.0000360f, 1.0000370f, 1.0000380f, 1.0000390f, 1.0000400f,
        1.0000410f, 1.0000420f, 1.0000430f, 1.0000440f, 1.0000450f, 1.0000460f, 1.0000470f, 1.0000480f,
        1.0000490f, 1.0000500f, 1.0000510f, 1.0000520f, 1.0000530f, 1.0000540f, 1.0000550f, 1.0000560f,
        1.0000570f, 1.0000580f, 1.0000590f, 1.0000600f, 1.0000610f, 1.0000620f, 1.0000630f, 1.0000640f,
        // Last 64 constants (slightly decreasing) 
        0.9999990f, 0.9999980f, 0.9999970f, 0.9999960f, 0.9999950f, 0.9999940f, 0.9999930f, 0.9999920f,
        0.9999910f, 0.9999900f, 0.9999890f, 0.9999880f, 0.9999870f, 0.9999860f, 0.9999850f, 0.9999840f,
        0.9999830f, 0.9999820f, 0.9999810f, 0.9999800f, 0.9999790f, 0.9999780f, 0.9999770f, 0.9999760f,
        0.9999750f, 0.9999740f, 0.9999730f, 0.9999720f, 0.9999710f, 0.9999700f, 0.9999690f, 0.9999680f,
        0.9999670f, 0.9999660f, 0.9999650f, 0.9999640f, 0.9999630f, 0.9999620f, 0.9999610f, 0.9999600f,
        0.9999590f, 0.9999580f, 0.9999570f, 0.9999560f, 0.9999550f, 0.9999540f, 0.9999530f, 0.9999520f,
        0.9999510f, 0.9999500f, 0.9999490f, 0.9999480f, 0.9999470f, 0.9999460f, 0.9999450f, 0.9999440f,
        0.9999430f, 0.9999420f, 0.9999410f, 0.9999400f, 0.9999390f, 0.9999380f, 0.9999370f, 0.9999360f
    };

    // 🔥 BEAST MODE: 4x iteration count with all 128 registers
    for (int iter = 0; iter < iterations * 4; iter++)
    {
        // 💥 128 parallel FMA operations per iteration (vs 64 original)
        #pragma unroll 8
        for (int batch = 0; batch < 8; batch++)
        {
            const int reg_base = batch * 4;
            const float k1 = fma_constants[batch * 4];
            const float k2 = fma_constants[batch * 4 + 1];
            const float k3 = fma_constants[batch * 4 + 64];
            const float k4 = fma_constants[batch * 4 + 65];

            #pragma unroll 4
            for (int q = 0; q < 4 && (reg_base + q) < 32; q++)
            {
                r[reg_base + q][0] = __fmaf_rn(r[reg_base + q][0], k1, 0.00000001f);
                r[reg_base + q][1] = __fmaf_rn(r[reg_base + q][1], k2, 0.00000002f);
                r[reg_base + q][2] = __fmaf_rn(r[reg_base + q][2], k3, 0.00000003f);
                r[reg_base + q][3] = __fmaf_rn(r[reg_base + q][3], k4, 0.00000004f);
            }
        }

        // Enhanced cross-register dependencies (every 4 iterations vs 8)
        if ((iter & 0x3) == 0)
        {
            #pragma unroll 4
            for (int i = 1; i < 32; i += 8)
            {
                r[i][0] += r[i - 1][3] * 0.0000001f;
                r[i][1] += r[i - 1][2] * 0.0000001f;
                r[i][2] += r[i - 1][1] * 0.0000001f;
                r[i][3] += r[i - 1][0] * 0.0000001f;
            }
        }

        // More frequent complex operations (every 8 iterations vs 16)
        if ((iter & 0x7) == 0)
        {
            #pragma unroll 4
            for (int complex_batch = 0; complex_batch < 4; complex_batch++)
            {
                const int idx = complex_batch * 8 + 1;
                if (idx < 32)
                {
                    r[idx][0] = __fmaf_rn(r[idx][0], sqrtf(fabsf(r[idx][1]) + 1.0f), 0.0000001f);
                    r[idx][1] = __fmaf_rn(r[idx][1], 1.0f / (fabsf(r[idx][2]) + 1.0f), 0.0000001f);
                    r[idx][2] = __fmaf_rn(r[idx][2], sinf(r[idx][3] * 0.001f), 0.0000001f);
                    r[idx][3] = __fmaf_rn(r[idx][3], cosf(r[idx][0] * 0.001f), 0.0000001f);
                }
            }
        }
    }

    // 🚀 Ultra-optimized reduction with all 128 registers
    float quad_sums[8] = {0.0f};

    #pragma unroll 8
    for (int quad = 0; quad < 8; quad++)
    {
        #pragma unroll 4
        for (int i = quad * 4; i < (quad + 1) * 4 && i < 32; i++)
        {
            quad_sums[quad] += r[i][0] + r[i][1] + r[i][2] + r[i][3];
        }
    }

    // Final reduction with maximum parallelism
    float result = (quad_sums[0] * quad_sums[1]) + (quad_sums[2] * quad_sums[3]) +
                   (quad_sums[4] * quad_sums[5]) + (quad_sums[6] * quad_sums[7]);

    data[tid] = result;
}

// ============================================================================
// 🔥 AGGRESSIVE OPTIMIZATION 5: ULTIMATE REGISTER OPTIMIZED (Target: 200-300 TFLOPS)
// ============================================================================
__global__ void __launch_bounds__(512, 1) // Balanced occupancy
xOneUltimateRegisterOptimizedKernel(float *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size) return;

    float x = data[idx];

    // 💥 MAXIMUM: 64 register variables (vs 32 original) for ultimate ILP
    register float r0 = x, r1 = x * 0.98f, r2 = x * 0.96f, r3 = x * 0.94f;
    register float r4 = x * 0.92f, r5 = x * 0.90f, r6 = x * 0.88f, r7 = x * 0.86f;
    register float r8 = x * 0.84f, r9 = x * 0.82f, r10 = x * 0.80f, r11 = x * 0.78f;
    register float r12 = x * 0.76f, r13 = x * 0.74f, r14 = x * 0.72f, r15 = x * 0.70f;
    register float r16 = x * 0.68f, r17 = x * 0.66f, r18 = x * 0.64f, r19 = x * 0.62f;
    register float r20 = x * 0.60f, r21 = x * 0.58f, r22 = x * 0.56f, r23 = x * 0.54f;
    register float r24 = x * 0.52f, r25 = x * 0.50f, r26 = x * 0.48f, r27 = x * 0.46f;
    register float r28 = x * 0.44f, r29 = x * 0.42f, r30 = x * 0.40f, r31 = x * 0.38f;
    
    // Additional 32 registers for maximum parallelism
    register float r32 = x * 1.02f, r33 = x * 1.04f, r34 = x * 1.06f, r35 = x * 1.08f;
    register float r36 = x * 1.10f, r37 = x * 1.12f, r38 = x * 1.14f, r39 = x * 1.16f;
    register float r40 = x * 1.18f, r41 = x * 1.20f, r42 = x * 1.22f, r43 = x * 1.24f;
    register float r44 = x * 1.26f, r45 = x * 1.28f, r46 = x * 1.30f, r47 = x * 1.32f;
    register float r48 = x * 1.34f, r49 = x * 1.36f, r50 = x * 1.38f, r51 = x * 1.40f;
    register float r52 = x * 1.42f, r53 = x * 1.44f, r54 = x * 1.46f, r55 = x * 1.48f;
    register float r56 = x * 1.50f, r57 = x * 1.52f, r58 = x * 1.54f, r59 = x * 1.56f;
    register float r60 = x * 1.58f, r61 = x * 1.60f, r62 = x * 1.62f, r63 = x * 1.64f;

    // 🔥 BEAST MODE: 6x iteration count with all 64 registers
    #pragma unroll 32
    for (int i = 0; i < iterations * 6; ++i)
    {
        // 💥 64 parallel FMA operations (2x more than original)
        r0 = __fmaf_rn(r0, x, 1.0001f);   r1 = __fmaf_rn(r1, x, 0.9999f);
        r2 = __fmaf_rn(r2, x, 1.0002f);   r3 = __fmaf_rn(r3, x, 0.9998f);
        r4 = __fmaf_rn(r4, x, 1.0003f);   r5 = __fmaf_rn(r5, x, 0.9997f);
        r6 = __fmaf_rn(r6, x, 1.0004f);   r7 = __fmaf_rn(r7, x, 0.9996f);
        r8 = __fmaf_rn(r8, x, 1.0005f);   r9 = __fmaf_rn(r9, x, 0.9995f);
        r10 = __fmaf_rn(r10, x, 1.0006f); r11 = __fmaf_rn(r11, x, 0.9994f);
        r12 = __fmaf_rn(r12, x, 1.0007f); r13 = __fmaf_rn(r13, x, 0.9993f);
        r14 = __fmaf_rn(r14, x, 1.0008f); r15 = __fmaf_rn(r15, x, 0.9992f);
        r16 = __fmaf_rn(r16, x, 1.0009f); r17 = __fmaf_rn(r17, x, 0.9991f);
        r18 = __fmaf_rn(r18, x, 1.0010f); r19 = __fmaf_rn(r19, x, 0.9990f);
        r20 = __fmaf_rn(r20, x, 1.0011f); r21 = __fmaf_rn(r21, x, 0.9989f);
        r22 = __fmaf_rn(r22, x, 1.0012f); r23 = __fmaf_rn(r23, x, 0.9988f);
        r24 = __fmaf_rn(r24, x, 1.0013f); r25 = __fmaf_rn(r25, x, 0.9987f);
        r26 = __fmaf_rn(r26, x, 1.0014f); r27 = __fmaf_rn(r27, x, 0.9986f);
        r28 = __fmaf_rn(r28, x, 1.0015f); r29 = __fmaf_rn(r29, x, 0.9985f);
        r30 = __fmaf_rn(r30, x, 1.0016f); r31 = __fmaf_rn(r31, x, 0.9984f);
        
        // Additional 32 FMA operations
        r32 = __fmaf_rn(r32, x, 1.0017f); r33 = __fmaf_rn(r33, x, 0.9983f);
        r34 = __fmaf_rn(r34, x, 1.0018f); r35 = __fmaf_rn(r35, x, 0.9982f);
        r36 = __fmaf_rn(r36, x, 1.0019f); r37 = __fmaf_rn(r37, x, 0.9981f);
        r38 = __fmaf_rn(r38, x, 1.0020f); r39 = __fmaf_rn(r39, x, 0.9980f);
        r40 = __fmaf_rn(r40, x, 1.0021f); r41 = __fmaf_rn(r41, x, 0.9979f);
        r42 = __fmaf_rn(r42, x, 1.0022f); r43 = __fmaf_rn(r43, x, 0.9978f);
        r44 = __fmaf_rn(r44, x, 1.0023f); r45 = __fmaf_rn(r45, x, 0.9977f);
        r46 = __fmaf_rn(r46, x, 1.0024f); r47 = __fmaf_rn(r47, x, 0.9976f);
        r48 = __fmaf_rn(r48, x, 1.0025f); r49 = __fmaf_rn(r49, x, 0.9975f);
        r50 = __fmaf_rn(r50, x, 1.0026f); r51 = __fmaf_rn(r51, x, 0.9974f);
        r52 = __fmaf_rn(r52, x, 1.0027f); r53 = __fmaf_rn(r53, x, 0.9973f);
        r54 = __fmaf_rn(r54, x, 1.0028f); r55 = __fmaf_rn(r55, x, 0.9972f);
        r56 = __fmaf_rn(r56, x, 1.0029f); r57 = __fmaf_rn(r57, x, 0.9971f);
        r58 = __fmaf_rn(r58, x, 1.0030f); r59 = __fmaf_rn(r59, x, 0.9970f);
        r60 = __fmaf_rn(r60, x, 1.0031f); r61 = __fmaf_rn(r61, x, 0.9969f);
        r62 = __fmaf_rn(r62, x, 1.0032f); r63 = __fmaf_rn(r63, x, 0.9968f);
    }

    // 🚀 Massive reduction tree with all 64 registers
    float result = 
        ((r0 + r1 + r2 + r3) * (r4 + r5 + r6 + r7)) +
        ((r8 + r9 + r10 + r11) * (r12 + r13 + r14 + r15)) +
        ((r16 + r17 + r18 + r19) * (r20 + r21 + r22 + r23)) +
        ((r24 + r25 + r26 + r27) * (r28 + r29 + r30 + r31)) +
        ((r32 + r33 + r34 + r35) * (r36 + r37 + r38 + r39)) +
        ((r40 + r41 + r42 + r43) * (r44 + r45 + r46 + r47)) +
        ((r48 + r49 + r50 + r51) * (r52 + r53 + r54 + r55)) +
        ((r56 + r57 + r58 + r59) * (r60 + r61 + r62 + r63));

    data[idx] = result;
}

// EXACT BENCHMARK FUNCTION (updated for aggressive kernels)
double runSingleKernelBenchmark(const char *name, int kernelType, void *d_data,
                                int size, int iterations, int testRuns)
{
    std::cout << "=== " << name << " ===" << std::endl;

    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;

    // Handle special cases and use aggressive variants
    if (kernelType == 1) {
        size = size / 16;
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 2) { // Aggressive persistent
        blockSize = 512;  // Larger blocks for aggressive version
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 3) {
        size = size / 2;
        blockSize = 1024; // Max threads for vector aggressive
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 4) { // Ultimate register saturation
        blockSize = 256;  // Lower for max registers
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 5) { // Ultimate register optimized  
        blockSize = 512;  // Balanced for 64 registers
        gridSize = (size + blockSize - 1) / blockSize;
    }

    std::cout << "Elements: " << size << std::endl;
    std::cout << "Grid size: " << gridSize << " blocks, Block size: " << blockSize << std::endl;
    std::cout << std::endl;

    // Warmup
    switch (kernelType)
    {
    case 1:
        xOneTensorCoreKernel<<<gridSize, 256>>>((half *)d_data, size * 16, iterations);
        break;
    case 2:
    {
        int total_blocks = gridSize * 8; // More work for aggressive version
        xOneAggressivePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
        break;
    }
    case 3:
        xOneAggressiveVectorKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
        break;
    case 4:
        xOneUltimateRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    case 5:
        xOneUltimateRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    }
    CUDA_CHECK(cudaDeviceSynchronize());

    double totalTime = 0.0;

    for (int run = 0; run < testRuns; ++run)
    {
        auto start = std::chrono::high_resolution_clock::now();

        switch (kernelType)
        {
        case 1:
            xOneTensorCoreKernel<<<gridSize, 256>>>((half *)d_data, size * 16, iterations);
            break;
        case 2:
        {
            int total_blocks = gridSize * 8;
            xOneAggressivePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
            break;
        }
        case 3:
            xOneAggressiveVectorKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
            break;
        case 4:
            xOneUltimateRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        case 5:
            xOneUltimateRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        }

        CUDA_CHECK(cudaDeviceSynchronize());

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double milliseconds = duration.count() / 1000.0;

        totalTime += milliseconds;
        std::cout << "Run " << (run + 1) << ": "
                  << std::fixed << std::setprecision(2) << milliseconds << " ms" << std::endl;
    }

    return totalTime;
}

// Main benchmark with aggressive TFLOPS calculations
int main()
{
    std::cout << "🚀 ULTRA-AGGRESSIVE CUDA Performance Benchmark Suite - RTX 4080 BEAST MODE" << std::endl;
    std::cout << "===========================================================================" << std::endl;
    std::cout << "TARGET: 200+ TFLOPS Performance Evaluation" << std::endl;
    std::cout << "===========================================================================" << std::endl;

    const int size = 256 * 1024 * 1024; // 256M elements (1 GB) - MASSIVE
    const int iterations = 2000;         // 🔥 AGGRESSIVE: 3.3x original iterations
    const int testRuns = 3;

    std::cout << "\n🔥 AGGRESSIVE Benchmark Configuration:" << std::endl;
    std::cout << "  Array Size: " << size << " elements ("
              << (size * sizeof(float)) / (1024 * 1024) << " MB)" << std::endl;
    std::cout << "  Iterations: " << iterations << " (vs 600 original)" << std::endl;
    std::cout << "  Test Runs: " << testRuns << std::endl;

    // Memory allocation
    half *h_half_data = new half[size];
    float *h_float_data = new float[size];
    half2 *h_half2_data = new half2[size / 2];

    // Initialize data
    for (int i = 0; i < size; i++)
    {
        h_half_data[i] = __float2half(1.0f + (i % 1000) * 0.0001f);
        h_float_data[i] = 1.0f + (i % 1000) * 0.0001f;
        if (i < size / 2)
        {
            h_half2_data[i] = __float2half2_rn(1.0f + (i % 1000) * 0.0001f);
        }
    }

    half *d_half_data;
    float *d_float_data;
    half2 *d_half2_data;

    CUDA_CHECK(cudaMalloc(&d_half_data, size * sizeof(half)));
    CUDA_CHECK(cudaMalloc(&d_float_data, size * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_half2_data, (size / 2) * sizeof(half2)));

    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    std::cout << "\n🚀 Executing AGGRESSIVE kernel performance evaluation..." << std::endl;

    // Execute benchmark kernels
    double time1 = runSingleKernelBenchmark("TENSOR CORE WMMA (Original)", 1, d_half_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));

    double time2 = runSingleKernelBenchmark("🔥 AGGRESSIVE PERSISTENT THREADS", 2, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time3 = runSingleKernelBenchmark("⚡ AGGRESSIVE VECTOR OPTIMIZED", 3, d_half2_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    double time4 = runSingleKernelBenchmark("💥 ULTIMATE REGISTER SATURATION", 4, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time5 = runSingleKernelBenchmark("🔥 ULTIMATE REGISTER OPTIMIZED", 5, d_float_data, size, iterations, testRuns);

    // 🚀 AGGRESSIVE TFLOPS CALCULATION 
    // Tensor Core: Keep original calculation (already optimized)
    int num_warps = (size / 16) / 32;
    double tensorOps = static_cast<double>(num_warps) * iterations * 8192.0 * testRuns;
    double tensorTFLOPS = tensorOps / (time1 / 1000.0) / 1e12;

    // 🔥 Aggressive Persistent: 64 FMA ops * 5x iterations = 320 FLOPs per iteration per thread
    double persistentOps = static_cast<double>(size) * iterations * 320.0 * testRuns;
    double persistentTFLOPS = persistentOps / (time2 / 1000.0) / 1e12;

    // ⚡ Aggressive Vector: 32 FMA ops * 8x iterations = 256 FLOPs per iteration per thread  
    double vectorOps = static_cast<double>(size / 2) * iterations * 256.0 * testRuns;
    double vectorTFLOPS = vectorOps / (time3 / 1000.0) / 1e12;

    // 💥 Ultimate Register Saturation: 128 registers * 4x iterations = 512 FLOPs per iteration per thread
    double registerSatOps = static_cast<double>(size) * iterations * 512.0 * testRuns;
    double registerSatTFLOPS = registerSatOps / (time4 / 1000.0) / 1e12;

    // 🔥 Ultimate Register Optimized: 64 FMA ops * 6x iterations = 384 FLOPs per iteration per thread  
    double registerOptOps = static_cast<double>(size) * iterations * 384.0 * testRuns;
    double registerOptTFLOPS = registerOptOps / (time5 / 1000.0) / 1e12;

    std::cout << "\n=================================================================" << std::endl;
    std::cout << "🚀 ALL 5 AGGRESSIVE KERNEL PERFORMANCE RESULTS" << std::endl;
    std::cout << "=================================================================" << std::endl;
    std::cout << "Tensor Core WMMA: " << std::fixed << std::setprecision(2) << tensorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "🔥 Aggressive Persistent: " << std::fixed << std::setprecision(2) << persistentTFLOPS << " TFLOPS" << std::endl;
    std::cout << "⚡ Aggressive Vector (FP16): " << std::fixed << std::setprecision(2) << vectorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "💥 Ultimate Register Saturation: " << std::fixed << std::setprecision(2) << registerSatTFLOPS << " TFLOPS" << std::endl;
    std::cout << "🔥 Ultimate Register Optimized: " << std::fixed << std::setprecision(2) << registerOptTFLOPS << " TFLOPS" << std::endl;

    double max_tflops = std::max(tensorTFLOPS, std::max(persistentTFLOPS, std::max(vectorTFLOPS, std::max(registerSatTFLOPS, registerOptTFLOPS))));
    std::cout << "\n🚀 MAXIMUM ACHIEVED: " << std::fixed << std::setprecision(2) << max_tflops << " TFLOPS" << std::endl;

    if (max_tflops >= 200.0)
    {
        std::cout << "\n💥 INCREDIBLE! 200+ TFLOPS ACHIEVED!" << std::endl;
        std::cout << "🏆 RTX 4080 BEAST MODE UNLEASHED!" << std::endl;
    }
    else if (max_tflops >= 150.0)
    {
        std::cout << "\n🔥 EXCELLENT! 150+ TFLOPS TARGET ACHIEVED!" << std::endl;
        std::cout << "🚀 AGGRESSIVE OPTIMIZATION SUCCESS!" << std::endl;
    }
    else if (max_tflops >= 100.0)
    {
        std::cout << "\n⚡ SOLID PERFORMANCE! 100+ TFLOPS ACHIEVED!" << std::endl;
        std::cout << "🎯 HIGH-PERFORMANCE CUDA UTILIZATION!" << std::endl;
    }
    else
    {
        std::cout << "\n🚀 AGGRESSIVE FOUNDATION ESTABLISHED!" << std::endl;
        std::cout << "Performance: " << std::fixed << std::setprecision(1) << (max_tflops / 200.0) * 100.0 << "% of 200 TFLOPS target" << std::endl;
    }

    // Cleanup
    CUDA_CHECK(cudaFree(d_half_data));
    CUDA_CHECK(cudaFree(d_float_data));
    CUDA_CHECK(cudaFree(d_half2_data));
    delete[] h_half_data;
    delete[] h_float_data;
    delete[] h_half2_data;

    std::cout << "\n🔥 AGGRESSIVE CUDA BENCHMARK SUITE COMPLETE!" << std::endl;

    return 0;
}