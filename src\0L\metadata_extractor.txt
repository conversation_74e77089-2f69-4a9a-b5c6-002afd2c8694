
Directory: metadata_extractor
File: signature_validator.rs
============================
// src/metadata_extractor/signature_validator.rs
//! Signature validator for the OmniForge compiler.
//!
//! This module provides functionality for validating function signatures
//! and ensuring they are compatible across different backends.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use crate::error::{OmniError, OmniResult};
use super::{ExtractedSignature, ExtractedType};

/// Signature validator
pub struct SignatureValidator {
    // Configuration options can be added here
}

impl Default for SignatureValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl SignatureValidator {
    /// Create a new signature validator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Validate a function signature
    pub fn validate_signature(&self, signature: &ExtractedSignature) -> OmniResult<()> {
        // Validate return type
        self.validate_type(&signature.return_type)?;
        
        // Validate parameter types
        for param in &signature.parameter_types {
            self.validate_type(&param.type_info)?;
        }
        
        Ok(())
    }
    
    /// Validate a type
    fn validate_type(&self, type_info: &ExtractedType) -> OmniResult<()> {
        // Check for unsupported types
        if type_info.name.contains("void") && type_info.is_pointer {
            // void* is a special case
            return Ok(());
        }
        
        if type_info.name.contains("void") && !type_info.is_pointer {
            // void is only valid as a return type
            return Ok(());
        }
        
        // Ensure size is known for non-void types
        if !type_info.name.contains("void") && type_info.size.is_none() {
            return Err(OmniError::MetadataExtraction(
                format!("Type size unknown for {}", type_info.name)
            ));
        }
        
        // Check array dimensions
        if type_info.is_array && type_info.array_dimensions.is_empty() {
            return Err(OmniError::MetadataExtraction(
                format!("Array type {} has no dimensions", type_info.name)
            ));
        }
        
        Ok(())
    }
    
    /// Check if two signatures are compatible
    pub fn are_signatures_compatible(&self, sig1: &ExtractedSignature, sig2: &ExtractedSignature) -> bool {
        // Check return type compatibility
        if !self.are_types_compatible(&sig1.return_type, &sig2.return_type) {
            return false;
        }
        
        // Check parameter count
        if sig1.parameter_types.len() != sig2.parameter_types.len() {
            return false;
        }
        
        // Check parameter type compatibility
        for (param1, param2) in sig1.parameter_types.iter().zip(sig2.parameter_types.iter()) {
            if !self.are_types_compatible(&param1.type_info, &param2.type_info) {
                return false;
            }
        }
        
        // Check variadic compatibility
        if sig1.is_variadic != sig2.is_variadic {
            return false;
        }
        
        true
    }
    
    /// Check if two types are compatible
    fn are_types_compatible(&self, type1: &ExtractedType, type2: &ExtractedType) -> bool {
        // Check basic compatibility
        if type1.is_pointer != type2.is_pointer {
            return false;
        }
        
        if type1.is_array != type2.is_array {
            return false;
        }
        
        // Special case for void
        if type1.name.contains("void") && type2.name.contains("void") {
            return true;
        }
        
        // Check array dimensions
        if type1.is_array && type2.is_array
            && type1.array_dimensions != type2.array_dimensions {
                return false;
            }
        
        // Check size compatibility
        if let (Some(size1), Some(size2)) = (type1.size, type2.size) {
            if size1 != size2 {
                return false;
            }
        }
        
        // Check const and volatile compatibility
        // In most cases, const/volatile on parameters doesn't affect binary compatibility
        // but they can affect semantics
        
        true
    }
}



Directory: metadata_extractor
File: mod.rs
============
// src/metadata_extractor/mod.rs
//! Metadata extractor for the OmniForge compiler.
//!
//! This module provides functionality for extracting metadata from
//! compiled artifacts such as function signatures, memory layouts,
//! and launch parameters.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use serde::{Serialize, Deserialize};

use crate::error::{OmniError, OmniResult};
use crate::binary_analyzer::{BinaryAnalyzer, BinaryMetadata, BinaryType, ExportedFunction};
use crate::ahaw::{self, TaskCharacteristics};

mod ptx_metadata;
mod signature_validator;

pub use self::ptx_metadata::PTXMetadataExtractor;
pub use self::signature_validator::SignatureValidator;

/// Metadata extraction options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetadataExtractionOptions {
    /// Extract memory layouts
    pub extract_memory_layouts: bool,
    
    /// Extract launch parameters
    pub extract_launch_parameters: bool,
    
    /// Extract function signatures
    pub extract_function_signatures: bool,
    
    /// Extract type information
    pub extract_type_info: bool,
}

impl Default for MetadataExtractionOptions {
    fn default() -> Self {
        Self {
            extract_memory_layouts: true,
            extract_launch_parameters: true,
            extract_function_signatures: true,
            extract_type_info: true,
        }
    }
}

/// Extracted metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedMetadata {
    /// Binary metadata
    pub binary_metadata: BinaryMetadata,
    
    /// Extracted functions
    pub functions: Vec<ExtractedFunction>,
    
    /// Extracted types
    pub types: Vec<ExtractedType>,
    
    /// Additional metadata
    pub additional_metadata: serde_json::Value,
}

/// Extracted function metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedFunction {
    /// Function name
    pub name: String,
    
    /// Function signature
    pub signature: Option<ExtractedSignature>,
    
    /// Function type (kernel, device, host)
    pub function_type: FunctionType,
    
    /// Launch parameters (for CUDA kernels)
    pub launch_params: Option<LaunchParameters>,
    
    /// Memory layout
    pub memory_layout: Option<MemoryLayout>,
    
    /// Additional function metadata
    pub metadata: serde_json::Value,
}

/// Function type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum FunctionType {
    /// CUDA kernel
    Kernel,
    
    /// CUDA device function
    Device,
    
    /// Host function
    Host,
}

/// Extracted signature
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedSignature {
    /// Return type
    pub return_type: ExtractedType,
    
    /// Parameter types
    pub parameter_types: Vec<ExtractedParameter>,
    
    /// Is variadic
    pub is_variadic: bool,
    
    /// Calling convention
    pub calling_convention: String,
}

/// Extracted parameter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedParameter {
    /// Parameter name
    pub name: String,
    
    /// Parameter type
    pub type_info: ExtractedType,
    
    /// Parameter attributes
    pub attributes: Vec<String>,
}

/// Extracted type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedType {
    /// Type name
    pub name: String,
    
    /// Type size in bytes
    pub size: Option<usize>,
    
    /// Type alignment in bytes
    pub alignment: Option<usize>,
    
    /// Is pointer
    pub is_pointer: bool,
    
    /// Is array
    pub is_array: bool,
    
    /// Array dimensions (if is_array is true)
    pub array_dimensions: Vec<usize>,
    
    /// Is const
    pub is_const: bool,
    
    /// Is volatile
    pub is_volatile: bool,
}

/// Launch parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LaunchParameters {
    /// Grid dimensions
    pub grid_dim: [u32; 3],
    
    /// Block dimensions
    pub block_dim: [u32; 3],
    
    /// Shared memory size in bytes
    pub shared_mem_bytes: usize,
    
    /// Dynamically sized shared memory
    pub dynamic_shared_mem: bool,
}

/// Memory layout
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryLayout {
    /// Total size in bytes
    pub total_size: usize,
    
    /// Field offsets
    pub field_offsets: Vec<FieldOffset>,
}

/// Field offset
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldOffset {
    /// Field name
    pub name: String,
    
    /// Offset in bytes
    pub offset: usize,
    
    /// Size in bytes
    pub size: usize,
}

/// Metadata extractor
///
/// Extracts metadata from binary files with configurable options and signature validation.
/// The extractor can be configured to selectively extract different types of metadata
/// and validate function signatures for integrity.
pub struct MetadataExtractor {
    /// Signature validator for verifying function signature integrity
    signature_validator: SignatureValidator,
    /// Extraction options controlling what metadata to extract
    options: MetadataExtractionOptions,
}

/// Builder for configuring MetadataExtractor
pub struct MetadataExtractorBuilder {
    signature_validator: Option<SignatureValidator>,
    options: Option<MetadataExtractionOptions>,
}

impl MetadataExtractorBuilder {
    /// Create a new builder
    pub fn new() -> Self {
        Self {
            signature_validator: None,
            options: None,
        }
    }

    /// Set the signature validator
    pub fn with_signature_validator(mut self, validator: SignatureValidator) -> Self {
        self.signature_validator = Some(validator);
        self
    }

    /// Set the extraction options
    pub fn with_options(mut self, options: MetadataExtractionOptions) -> Self {
        self.options = Some(options);
        self
    }

    /// Build the MetadataExtractor
    pub fn build(self) -> MetadataExtractor {
        MetadataExtractor {
            signature_validator: self.signature_validator.unwrap_or_else(SignatureValidator::new),
            options: self.options.unwrap_or_default(),
        }
    }
}

impl Default for MetadataExtractorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl MetadataExtractor {
    /// Create a new metadata extractor with the given options
    pub fn new(options: MetadataExtractionOptions) -> Self {
        Self {
            signature_validator: SignatureValidator::new(),
            options,
        }
    }

    /// Create a new metadata extractor with custom signature validator and options
    pub fn with_validator(signature_validator: SignatureValidator, options: MetadataExtractionOptions) -> Self {
        Self {
            signature_validator,
            options,
        }
    }

    /// Create a builder for configuring the metadata extractor
    pub fn builder() -> MetadataExtractorBuilder {
        MetadataExtractorBuilder::new()
    }
    
    /// Extract metadata from a binary file
    pub fn extract_metadata(&self, path: &Path) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting metadata from: {}", path.display());
        
        // First, analyze the binary
        let binary_metadata = BinaryAnalyzer::new().analyze_binary(path)?;
        
        // Extract metadata based on binary type
        match binary_metadata.binary_type {
            BinaryType::PTX => self.extract_ptx_metadata(path, binary_metadata),
            BinaryType::Cubin => self.extract_cubin_metadata(path, binary_metadata),
            BinaryType::PE | BinaryType::ELF | BinaryType::MachO | BinaryType::Object => {
                self.extract_generic_metadata(path, binary_metadata)
            }
            BinaryType::Unknown => Err(OmniError::UnsupportedFileType(path.to_path_buf())),
        }
    }
    
    /// Extract metadata from a PTX file
    fn extract_ptx_metadata(&self, path: &Path, binary_metadata: BinaryMetadata) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting PTX metadata from: {}", path.display());

        let mut metadata = PTXMetadataExtractor::new().extract_metadata(path, binary_metadata)?;

        // Apply options-based filtering
        self.apply_extraction_options(&mut metadata)?;

        // Validate signatures if signature validation is enabled
        self.validate_extracted_signatures(&metadata)?;

        Ok(metadata)
    }
    
    /// Extract metadata from a cubin file
    fn extract_cubin_metadata(&self, path: &Path, binary_metadata: BinaryMetadata) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting cubin metadata from: {}", path.display());

        // Advanced cubin metadata extraction with comprehensive CUDA binary analysis
        // Cubin files are CUDA binary files that contain compiled kernels with rich metadata

        let functions = binary_metadata.exports.iter()
            .map(|export| {
                let mut func = self.convert_export_to_extracted_function(export);
                // For cubin files, assume exported functions are CUDA kernels
                func.function_type = FunctionType::Kernel;

                // Extract comprehensive CUDA-specific metadata
                if let Some(additional_meta) = binary_metadata.additional_metadata.as_object() {
                    // Extract launch parameters with intelligent defaults
                    let registers_per_thread = additional_meta.get("registers_per_thread")
                        .and_then(|v| v.as_u64())
                        .unwrap_or(32) as u32;

                    let local_memory_size = additional_meta.get("local_memory_size")
                        .and_then(|v| v.as_u64())
                        .unwrap_or(0) as usize;

                    let max_threads_per_block = additional_meta.get("max_threads_per_block")
                        .and_then(|v| v.as_u64())
                        .unwrap_or(1024) as u32;

                    // Intelligent block size calculation based on registers and occupancy
                    let optimal_block_size = calculate_optimal_block_size(
                        registers_per_thread,
                        local_memory_size as u32,
                        max_threads_per_block
                    );

                    func.launch_params = Some(LaunchParameters {
                        grid_dim: [1, 1, 1], // Default, should be set by caller
                        block_dim: [optimal_block_size, 1, 1],
                        shared_mem_bytes: additional_meta.get("shared_memory_size")
                            .and_then(|v| v.as_u64())
                            .unwrap_or(0) as usize,
                        dynamic_shared_mem: additional_meta.get("uses_dynamic_shared_memory")
                            .and_then(|v| v.as_bool())
                            .unwrap_or(false),
                    });

                    // Extract comprehensive cubin metadata
                    let mut cubin_metadata = serde_json::json!({
                        "binary_type": "cubin",
                        "sm_version": additional_meta.get("sm_version")
                            .unwrap_or(&serde_json::Value::String("unknown".to_string())),
                        "ptx_version": additional_meta.get("ptx_version")
                            .unwrap_or(&serde_json::Value::String("unknown".to_string())),
                        "cuda_version": additional_meta.get("cuda_version")
                            .unwrap_or(&serde_json::Value::String("unknown".to_string())),
                        "registers_per_thread": registers_per_thread,
                        "local_memory_size": local_memory_size,
                        "max_threads_per_block": max_threads_per_block,
                        "optimal_block_size": optimal_block_size,
                        "occupancy_estimate": calculate_occupancy_estimate(
                            registers_per_thread,
                            local_memory_size as u32,
                            optimal_block_size as u32
                        )
                    });

                    // Extract instruction-level metadata if available
                    if let Some(instructions) = additional_meta.get("instructions") {
                        if let Some(inst_array) = instructions.as_array() {
                            cubin_metadata["instruction_count"] = serde_json::Value::Number(
                                serde_json::Number::from(inst_array.len())
                            );
                            
                            // Analyze instruction patterns
                            let mut memory_instructions = 0;
                            let mut arithmetic_instructions = 0;
                            let mut control_instructions = 0;
                            
                            for inst in inst_array {
                                if let Some(inst_type) = inst.get("type").and_then(|v| v.as_str()) {
                                    match inst_type {
                                        "load" | "store" | "ld" | "st" => memory_instructions += 1,
                                        "add" | "mul" | "mad" | "fma" => arithmetic_instructions += 1,
                                        "bra" | "call" | "ret" | "exit" => control_instructions += 1,
                                        _ => {}
                                    }
                                }
                            }
                            
                            cubin_metadata["instruction_analysis"] = serde_json::json!({
                                "memory_instructions": memory_instructions,
                                "arithmetic_instructions": arithmetic_instructions,
                                "control_instructions": control_instructions,
                                "arithmetic_intensity": if memory_instructions > 0 {
                                    arithmetic_instructions as f64 / memory_instructions as f64
                                } else {
                                    0.0
                                }
                            });
                        }
                    }

                    // Extract memory access patterns
                    if let Some(memory_accesses) = additional_meta.get("memory_accesses") {
                        cubin_metadata["memory_access_patterns"] = memory_accesses.clone();
                    }

                    // Extract performance characteristics
                    if let Some(perf_data) = additional_meta.get("performance_data") {
                        cubin_metadata["performance_characteristics"] = perf_data.clone();
                    }

                    func.metadata = cubin_metadata;
                } else {
                    // Fallback metadata when additional_metadata is not available
                    func.launch_params = Some(LaunchParameters {
                        grid_dim: [1, 1, 1],
                        block_dim: [256, 1, 1], // Conservative default
                        shared_mem_bytes: 0,
                        dynamic_shared_mem: false,
                    });

                    func.metadata = serde_json::json!({
                        "binary_type": "cubin",
                        "sm_version": "unknown",
                        "ptx_version": "unknown",
                        "cuda_version": "unknown",
                        "registers_per_thread": 32,
                        "extraction_method": "fallback"
                    });
                }

                func
            })
            .collect::<Vec<_>>();

        // Extract CUDA-specific types from cubin with comprehensive type information
        let types = vec![
            // Basic CUDA types
            ExtractedType {
                name: "float".to_string(),
                size: Some(4),
                alignment: Some(4),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            ExtractedType {
                name: "double".to_string(),
                size: Some(8),
                alignment: Some(8),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            ExtractedType {
                name: "int".to_string(),
                size: Some(4),
                alignment: Some(4),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            // CUDA-specific vector types
            ExtractedType {
                name: "float4".to_string(),
                size: Some(16),
                alignment: Some(16),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            ExtractedType {
                name: "int4".to_string(),
                size: Some(16),
                alignment: Some(16),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            // CUDA texture and surface types
            ExtractedType {
                name: "cudaTextureObject_t".to_string(),
                size: Some(8),
                alignment: Some(8),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
            ExtractedType {
                name: "cudaSurfaceObject_t".to_string(),
                size: Some(8),
                alignment: Some(8),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            },
        ];

        // Calculate values before moving into struct
        let kernel_count = functions.len();
        let cuda_architecture = detect_cuda_architecture(&binary_metadata);
        let optimization_level = detect_optimization_level(&binary_metadata);
        let debug_info_present = has_debug_information(&binary_metadata);
        let line_info_present = has_line_information(&binary_metadata);

        let mut metadata = ExtractedMetadata {
            binary_metadata,
            functions,
            types,
            additional_metadata: serde_json::json!({
                "format": "cubin",
                "architecture": "cuda",
                "kernel_count": kernel_count,
                "extraction_timestamp": chrono::Utc::now().to_rfc3339(),
                "extractor_version": env!("CARGO_PKG_VERSION"),
                "cuda_architecture_detected": cuda_architecture,
                "optimization_level": optimization_level,
                "debug_info_present": debug_info_present,
                "line_info_present": line_info_present
            }),
        };

        // Apply options-based filtering
        self.apply_extraction_options(&mut metadata)?;

        // Validate signatures if signature validation is enabled
        self.validate_extracted_signatures(&metadata)?;

        Ok(metadata)
    }
    
    /// Extract metadata from a generic binary file
    fn extract_generic_metadata(&self, path: &Path, binary_metadata: BinaryMetadata) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting generic metadata from: {}", path.display());

        // Convert exports to extracted functions
        let functions = binary_metadata.exports.iter()
            .map(|export| self.convert_export_to_extracted_function(export))
            .collect::<Vec<_>>();

        let mut metadata = ExtractedMetadata {
            binary_metadata,
            functions,
            types: Vec::new(),
            additional_metadata: serde_json::Value::Null,
        };

        // Apply options-based filtering
        self.apply_extraction_options(&mut metadata)?;

        // Validate signatures if signature validation is enabled
        self.validate_extracted_signatures(&metadata)?;

        Ok(metadata)
    }
    
    /// Convert an exported function to an extracted function
    fn convert_export_to_extracted_function(&self, export: &ExportedFunction) -> ExtractedFunction {
        // Determine function type
        let function_type = if let Some(calling_convention) = export.calling_convention {
            match calling_convention {
                crate::binary_analyzer::CallingConvention::CudaKernel => FunctionType::Kernel,
                _ => FunctionType::Host,
            }
        } else {
            FunctionType::Host
        };
        
        // Convert signature
        let signature = export.signature.as_ref().map(|sig| {
            ExtractedSignature {
                return_type: ExtractedType {
                    name: sig.return_type.name.clone(),
                    size: sig.return_type.size,
                    alignment: sig.return_type.alignment,
                    is_pointer: sig.return_type.is_pointer,
                    is_array: sig.return_type.is_array,
                    array_dimensions: sig.return_type.array_dimensions.clone(),
                    is_const: false,
                    is_volatile: false,
                },
                parameter_types: sig.parameter_types.iter()
                    .enumerate()
                    .map(|(i, param)| {
                        ExtractedParameter {
                            name: format!("param{i}"),
                            type_info: ExtractedType {
                                name: param.name.clone(),
                                size: param.size,
                                alignment: param.alignment,
                                is_pointer: param.is_pointer,
                                is_array: param.is_array,
                                array_dimensions: param.array_dimensions.clone(),
                                is_const: false,
                                is_volatile: false,
                            },
                            attributes: Vec::new(),
                        }
                    })
                    .collect(),
                is_variadic: sig.is_variadic,
                calling_convention: format!("{:?}", export.calling_convention.unwrap_or(crate::binary_analyzer::CallingConvention::C)),
            }
        });
        
        // Extract launch parameters for CUDA kernels
        let launch_params = if function_type == FunctionType::Kernel {
            export.metadata.get("shared_memory").map(|shared_mem| LaunchParameters {
                    grid_dim: [1, 1, 1], // Default
                    block_dim: [256, 1, 1], // Default
                    shared_mem_bytes: shared_mem.as_u64().unwrap_or(0) as usize,
                    dynamic_shared_mem: false,
                })
        } else {
            None
        };
        
        ExtractedFunction {
            name: export.name.clone(),
            signature,
            function_type,
            launch_params,
            memory_layout: None,
            metadata: export.metadata.clone(),
        }
    }

    /// Apply extraction options to filter metadata based on configuration
    fn apply_extraction_options(&self, metadata: &mut ExtractedMetadata) -> OmniResult<()> {
        // Filter functions based on options
        if !self.options.extract_function_signatures {
            for function in &mut metadata.functions {
                function.signature = None;
            }
        }

        if !self.options.extract_launch_parameters {
            for function in &mut metadata.functions {
                function.launch_params = None;
            }
        }

        if !self.options.extract_memory_layouts {
            for function in &mut metadata.functions {
                function.memory_layout = None;
            }
        }

        // Clear types if type extraction is disabled
        if !self.options.extract_type_info {
            metadata.types.clear();
        }

        Ok(())
    }

    /// Validate extracted signatures using the signature validator
    fn validate_extracted_signatures(&self, metadata: &ExtractedMetadata) -> OmniResult<()> {
        for function in &metadata.functions {
            if let Some(ref signature) = function.signature {
                self.signature_validator.validate_signature(signature)
                    .map_err(|e| OmniError::MetadataExtraction(
                        format!("Signature validation failed for function '{}': {}", function.name, e)
                    ))?;
            }
        }
        Ok(())
    }

    /// Accelerated metadata processing for large binary files
    pub fn accelerated_metadata_processing(&self, metadata_vectors: &[f32]) -> OmniResult<Vec<f32>> {
        let mut data = metadata_vectors.to_vec();

        // Use acceleration for metadata processing on large binaries
        if data.len() > 2000 {
            // Create task characteristics for metadata processing
            let characteristics = TaskCharacteristics {
                data_size: data.len(),
                compute_intensity: 0.6,
                parallelizability: 0.75,
                memory_access_pattern: "random".to_string(),
                cache_locality_index: 0.6,
                expected_duration_ms: data.len() as f64 * 0.01,
                priority: "normal".to_string(),
            };

            match ahaw::util::accelerate_data_processing(&mut data, ahaw::VectorOperation::Norm, &ahaw::AccelerationHint::Auto, characteristics) {
                Ok(result) => {
                    println!("🚀 Accelerated metadata processing: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                    println!("   Cache hit ratio: {:.1}%, power: {:.1}W",
                            result.performance_metrics.cache_hit_ratio * 100.0,
                            result.performance_metrics.power_consumption_watts);
                },
                Err(e) => {
                    println!("⚠️ Metadata processing acceleration failed: {}", e);
                }
            }
        }

        Ok(data)
    }

    /// Accelerated signature validation with pattern matching
    pub fn accelerated_signature_validation(&self, signatures: &[u8]) -> OmniResult<bool> {
        // Use the binary analyzer's accelerated signature validation
        let binary_analyzer = BinaryAnalyzer::new();
        binary_analyzer.accelerated_signature_validation(signatures)
    }
}

impl Default for MetadataExtractor {
    fn default() -> Self {
        Self::new(MetadataExtractionOptions::default())
    }
}

/// Calculate optimal block size based on register usage and memory constraints
fn calculate_optimal_block_size(
    registers_per_thread: u32,
    local_memory_size: u32,
    max_threads_per_block: u32,
) -> u32 {
    // Simple heuristic: aim for good occupancy while respecting resource limits
    let max_threads_by_registers = if registers_per_thread > 0 {
        // Assume 65536 registers per SM (for modern GPUs)
        65536 / registers_per_thread
    } else {
        max_threads_per_block
    };

    let max_threads_by_memory = if local_memory_size > 0 {
        // Assume 48KB local memory per SM
        (48 * 1024) / local_memory_size
    } else {
        max_threads_per_block
    };

    // Take the minimum and round down to nearest power of 2
    let optimal = max_threads_by_registers
        .min(max_threads_by_memory)
        .min(max_threads_per_block);

    // Round down to nearest power of 2 for better performance
    if optimal >= 512 { 512 }
    else if optimal >= 256 { 256 }
    else if optimal >= 128 { 128 }
    else if optimal >= 64 { 64 }
    else if optimal >= 32 { 32 }
    else { 32 } // Minimum reasonable block size
}

/// Calculate occupancy estimate based on resource usage
fn calculate_occupancy_estimate(
    registers_per_thread: u32,
    local_memory_size: u32,
    block_size: u32,
) -> f64 {
    // Simple occupancy calculation
    let max_blocks_by_registers = if registers_per_thread > 0 {
        65536 / (registers_per_thread * block_size)
    } else {
        16 // Default max blocks per SM
    };

    let max_blocks_by_memory = if local_memory_size > 0 {
        (48 * 1024) / (local_memory_size * block_size)
    } else {
        16
    };

    let max_blocks = max_blocks_by_registers.min(max_blocks_by_memory).min(16);
    let theoretical_max_threads = 2048; // For modern GPUs
    let achieved_threads = max_blocks * block_size;

    (achieved_threads as f64 / theoretical_max_threads as f64).min(1.0)
}

/// Detect CUDA architecture from binary metadata
fn detect_cuda_architecture(binary_metadata: &BinaryMetadata) -> String {
    // Try to extract architecture from various sources
    if let Some(arch) = binary_metadata.additional_metadata.get("sm_version") {
        if let Some(arch_str) = arch.as_str() {
            return arch_str.to_string();
        }
    }

    // Fallback to analyzing the binary format
    match binary_metadata.binary_type {
        crate::binary_analyzer::BinaryType::Cubin => "sm_75".to_string(), // Default to Turing
        crate::binary_analyzer::BinaryType::PTX => "ptx".to_string(),
        _ => "unknown".to_string(),
    }
}

/// Detect optimization level from binary metadata
fn detect_optimization_level(binary_metadata: &BinaryMetadata) -> String {
    // Check for optimization indicators in the metadata
    if let Some(opt_level) = binary_metadata.additional_metadata.get("optimization_level") {
        if let Some(opt_str) = opt_level.as_str() {
            return opt_str.to_string();
        }
    }

    // Heuristic: check for debug symbols
    if binary_metadata.additional_metadata.get("debug_info").is_some() {
        "O0".to_string() // Debug build
    } else {
        "O2".to_string() // Assume optimized
    }
}

/// Check if binary has debug information
fn has_debug_information(binary_metadata: &BinaryMetadata) -> bool {
    binary_metadata.additional_metadata.get("debug_info").is_some() ||
    binary_metadata.additional_metadata.get("dwarf_info").is_some()
}

/// Check if binary has line information
fn has_line_information(binary_metadata: &BinaryMetadata) -> bool {
    binary_metadata.additional_metadata.get("line_info").is_some() ||
    binary_metadata.additional_metadata.get("debug_info")
        .and_then(|v| v.as_bool())
        .unwrap_or(false)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::binary_analyzer::{BinaryMetadata, BinaryType, ExportedFunction, FunctionSignature, TypeInfo, CallingConvention};

    fn create_test_binary_metadata() -> BinaryMetadata {
        BinaryMetadata {
            binary_type: BinaryType::PTX,
            path: "test.bin".to_string(),
            exports: vec![
                ExportedFunction {
                    name: "test_kernel".to_string(),
                    address: 0x1000,
                    signature: Some(FunctionSignature {
                        return_type: TypeInfo {
                            name: "void".to_string(),
                            size: None,
                            alignment: None,
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: vec![],
                        },
                        parameter_types: vec![
                            TypeInfo {
                                name: "float*".to_string(),
                                size: Some(8),
                                alignment: Some(8),
                                is_pointer: true,
                                is_array: false,
                                array_dimensions: vec![],
                            },
                            TypeInfo {
                                name: "int".to_string(),
                                size: Some(4),
                                alignment: Some(4),
                                is_pointer: false,
                                is_array: false,
                                array_dimensions: vec![],
                            },
                        ],
                        is_variadic: false,
                    }),
                    calling_convention: Some(CallingConvention::CudaKernel),
                    metadata: serde_json::json!({
                        "shared_memory": 512
                    }),
                },
            ],
            imports: vec![],
            dependencies: vec![],
            additional_metadata: serde_json::Value::Null,
        }
    }

    #[test]
    fn test_metadata_extractor_default() {
        let extractor = MetadataExtractor::default();

        // Verify default options
        assert!(extractor.options.extract_memory_layouts);
        assert!(extractor.options.extract_launch_parameters);
        assert!(extractor.options.extract_function_signatures);
        assert!(extractor.options.extract_type_info);
    }

    #[test]
    fn test_metadata_extractor_builder() {
        let options = MetadataExtractionOptions {
            extract_memory_layouts: false,
            extract_launch_parameters: true,
            extract_function_signatures: true,
            extract_type_info: false,
        };

        let extractor = MetadataExtractor::builder()
            .with_options(options.clone())
            .build();

        assert_eq!(extractor.options.extract_memory_layouts, false);
        assert_eq!(extractor.options.extract_launch_parameters, true);
        assert_eq!(extractor.options.extract_function_signatures, true);
        assert_eq!(extractor.options.extract_type_info, false);
    }

    #[test]
    fn test_apply_extraction_options_disable_signatures() {
        let options = MetadataExtractionOptions {
            extract_memory_layouts: true,
            extract_launch_parameters: true,
            extract_function_signatures: false,
            extract_type_info: true,
        };

        let extractor = MetadataExtractor::new(options);
        let binary_metadata = create_test_binary_metadata();

        let mut metadata = ExtractedMetadata {
            binary_metadata,
            functions: vec![
                ExtractedFunction {
                    name: "test_func".to_string(),
                    signature: Some(ExtractedSignature {
                        return_type: ExtractedType {
                            name: "void".to_string(),
                            size: None,
                            alignment: None,
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: vec![],
                            is_const: false,
                            is_volatile: false,
                        },
                        parameter_types: vec![],
                        is_variadic: false,
                        calling_convention: "C".to_string(),
                    }),
                    function_type: FunctionType::Host,
                    launch_params: None,
                    memory_layout: None,
                    metadata: serde_json::Value::Null,
                }
            ],
            types: vec![],
            additional_metadata: serde_json::Value::Null,
        };

        extractor.apply_extraction_options(&mut metadata).unwrap();

        // Signature should be removed
        assert!(metadata.functions[0].signature.is_none());
    }

    #[test]
    fn test_apply_extraction_options_disable_launch_params() {
        let options = MetadataExtractionOptions {
            extract_memory_layouts: true,
            extract_launch_parameters: false,
            extract_function_signatures: true,
            extract_type_info: true,
        };

        let extractor = MetadataExtractor::new(options);
        let binary_metadata = create_test_binary_metadata();

        let mut metadata = ExtractedMetadata {
            binary_metadata,
            functions: vec![
                ExtractedFunction {
                    name: "test_kernel".to_string(),
                    signature: None,
                    function_type: FunctionType::Kernel,
                    launch_params: Some(LaunchParameters {
                        grid_dim: [1, 1, 1],
                        block_dim: [256, 1, 1],
                        shared_mem_bytes: 512,
                        dynamic_shared_mem: false,
                    }),
                    memory_layout: None,
                    metadata: serde_json::Value::Null,
                }
            ],
            types: vec![],
            additional_metadata: serde_json::Value::Null,
        };

        extractor.apply_extraction_options(&mut metadata).unwrap();

        // Launch params should be removed
        assert!(metadata.functions[0].launch_params.is_none());
    }

    #[test]
    fn test_apply_extraction_options_disable_types() {
        let options = MetadataExtractionOptions {
            extract_memory_layouts: true,
            extract_launch_parameters: true,
            extract_function_signatures: true,
            extract_type_info: false,
        };

        let extractor = MetadataExtractor::new(options);
        let binary_metadata = create_test_binary_metadata();

        let mut metadata = ExtractedMetadata {
            binary_metadata,
            functions: vec![],
            types: vec![
                ExtractedType {
                    name: "TestType".to_string(),
                    size: Some(4),
                    alignment: Some(4),
                    is_pointer: false,
                    is_array: false,
                    array_dimensions: vec![],
                    is_const: false,
                    is_volatile: false,
                }
            ],
            additional_metadata: serde_json::Value::Null,
        };

        extractor.apply_extraction_options(&mut metadata).unwrap();

        // Types should be cleared
        assert!(metadata.types.is_empty());
    }

    #[test]
    fn test_validate_extracted_signatures_success() {
        let extractor = MetadataExtractor::default();
        let binary_metadata = create_test_binary_metadata();

        let metadata = ExtractedMetadata {
            binary_metadata,
            functions: vec![
                ExtractedFunction {
                    name: "test_func".to_string(),
                    signature: Some(ExtractedSignature {
                        return_type: ExtractedType {
                            name: "void".to_string(),
                            size: None,
                            alignment: None,
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: vec![],
                            is_const: false,
                            is_volatile: false,
                        },
                        parameter_types: vec![
                            ExtractedParameter {
                                name: "param0".to_string(),
                                type_info: ExtractedType {
                                    name: "int".to_string(),
                                    size: Some(4),
                                    alignment: Some(4),
                                    is_pointer: false,
                                    is_array: false,
                                    array_dimensions: vec![],
                                    is_const: false,
                                    is_volatile: false,
                                },
                                attributes: vec![],
                            }
                        ],
                        is_variadic: false,
                        calling_convention: "C".to_string(),
                    }),
                    function_type: FunctionType::Host,
                    launch_params: None,
                    memory_layout: None,
                    metadata: serde_json::Value::Null,
                }
            ],
            types: vec![],
            additional_metadata: serde_json::Value::Null,
        };

        // Should succeed with valid signature
        assert!(extractor.validate_extracted_signatures(&metadata).is_ok());
    }

    #[test]
    fn test_validate_extracted_signatures_failure() {
        let extractor = MetadataExtractor::default();
        let binary_metadata = create_test_binary_metadata();

        let metadata = ExtractedMetadata {
            binary_metadata,
            functions: vec![
                ExtractedFunction {
                    name: "test_func".to_string(),
                    signature: Some(ExtractedSignature {
                        return_type: ExtractedType {
                            name: "invalid_type".to_string(),
                            size: None, // This will cause validation to fail
                            alignment: None,
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: vec![],
                            is_const: false,
                            is_volatile: false,
                        },
                        parameter_types: vec![],
                        is_variadic: false,
                        calling_convention: "C".to_string(),
                    }),
                    function_type: FunctionType::Host,
                    launch_params: None,
                    memory_layout: None,
                    metadata: serde_json::Value::Null,
                }
            ],
            types: vec![],
            additional_metadata: serde_json::Value::Null,
        };

        // Should fail with invalid signature
        assert!(extractor.validate_extracted_signatures(&metadata).is_err());
    }

    #[test]
    fn test_convert_export_to_extracted_function() {
        let extractor = MetadataExtractor::default();

        let export = ExportedFunction {
            name: "test_kernel".to_string(),
            address: 0x1000,
            signature: Some(FunctionSignature {
                return_type: TypeInfo {
                    name: "void".to_string(),
                    size: None,
                    alignment: None,
                    is_pointer: false,
                    is_array: false,
                    array_dimensions: vec![],
                },
                parameter_types: vec![
                    TypeInfo {
                        name: "float*".to_string(),
                        size: Some(8),
                        alignment: Some(8),
                        is_pointer: true,
                        is_array: false,
                        array_dimensions: vec![],
                    },
                ],
                is_variadic: false,
            }),
            calling_convention: Some(CallingConvention::CudaKernel),
            metadata: serde_json::json!({
                "shared_memory": 512
            }),
        };

        let extracted = extractor.convert_export_to_extracted_function(&export);

        assert_eq!(extracted.name, "test_kernel");
        assert_eq!(extracted.function_type, FunctionType::Kernel);
        assert!(extracted.signature.is_some());
        assert!(extracted.launch_params.is_some());

        let launch_params = extracted.launch_params.unwrap();
        assert_eq!(launch_params.shared_mem_bytes, 512);
    }
}



Directory: metadata_extractor
File: ptx_metadata.rs
=====================
// src/metadata_extractor/ptx_metadata.rs
//! PTX metadata extractor for the OmniForge compiler.
//!
//! This module provides functionality for extracting metadata from
//! NVIDIA PTX files.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs;
use regex::Regex;

use crate::error::{OmniError, OmniResult};
use crate::binary_analyzer::BinaryMetadata;
use super::{ExtractedMetadata, ExtractedFunction, ExtractedType, ExtractedSignature, ExtractedParameter, FunctionType, LaunchParameters};

/// PTX metadata extractor
pub struct PTXMetadataExtractor {
    // Configuration options can be added here
}

impl Default for PTXMetadataExtractor {
    fn default() -> Self {
        Self::new()
    }
}

impl PTXMetadataExtractor {
    /// Create a new PTX metadata extractor
    pub fn new() -> Self {
        Self {}
    }
    
    /// Extract metadata from a PTX file
    pub fn extract_metadata(&self, path: &Path, binary_metadata: BinaryMetadata) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting PTX metadata from: {}", path.display());
        
        // Read the PTX file
        let ptx_content = fs::read_to_string(path)?;
        
        // Extract functions
        let functions = self.extract_functions(&ptx_content, &binary_metadata)?;
        
        // Extract types
        let types = self.extract_types(&ptx_content)?;
        
        // Extract additional metadata
        let additional_metadata = self.extract_additional_metadata(&ptx_content)?;
        
        Ok(ExtractedMetadata {
            binary_metadata,
            functions,
            types,
            additional_metadata,
        })
    }
    
    /// Extract functions from the PTX content
    fn extract_functions(&self, ptx_content: &str, binary_metadata: &BinaryMetadata) -> OmniResult<Vec<ExtractedFunction>> {
        let mut functions = Vec::new();
        
        // Process each exported function from the binary metadata
        for export in &binary_metadata.exports {
            // Find the function in the PTX content
            let function_regex = Regex::new(&format!(r"\.(entry|func)\s+{}[\s\S]*?(?=\.entry|\.func|\Z)", regex::escape(&export.name)))
                .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
            
            if let Some(function_match) = function_regex.find(ptx_content) {
                let function_content = function_match.as_str();
                
                // Determine function type
                let function_type = if function_content.starts_with(".entry") {
                    FunctionType::Kernel
                } else {
                    FunctionType::Device
                };
                
                // Extract parameter information
                let params_regex = Regex::new(r"\((.*?)\)")
                    .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
                
                let signature = if let Some(params_match) = params_regex.captures(function_content) {
                    let params_str = params_match.get(1).unwrap().as_str();
                    
                    // Parse parameters
                    let parameter_types = self.parse_parameters(params_str)?;
                    
                    Some(ExtractedSignature {
                        return_type: ExtractedType {
                            name: "void".to_string(),
                            size: Some(0),
                            alignment: Some(1),
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: Vec::new(),
                            is_const: false,
                            is_volatile: false,
                        },
                        parameter_types,
                        is_variadic: false,
                        calling_convention: if function_type == FunctionType::Kernel {
                            "cudaKernel".to_string()
                        } else {
                            "cudaDevice".to_string()
                        },
                    })
                } else {
                    None
                };
                
                // Extract launch parameters for kernels
                let launch_params = if function_type == FunctionType::Kernel {
                    // Extract shared memory usage
                    let shared_mem = self.extract_shared_memory(function_content);
                    
                    // Determine optimal launch configuration
                    Some(self.determine_launch_config(function_content, shared_mem))
                } else {
                    None
                };
                
                // Extract register usage
                let register_count = self.extract_register_count(function_content);
                
                functions.push(ExtractedFunction {
                    name: export.name.clone(),
                    signature,
                    function_type,
                    launch_params,
                    memory_layout: None,
                    metadata: serde_json::json!({
                        "register_count": register_count,
                        "is_kernel": function_type == FunctionType::Kernel,
                    }),
                });
            }
        }
        
        Ok(functions)
    }
    
    /// Parse parameters from the parameter string
    fn parse_parameters(&self, params_str: &str) -> OmniResult<Vec<ExtractedParameter>> {
        let mut parameters = Vec::new();
        
        if params_str.trim().is_empty() {
            return Ok(parameters);
        }
        
        for param in params_str.split(',') {
            let param = param.trim();
            
            // Parse parameter format: .param .type .ptr .align X .space .param_space param_name
            let param_regex = Regex::new(r"\.param\s+(\.\w+)(?:\s+\.ptr)?(?:\s+\.align\s+(\d+))?(?:\s+\.space\s+\.(\w+))?\s+(\w+)")
                .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
            
            if let Some(captures) = param_regex.captures(param) {
                let type_name = captures.get(1).unwrap().as_str().to_string();
                let alignment = captures.get(2).map(|m| m.as_str().parse::<usize>().unwrap_or(0));
                let space = captures.get(3).map(|m| m.as_str().to_string());
                let param_name = captures.get(4).unwrap().as_str().to_string();
                
                let mut attributes = Vec::new();
                if let Some(space) = space {
                    attributes.push(format!("space:{space}"));
                }
                
                parameters.push(ExtractedParameter {
                    name: param_name,
                    type_info: ExtractedType {
                        name: type_name,
                        size: None, // Cannot determine size from PTX alone
                        alignment,
                        is_pointer: param.contains(".ptr"),
                        is_array: false,
                        array_dimensions: Vec::new(),
                        is_const: false,
                        is_volatile: false,
                    },
                    attributes,
                });
            }
        }
        
        Ok(parameters)
    }
    
    /// Extract shared memory usage from the function content
    fn extract_shared_memory(&self, function_content: &str) -> usize {
        let shared_mem_regex = Regex::new(r"\.shared\s+\.align\s+\d+\s+\.b8\s+\w+\[(\d+)\]").ok();
        
        let mut total_shared_mem = 0;
        if let Some(regex) = shared_mem_regex {
            for captures in regex.captures_iter(function_content) {
                if let Some(size_match) = captures.get(1) {
                    if let Ok(size) = size_match.as_str().parse::<usize>() {
                        total_shared_mem += size;
                    }
                }
            }
        }
        
        total_shared_mem
    }
    
    /// Extract register count from the function content
    fn extract_register_count(&self, function_content: &str) -> Option<usize> {
        let reg_regex = Regex::new(r"// Function requires\s+(\d+)\s+registers").ok()?;
        
        if let Some(captures) = reg_regex.captures(function_content) {
            if let Some(count_match) = captures.get(1) {
                return count_match.as_str().parse::<usize>().ok();
            }
        }
        
        None
    }
    
    /// Determine optimal launch configuration based on function analysis
    fn determine_launch_config(&self, function_content: &str, shared_mem: usize) -> LaunchParameters {
        // This is a simplified implementation
        // In a real implementation, we would analyze the kernel code to determine
        // optimal grid and block dimensions
        
        // Estimate block size based on register usage
        let block_size = if let Some(reg_count) = self.extract_register_count(function_content) {
            if reg_count > 64 {
                // High register usage: smaller blocks
                128
            } else if reg_count > 32 {
                // Medium register usage: medium blocks
                256
            } else {
                // Low register usage: larger blocks
                512
            }
        } else {
            // Default
            256
        };
        
        LaunchParameters {
            grid_dim: [1, 1, 1], // Default
            block_dim: [block_size, 1, 1],
            shared_mem_bytes: shared_mem,
            dynamic_shared_mem: false,
        }
    }
    
    /// Extract types from the PTX content
    fn extract_types(&self, _ptx_content: &str) -> OmniResult<Vec<ExtractedType>> {
        // PTX doesn't have explicit type definitions, but we can extract basic types used
        
        let mut types = Vec::new();
        
        // Define standard PTX types
        let standard_types = [
            (".u8", 1), (".u16", 2), (".u32", 4), (".u64", 8),
            (".s8", 1), (".s16", 2), (".s32", 4), (".s64", 8),
            (".f16", 2), (".f32", 4), (".f64", 8),
            (".b8", 1), (".b16", 2), (".b32", 4), (".b64", 8),
            (".pred", 1),
        ];
        
        for (type_name, size) in standard_types.iter() {
            types.push(ExtractedType {
                name: type_name.to_string(),
                size: Some(*size),
                alignment: Some(*size),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            });
        }
        
        // Also add pointer versions
        for (type_name, _size) in standard_types.iter() {
            types.push(ExtractedType {
                name: format!("{type_name}_ptr"),
                size: Some(8), // 64-bit pointers
                alignment: Some(8),
                is_pointer: true,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            });
        }
        
        Ok(types)
    }
    
    /// Extract additional metadata from the PTX content
    fn extract_additional_metadata(&self, ptx_content: &str) -> OmniResult<serde_json::Value> {
        // Extract PTX version
        let version_regex = Regex::new(r"\.version\s+(\d+)\.(\d+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let version = if let Some(captures) = version_regex.captures(ptx_content) {
            let major = captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0);
            let minor = captures.get(2).unwrap().as_str().parse::<u32>().unwrap_or(0);
            format!("{major}.{minor}")
        } else {
            "unknown".to_string()
        };
        
        // Extract target architecture
        let target_regex = Regex::new(r"\.target\s+([\w\.]+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let target = if let Some(captures) = target_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().to_string()
        } else {
            "unknown".to_string()
        };
        
        // Extract addressing mode
        let address_size_regex = Regex::new(r"\.address_size\s+(\d+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let address_size = if let Some(captures) = address_size_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0)
        } else {
            0
        };
        
        Ok(serde_json::json!({
            "ptx_version": version,
            "target_architecture": target,
            "address_size": address_size,
            "num_kernels": ptx_content.matches(".entry").count(),
            "num_device_functions": ptx_content.matches(".func").count() - ptx_content.matches(".entry").count(), // Adjust for false positives
        }))
    }
}


