#include <immintrin.h>
#include <chrono>
#include <iostream>
#include <vector>

const size_t N = 1024 * 1024;

void ultra_aggressive_avx2_kernel(float* data, size_t n) {
    for (size_t i = 0; i < n; i += 64) {
        // Load 8 AVX2 vectors (64 floats)
        __m256 v0 = _mm256_load_ps(&data[i]);
        __m256 v1 = _mm256_load_ps(&data[i + 8]);
        __m256 v2 = _mm256_load_ps(&data[i + 16]);
        __m256 v3 = _mm256_load_ps(&data[i + 24]);
        __m256 v4 = _mm256_load_ps(&data[i + 32]);
        __m256 v5 = _mm256_load_ps(&data[i + 40]);
        __m256 v6 = _mm256_load_ps(&data[i + 48]);
        __m256 v7 = _mm256_load_ps(&data[i + 56]);
        
        // Create constants for different computational paths
        const __m256 c1 = _mm256_set1_ps(1.0001f);
        const __m256 c2 = _mm256_set1_ps(0.9999f);
        const __m256 c3 = _mm256_set1_ps(1.0002f);
        const __m256 c4 = _mm256_set1_ps(0.9998f);
        
        // Extremely aggressive computation loop
        for (int iter = 0; iter < 200; ++iter) {
            // 8 parallel FMA chains
            v0 = _mm256_fmadd_ps(v0, v1, c1);
            v1 = _mm256_fmadd_ps(v1, v2, c2);
            v2 = _mm256_fmadd_ps(v2, v3, c3);
            v3 = _mm256_fmadd_ps(v3, v4, c4);
            v4 = _mm256_fmadd_ps(v4, v5, c1);
            v5 = _mm256_fmadd_ps(v5, v6, c2);
            v6 = _mm256_fmadd_ps(v6, v7, c3);
            v7 = _mm256_fmadd_ps(v7, v0, c4);
            
            // Additional cross-register operations
            v0 = _mm256_fmadd_ps(v0, v4, c2);
            v1 = _mm256_fmadd_ps(v1, v5, c1);
            v2 = _mm256_fmadd_ps(v2, v6, c4);
            v3 = _mm256_fmadd_ps(v3, v7, c3);
            v4 = _mm256_fmadd_ps(v4, v0, c3);
            v5 = _mm256_fmadd_ps(v5, v1, c4);
            v6 = _mm256_fmadd_ps(v6, v2, c1);
            v7 = _mm256_fmadd_ps(v7, v3, c2);
        }
        
        _mm256_store_ps(&data[i], v0);
        _mm256_store_ps(&data[i + 8], v1);
        _mm256_store_ps(&data[i + 16], v2);
        _mm256_store_ps(&data[i + 24], v3);
        _mm256_store_ps(&data[i + 32], v4);
        _mm256_store_ps(&data[i + 40], v5);
        _mm256_store_ps(&data[i + 48], v6);
        _mm256_store_ps(&data[i + 56], v7);
    }
}

int main() {
    alignas(32) std::vector<float> a(N);
    
    for (size_t i = 0; i < N; ++i) {
        a[i] = i * 0.1f;
    }

    auto start = std::chrono::high_resolution_clock::now();
    
    for (int iter = 0; iter < 1000; ++iter) {
        ultra_aggressive_avx2_kernel(a.data(), N);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double>(end - start).count();
    
    // 16 FMA ops per inner iteration * 200 inner iterations = 3200 FLOPs per element per outer iteration
    double ops = 1000.0 * N * 3200.0;
    double gflops = (ops / duration) / 1e9;
    
    std::cout << "AVX2 ULTRA AGGRESSIVE: " << gflops << " GFLOPS" << std::endl;
    return 0;
}
