// src/codegen/cpp_codegen.rs
//! C++ code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating C++ code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables and modern C++ features.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// C++ code generator
pub struct CppCodeGenerator {
    // Configuration options can be added here
}

impl Default for CppCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl CppCodeGenerator {
    /// Create a new C++ code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate C++ type from argument type
    fn generate_cpp_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "void".to_string(),
            super::ArgType::I8 => "std::int8_t".to_string(),
            super::ArgType::I16 => "std::int16_t".to_string(),
            super::ArgType::I32 => "std::int32_t".to_string(),
            super::ArgType::I64 => "std::int64_t".to_string(),
            super::ArgType::U8 => "std::uint8_t".to_string(),
            super::ArgType::U16 => "std::uint16_t".to_string(),
            super::ArgType::U32 => "std::uint32_t".to_string(),
            super::ArgType::U64 => "std::uint64_t".to_string(),
            super::ArgType::F32 => "float".to_string(),
            super::ArgType::F64 => "double".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "std::int8_t*".to_string(),
            super::ArgType::I16Ptr => "std::int16_t*".to_string(),
            super::ArgType::I32Ptr => "std::int32_t*".to_string(),
            super::ArgType::I64Ptr => "std::int64_t*".to_string(),
            super::ArgType::U8Ptr => "std::uint8_t*".to_string(),
            super::ArgType::U16Ptr => "std::uint16_t*".to_string(),
            super::ArgType::U32Ptr => "std::uint32_t*".to_string(),
            super::ArgType::U64Ptr => "std::uint64_t*".to_string(),
            super::ArgType::F32Ptr => "float*".to_string(),
            super::ArgType::F64Ptr => "double*".to_string(),
            super::ArgType::BoolPtr => "bool*".to_string(),
            super::ArgType::VoidPtr => "void*".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate C++ function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_cpp_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cpp_type = self.generate_cpp_type(&arg_type);
                        format!("{cpp_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        Ok(format!("{} {}({})", return_type, function.name, params))
    }
    
    /// Generate function declarations
    fn generate_function_declarations(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        for function in functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration
            result.push_str(&format!("extern \"C\" {signature};\n"));
        }
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cpp_type = self.generate_cpp_type(&arg_type);
                        format!("{cpp_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("{{{}, {}, {}}}", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("{{{}, {}, {}}}", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "{1, 1, 1}".to_string(),
                "{256, 1, 1}".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch the CUDA kernel `{function_name}`
 */
void launch_{function_name}({params}) {{
    extern "C" void {function_name}({params});
    
    // Launch parameters
    constexpr std::array<std::uint32_t, 3> grid_dim = {grid_dim};
    constexpr std::array<std::uint32_t, 3> block_dim = {block_dim};
    constexpr std::size_t shared_mem = {shared_mem};
    
    // Advanced CUDA kernel launch implementation with comprehensive error handling
    #ifdef __CUDACC__
    // Validate launch configuration before execution
    {{
        int device_id;
        cudaError_t device_err = cudaGetDevice(&device_id);
        if (device_err != cudaSuccess) {{
            throw std::runtime_error(
                std::string("Failed to get CUDA device: ") + cudaGetErrorString(device_err)
            );
        }}

        cudaDeviceProp device_props;
        cudaError_t props_err = cudaGetDeviceProperties(&device_props, device_id);
        if (props_err != cudaSuccess) {{
            throw std::runtime_error(
                std::string("Failed to get device properties: ") + cudaGetErrorString(props_err)
            );
        }}

        // Validate grid dimensions
        if (grid_dim[0] > device_props.maxGridSize[0] || 
            grid_dim[1] > device_props.maxGridSize[1] || 
            grid_dim[2] > device_props.maxGridSize[2]) {{
            throw std::runtime_error(
                "Grid dimensions exceed device limits"
            );
        }}

        // Validate block dimensions
        if (block_dim[0] > device_props.maxThreadsDim[0] || 
            block_dim[1] > device_props.maxThreadsDim[1] || 
            block_dim[2] > device_props.maxThreadsDim[2]) {{
            throw std::runtime_error(
                "Block dimensions exceed device limits"
            );
        }}

        // Validate total threads per block
        std::size_t total_threads = block_dim[0] * block_dim[1] * block_dim[2];
        if (total_threads > device_props.maxThreadsPerBlock) {{
            throw std::runtime_error(
                "Total threads per block exceed device limit"
            );
        }}

        // Validate shared memory usage
        if (shared_mem > device_props.sharedMemPerBlock) {{
            throw std::runtime_error(
                "Shared memory usage exceeds device limit"
            );
        }}
    }}

    // Create CUDA events for timing and synchronization
    cudaEvent_t start_event, stop_event;
    cudaEventCreate(&start_event);
    cudaEventCreate(&stop_event);

    // Record start time
    cudaEventRecord(start_event);

    // Execute kernel with comprehensive error checking
    {function_name}<<<dim3(grid_dim[0], grid_dim[1], grid_dim[2]),
                      dim3(block_dim[0], block_dim[1], block_dim[2]),
                      shared_mem,
                      0>>>({args});

    // Record stop time
    cudaEventRecord(stop_event);

    // Check for launch errors immediately
    cudaError_t launch_err = cudaGetLastError();
    if (launch_err != cudaSuccess) {{
        cudaEventDestroy(start_event);
        cudaEventDestroy(stop_event);
        throw std::runtime_error(
            std::string("CUDA kernel launch failed for {function_name}: ") + 
            cudaGetErrorString(launch_err)
        );
    }}

    // Synchronize and check for execution errors
    cudaError_t sync_err = cudaDeviceSynchronize();
    if (sync_err != cudaSuccess) {{
        cudaEventDestroy(start_event);
        cudaEventDestroy(stop_event);
        throw std::runtime_error(
            std::string("CUDA kernel execution failed for {function_name}: ") + 
            cudaGetErrorString(sync_err)
        );
    }}

    // Calculate and log execution time
    cudaEventSynchronize(stop_event);
    float execution_time_ms;
    cudaEventElapsedTime(&execution_time_ms, start_event, stop_event);
    
    #ifdef OMNI_DEBUG
    std::cout << "Kernel {function_name} executed successfully in " 
              << execution_time_ms << " ms" << std::endl;
    #endif

    // Cleanup events
    cudaEventDestroy(start_event);
    cudaEventDestroy(stop_event);

    #else
    // Advanced fallback using CUDA runtime API
    {{
        // Initialize CUDA runtime if not already done
        static bool runtime_initialized = false;
        if (!runtime_initialized) {{
            int device_count;
            cudaError_t count_err = cudaGetDeviceCount(&device_count);
            if (count_err != cudaSuccess || device_count == 0) {{
                throw std::runtime_error(
                    "No CUDA devices available or CUDA runtime not initialized"
                );
            }}
            runtime_initialized = true;
        }}

        // Create kernel launch configuration
        const dim3 grid_config(grid_dim[0], grid_dim[1], grid_dim[2]);
        const dim3 block_config(block_dim[0], block_dim[1], block_dim[2]);

        // Prepare argument array for runtime launch
        std::vector<void*> kernel_args = {{{args}}};

        // Launch using cudaLaunchKernel
        cudaError_t runtime_err = cudaLaunchKernel(
            reinterpret_cast<const void*>({function_name}),
            grid_config,
            block_config,
            kernel_args.data(),
            shared_mem,
            nullptr  // Default stream
        );

        if (runtime_err != cudaSuccess) {{
            throw std::runtime_error(
                std::string("CUDA runtime kernel launch failed for {function_name}: ") + 
                cudaGetErrorString(runtime_err)
            );
        }}

        // Synchronize and verify execution
        cudaError_t sync_err = cudaDeviceSynchronize();
        if (sync_err != cudaSuccess) {{
            throw std::runtime_error(
                std::string("CUDA runtime kernel execution failed for {function_name}: ") + 
                cudaGetErrorString(sync_err)
            );
        }}
    }}
    #endif
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("reinterpret_cast<void*>(&arg{i})"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/**
 * Argument type enumeration
 */
enum class OmniArgType {
    Void,
    I8,
    I16,
    I32,
    I64,
    U8,
    U16,
    U32,
    U64,
    F32,
    F64,
    Bool,
    I8Ptr,
    I16Ptr,
    I32Ptr,
    I64Ptr,
    U8Ptr,
    U16Ptr,
    U32Ptr,
    U64Ptr,
    F32Ptr,
    F64Ptr,
    BoolPtr,
    VoidPtr
};

/**
 * Target type enumeration
 */
enum class OmniTargetType {
    CPU,
    GPU,
    CPUSIMD,
    TPU,
    FPGA,
    Other
};

/**
 * Compute metadata structure
 */
struct OmniComputeMetadata {
    std::array<std::uint32_t, 3> grid_size;
    std::array<std::uint32_t, 3> block_size;
    std::size_t shared_mem;
    std::vector<OmniArgType> args_layout;
    
    // Convenience constructors
    static OmniComputeMetadata cpu() {
        return {
            {1, 1, 1},
            {1, 1, 1},
            0,
            {}
        };
    }
    
    static OmniComputeMetadata cpu_simd() {
        return {
            {1, 1, 1},
            {1, 1, 1},
            0,
            {}
        };
    }
    
    static OmniComputeMetadata gpu(
        const std::array<std::uint32_t, 3>& grid_size,
        const std::array<std::uint32_t, 3>& block_size,
        std::size_t shared_mem,
        const std::vector<OmniArgType>& args_layout
    ) {
        return {
            grid_size,
            block_size,
            shared_mem,
            args_layout
        };
    }
};

/**
 * Function pointer type
 */
using OmniFunctionPtr = void(*)();

/**
 * OmniCodex entry structure
 */
struct OmniCodexEntry {
    std::string name;
    OmniTargetType target_type;
    OmniFunctionPtr function_ptr;
    OmniComputeMetadata metadata;
};
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> String {
        if entry.metadata.arg_layout.is_empty() {
            return "{}".to_string();
        }
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| match arg {
                super::ArgType::Void => "OmniArgType::Void",
                super::ArgType::I8 => "OmniArgType::I8",
                super::ArgType::I16 => "OmniArgType::I16",
                super::ArgType::I32 => "OmniArgType::I32",
                super::ArgType::I64 => "OmniArgType::I64",
                super::ArgType::U8 => "OmniArgType::U8",
                super::ArgType::U16 => "OmniArgType::U16",
                super::ArgType::U32 => "OmniArgType::U32",
                super::ArgType::U64 => "OmniArgType::U64",
                super::ArgType::F32 => "OmniArgType::F32",
                super::ArgType::F64 => "OmniArgType::F64",
                super::ArgType::Bool => "OmniArgType::Bool",
                super::ArgType::I8Ptr => "OmniArgType::I8Ptr",
                super::ArgType::I16Ptr => "OmniArgType::I16Ptr",
                super::ArgType::I32Ptr => "OmniArgType::I32Ptr",
                super::ArgType::I64Ptr => "OmniArgType::I64Ptr",
                super::ArgType::U8Ptr => "OmniArgType::U8Ptr",
                super::ArgType::U16Ptr => "OmniArgType::U16Ptr",
                super::ArgType::U32Ptr => "OmniArgType::U32Ptr",
                super::ArgType::U64Ptr => "OmniArgType::U64Ptr",
                super::ArgType::F32Ptr => "OmniArgType::F32Ptr",
                super::ArgType::F64Ptr => "OmniArgType::F64Ptr",
                super::ArgType::BoolPtr => "OmniArgType::BoolPtr",
                super::ArgType::VoidPtr => "OmniArgType::VoidPtr",
                super::ArgType::Custom(_) => "OmniArgType::VoidPtr", // Default to void* for custom types
            })
            .collect::<Vec<_>>()
            .join(", ");
        
        format!("{{{arg_types}}}")
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        // Generate table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("const std::vector<OmniCodexEntry> OMNI_CODEX = {\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_dim, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("{{{}, {}, {}}}", grid[0], grid[1], grid[2]),
                    format!("{{{}, {}, {}}}", block[0], block[1], block[2]),
                )
            } else {
                ("{1, 1, 1}".to_string(), "{256, 1, 1}".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = self.generate_arg_types(entry);
            
            // Generate target type
            let target_type = match entry.target_type {
                super::TargetType::CPU => "OmniTargetType::CPU",
                super::TargetType::GPU => "OmniTargetType::GPU",
                super::TargetType::CPUSIMD => "OmniTargetType::CPUSIMD",
                super::TargetType::TPU => "OmniTargetType::TPU",
                super::TargetType::FPGA => "OmniTargetType::FPGA",
                super::TargetType::Other => "OmniTargetType::Other",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    {{
        /* name */ "{}",
        /* target_type */ {},
        /* function_ptr */ reinterpret_cast<OmniFunctionPtr>(&{}),
        /* metadata */ {{
            /* grid_size */ {},
            /* block_size */ {},
            /* shared_mem */ {},
            /* args_layout */ {}
        }}
    }},
"#,
                entry.name,
                target_type,
                entry.function_pointer,
                grid_dim,
                block_size,
                shared_mem,
                args_layout,
            ));
        }
        
        result.push_str("};\n");
        
        result
    }
    
    /// Generate file header
    fn generate_file_header(&self) -> String {
        r#"/**
 * OmniCodex dispatch table generated by OmniForge.
 *
 * This file contains the OmniCodex dispatch table, which provides a zero-cost
 * abstraction for heterogeneous computing. It allows calling functions on
 * different backends (CPU, GPU, etc.) with a unified interface.
 */

#ifndef OMNICODEX_HPP
#define OMNICODEX_HPP

#include <cstdint>
#include <array>
#include <vector>
#include <string>
#include <stdexcept>
#include <optional>
#include <type_traits>
#include <functional>
#include <memory>
#include <variant>

namespace omni {

"#.to_string()
    }
    
    /// Generate file footer
    fn generate_file_footer(&self) -> String {
        r#"

} // namespace omni

#endif // OMNICODEX_HPP
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/**
 * Error codes for OmniCodex operations
 */
enum class OmniError {
    None,
    FunctionNotFound,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    NotImplemented
};

/**
 * OmniCodex exception
 */
class OmniException : public std::runtime_error {
public:
    OmniException(OmniError error, const std::string& message)
        : std::runtime_error(message), error_(error) {}
    
    OmniError error() const { return error_; }
    
private:
    OmniError error_;
};

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or nullptr if not found
 */
const OmniCodexEntry* find_function(const std::string& name) {
    for (const auto& entry : OMNI_CODEX) {
        if (entry.name == name) {
            return &entry;
        }
    }
    return nullptr;
}

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
template<typename T>
T execute(const std::string& name, const std::vector<void*>& args) {
    // Find the function in the dispatch table
    const OmniCodexEntry* entry = find_function(name);
    if (entry == nullptr) {
        throw OmniException(
            OmniError::FunctionNotFound,
            "Function not found: " + name
        );
    }
    
    // Check argument count
    if (args.size() != entry->metadata.args_layout.size()) {
        throw OmniException(
            OmniError::ArgumentCountMismatch,
            "Argument count mismatch: expected " + 
            std::to_string(entry->metadata.args_layout.size()) + 
            ", got " + std::to_string(args.size())
        );
    }
    
    // Advanced function execution implementation with comprehensive target handling
    try {{
        // Pre-execution validation and setup
        std::chrono::high_resolution_clock::time_point execution_start = 
            std::chrono::high_resolution_clock::now();

        switch (entry->target_type) {{
            case OmniTargetType::CPU: {{
                // CPU function execution with vectorization hints
                #ifdef __INTEL_COMPILER
                #pragma vector always
                #endif
                
                // Validate function pointer
                if (!entry->function_ptr) {{
                    throw OmniException(
                        OmniError::InvalidFunctionPointer,
                        "CPU function pointer is null for function: " + std::string(entry->name)
                    );
                }}

                // Set CPU affinity for optimal performance
                #ifdef _WIN32
                DWORD_PTR old_affinity = SetThreadAffinityMask(GetCurrentThread(), 1);
                #elif defined(__linux__)
                cpu_set_t cpuset;
                CPU_ZERO(&cpuset);
                CPU_SET(0, &cpuset);
                pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset);
                #endif

                // Execute with different calling conventions based on metadata
                const std::string& calling_conv = entry->metadata.calling_convention;
                if (calling_conv == "cdecl") {{
                    auto cpu_func = reinterpret_cast<T(*)(...)>(entry->function_ptr);
                    T result = cpu_func(args...);
                    return result;
                }} else if (calling_conv == "stdcall") {{
                    #ifdef _WIN32
                    auto cpu_func = reinterpret_cast<T(__stdcall*)(...)>(entry->function_ptr);
                    T result = cpu_func(args...);
                    return result;
                    #else
                    // Fallback to cdecl on non-Windows platforms
                    auto cpu_func = reinterpret_cast<T(*)(...)>(entry->function_ptr);
                    T result = cpu_func(args...);
                    return result;
                    #endif
                }} else {{
                    // Default calling convention
                    auto cpu_func = reinterpret_cast<T(*)(...)>(entry->function_ptr);
                    T result = cpu_func(args...);
                    return result;
                }}

                #ifdef _WIN32
                SetThreadAffinityMask(GetCurrentThread(), old_affinity);
                #endif
                break;
            }}

            case OmniTargetType::GPU: {{
                // Advanced GPU/CUDA function execution with memory management
                if (!entry->function_ptr) {{
                    throw OmniException(
                        OmniError::InvalidFunctionPointer,
                        "GPU function pointer is null for function: " + std::string(entry->name)
                    );
                }}

                // Validate CUDA context
                CUcontext current_context;
                CUresult context_result = cuCtxGetCurrent(&current_context);
                if (context_result != CUDA_SUCCESS || !current_context) {{
                    throw OmniException(
                        OmniError::CudaContextError,
                        "No valid CUDA context for GPU execution"
                    );
                }}

                // Handle memory transfer for GPU arguments
                std::vector<void*> device_args;
                std::vector<CUdeviceptr> device_ptrs;
                
                for (size_t i = 0; i < args.size(); ++i) {{
                    const auto& arg_type = entry->metadata.args_layout[i];
                    size_t arg_size = get_type_size(arg_type);
                    
                    // Allocate device memory
                    CUdeviceptr device_ptr;
                    CUresult alloc_result = cuMemAlloc(&device_ptr, arg_size);
                    if (alloc_result != CUDA_SUCCESS) {{
                        // Cleanup previously allocated memory
                        for (auto ptr : device_ptrs) {{
                            cuMemFree(ptr);
                        }}
                        throw OmniException(
                            OmniError::CudaMemoryError,
                            "Failed to allocate device memory for argument " + std::to_string(i)
                        );
                    }}
                    
                    device_ptrs.push_back(device_ptr);
                    
                    // Copy data to device if it's an input argument
                    if (is_input_argument(arg_type)) {{
                        CUresult copy_result = cuMemcpyHtoD(device_ptr, args[i], arg_size);
                        if (copy_result != CUDA_SUCCESS) {{
                            // Cleanup memory
                            for (auto ptr : device_ptrs) {{
                                cuMemFree(ptr);
                            }}
                            throw OmniException(
                                OmniError::CudaMemoryError,
                                "Failed to copy argument " + std::to_string(i) + " to device"
                            );
                        }}
                    }}
                    
                    device_args.push_back(reinterpret_cast<void*>(device_ptr));
                }}

                // Launch kernel using driver API for maximum control
                CUfunction kernel_func = reinterpret_cast<CUfunction>(entry->function_ptr);
                
                CUresult launch_result = cuLaunchKernel(
                    kernel_func,
                    entry->metadata.grid_size[0], entry->metadata.grid_size[1], entry->metadata.grid_size[2],
                    entry->metadata.block_size[0], entry->metadata.block_size[1], entry->metadata.block_size[2],
                    entry->metadata.shared_mem,
                    nullptr,  // Default stream
                    device_args.data(),
                    nullptr
                );

                if (launch_result != CUDA_SUCCESS) {{
                    // Cleanup memory
                    for (auto ptr : device_ptrs) {{
                        cuMemFree(ptr);
                    }}
                    throw OmniException(
                        OmniError::CudaLaunchError,
                        "CUDA kernel launch failed with error code: " + std::to_string(launch_result)
                    );
                }}

                // Synchronize execution
                CUresult sync_result = cuCtxSynchronize();
                if (sync_result != CUDA_SUCCESS) {{
                    // Cleanup memory
                    for (auto ptr : device_ptrs) {{
                        cuMemFree(ptr);
                    }}
                    throw OmniException(
                        OmniError::CudaExecutionError,
                        "CUDA kernel execution failed with error code: " + std::to_string(sync_result)
                    );
                }}

                // Copy results back to host
                T result{{}};
                for (size_t i = 0; i < args.size(); ++i) {{
                    const auto& arg_type = entry->metadata.args_layout[i];
                    if (is_output_argument(arg_type)) {{
                        size_t arg_size = get_type_size(arg_type);
                        CUresult copy_result = cuMemcpyDtoH(args[i], device_ptrs[i], arg_size);
                        if (copy_result != CUDA_SUCCESS) {{
                            // Log warning but don't fail the entire execution
                            std::cerr << "Warning: Failed to copy result " << i << " from device" << std::endl;
                        }}
                    }}
                }}

                // Cleanup device memory
                for (auto ptr : device_ptrs) {{
                    cuMemFree(ptr);
                }}

                return result;
            }}

            case OmniTargetType::CPUSIMD: {{
                // CPU SIMD execution with vectorization
                if (!entry->function_ptr) {{
                    throw OmniException(
                        OmniError::InvalidFunctionPointer,
                        "CPU SIMD function pointer is null for function: " + std::string(entry->name)
                    );
                }}

                // Check for SIMD support
                #ifdef __AVX2__
                if (!__builtin_cpu_supports("avx2")) {{
                    throw OmniException(
                        OmniError::UnsupportedSIMD,
                        "AVX2 required but not supported by CPU"
                    );
                }}
                #endif

                // Set optimal CPU state for SIMD
                #ifdef _WIN32
                _mm_setcsr(_mm_getcsr() | 0x8040); // Set FTZ and DAZ flags
                #endif

                auto simd_func = reinterpret_cast<T(*)(...)>(entry->function_ptr);
                T result = simd_func(args...);
                return result;
            }}

            case OmniTargetType::TPU: {{
                // TPU execution through vendor-specific API
                throw OmniException(
                    OmniError::NotImplemented,
                    "TPU execution not yet implemented"
                );
            }}

            case OmniTargetType::FPGA: {{
                // FPGA execution through OpenCL or vendor-specific API
                throw OmniException(
                    OmniError::NotImplemented,
                    "FPGA execution not yet implemented"
                );
            }}

            default: {{
                throw OmniException(
                    OmniError::UnsupportedTarget,
                    "Unsupported target type: " + std::to_string(static_cast<int>(entry->target_type))
                );
            }}
        }}

        // Calculate execution time
        auto execution_end = std::chrono::high_resolution_clock::now();
        auto execution_duration = std::chrono::duration_cast<std::chrono::microseconds>(
            execution_end - execution_start
        ).count();

        #ifdef OMNI_DEBUG
        std::cout << "Function " << entry->name << " executed in " 
                  << execution_duration << " microseconds" << std::endl;
        #endif

        // Return default-constructed result for void functions
        return T{{}};

    }} catch (const OmniException& e) {{
        throw; // Re-throw OmniExceptions as-is
    }} catch (const std::exception& e) {{
        throw OmniException(
            OmniError::ExecutionFailed,
            std::string("Function execution failed for ") + entry->name + ": " + e.what()
        );
    }} catch (...) {{
        throw OmniException(
            OmniError::UnknownError,
            std::string("Unknown error during execution of function: ") + entry->name
        );
    }}
}

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
std::string error_message(OmniError error) {
    switch (error) {
        case OmniError::None:
            return "No error";
        case OmniError::FunctionNotFound:
            return "Function not found";
        case OmniError::ArgumentCountMismatch:
            return "Argument count mismatch";
        case OmniError::ArgumentTypeMismatch:
            return "Argument type mismatch";
        case OmniError::NotImplemented:
            return "Not implemented";
        default:
            return "Unknown error";
    }
}

/**
 * CUDA runtime functions
 */
namespace cuda {
    /**
     * Launch a CUDA kernel
     *
     * @param kernel Kernel function pointer
     * @param grid_dim Grid dimensions
     * @param block_dim Block dimensions
     * @param shared_mem Shared memory size
     * @param args Kernel arguments
     */
    void launch_kernel(
        void* kernel,
        const std::array<std::uint32_t, 3>& grid_dim,
        const std::array<std::uint32_t, 3>& block_dim,
        std::size_t shared_mem,
        const std::vector<void*>& args
    ) {
        // Validate CUDA context
        CUcontext current_context;
        CUresult context_result = cuCtxGetCurrent(&current_context);
        if (context_result != CUDA_SUCCESS || !current_context) {
            throw OmniException(
                OmniError::CudaError,
                "No valid CUDA context available for kernel launch"
            );
        }

        // Validate grid and block dimensions
        if (grid_dim[0] == 0 || grid_dim[1] == 0 || grid_dim[2] == 0) {
            throw OmniException(
                OmniError::InvalidArgument,
                "Grid dimensions must be greater than 0"
            );
        }

        if (block_dim[0] == 0 || block_dim[1] == 0 || block_dim[2] == 0) {
            throw OmniException(
                OmniError::InvalidArgument,
                "Block dimensions must be greater than 0"
            );
        }

        // Check device limits
        int max_threads_per_block;
        cuDeviceGetAttribute(&max_threads_per_block, CU_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK, 0);

        size_t total_threads = block_dim[0] * block_dim[1] * block_dim[2];
        if (total_threads > static_cast<size_t>(max_threads_per_block)) {
            throw OmniException(
                OmniError::InvalidArgument,
                "Block size exceeds device maximum threads per block"
            );
        }

        // Launch the kernel
        CUresult launch_result = cuLaunchKernel(
            function,
            grid_dim[0], grid_dim[1], grid_dim[2],    // Grid dimensions
            block_dim[0], block_dim[1], block_dim[2], // Block dimensions
            shared_mem,                                // Shared memory size
            nullptr,                                   // Stream (default)
            const_cast<void**>(args.data()),          // Kernel arguments
            nullptr                                    // Extra parameters
        );

        if (launch_result != CUDA_SUCCESS) {
            const char* error_name;
            const char* error_string;
            cuGetErrorName(launch_result, &error_name);
            cuGetErrorString(launch_result, &error_string);

            throw OmniException(
                OmniError::CudaError,
                std::string("CUDA kernel launch failed: ") + error_name + " - " + error_string
            );
        }

        // Synchronize to ensure kernel completion
        CUresult sync_result = cuCtxSynchronize();
        if (sync_result != CUDA_SUCCESS) {
            const char* error_name;
            const char* error_string;
            cuGetErrorName(sync_result, &error_name);
            cuGetErrorString(sync_result, &error_string);

            throw OmniException(
                OmniError::CudaError,
                std::string("CUDA synchronization failed: ") + error_name + " - " + error_string
            );
        }
    }
} // namespace cuda
"#.to_string()
    }
    
    
    
    /// Generate header file
    fn generate_header(&self, _entries: &[CodexEntry], functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut header = String::new();
        
        // Generate file header
        header.push_str(&self.generate_file_header());
        header.push('\n');
        
        // Generate struct definitions
        header.push_str(&self.generate_codex_entry_struct());
        header.push('\n');
        
        // Generate forward declarations
        header.push_str(&self.generate_function_declarations(functions)?);
        header.push('\n');

        // Generate kernel launchers
        for function in functions {
            header.push_str(&self.generate_kernel_launcher(function)?);
        }
        header.push('\n');
        
        // Declare the OmniCodex table
        header.push_str("/**\n");
        header.push_str(" * OmniCodex dispatch table\n");
        header.push_str(" */\n");
        header.push_str("extern const std::vector<OmniCodexEntry> OMNI_CODEX;\n\n");
        
        // Generate helper function declarations
        header.push_str(r#"/**
 * Error codes for OmniCodex operations
 */
enum class OmniError {
    None,
    FunctionNotFound,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    NotImplemented
};

/**
 * OmniCodex exception
 */
class OmniException : public std::runtime_error {
public:
    OmniException(OmniError error, const std::string& message);
    OmniError error() const;
    
private:
    OmniError error_;
};

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or nullptr if not found
 */
const OmniCodexEntry* find_function(const std::string& name);

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
template<typename T>
T execute(const std::string& name, const std::vector<void*>& args);

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
std::string error_message(OmniError error);

/**
 * CUDA runtime functions
 */
namespace cuda {
    /**
     * Launch a CUDA kernel
     *
     * @param kernel Kernel function pointer
     * @param grid_dim Grid dimensions
     * @param block_dim Block dimensions
     * @param shared_mem Shared memory size
     * @param args Kernel arguments
     */
    void launch_kernel(
        void* kernel,
        const std::array<std::uint32_t, 3>& grid_dim,
        const std::array<std::uint32_t, 3>& block_dim,
        std::size_t shared_mem,
        const std::vector<void*>& args
    );
} // namespace cuda
"#);
        
        // Generate file footer
        header.push_str(&self.generate_file_footer());
        
        Ok(header)
    }
}

impl CodeGenerator for CppCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating C++ OmniCodex");
        
        // Generate header
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.hpp\"\n\n");
        
        // Add namespace
        code.push_str("namespace omni {\n\n");
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        // Close namespace
        code.push_str("\n} // namespace omni\n");
        
        let all_functions: Vec<_> = metadata.iter().flat_map(|m| &m.functions).cloned().collect();

        // Generate header file
        let header_code = self.generate_header(&entries, &all_functions)?;
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: Some(header_code),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating C++ wrappers");
        
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.hpp\"\n\n");
        
        // Add namespace
        code.push_str("namespace omni {\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let cpp_return_type = self.generate_cpp_type(&return_type);
                    
                    // Extract parameter types
                    let params = if signature.parameter_types.is_empty() {
                        "".to_string()
                    } else {
                        signature
                            .parameter_types
                            .iter()
                            .enumerate()
                            .map(|(i, param)| {
                                let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                                let cpp_type = self.generate_cpp_type(&arg_type);
                                format!("{cpp_type} arg{i}")
                            })
                            .collect::<Vec<_>>()
                            .join(", ")
                    };
                    
                    // Generate wrapper function
                    let func_name = function.name.to_lowercase();
                    
                    code.push_str(&format!(
                        r#"/**
 * Wrapper for the `{}` function
 *
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
{} {}({}) {{
    std::vector<void*> args;
    {}
    return execute<{}>("{}", args);
}}

"#,
                        function.name,
                        cpp_return_type,
                        func_name,
                        params,
                        if signature.parameter_types.is_empty() {
                            "// No arguments".to_string()
                        } else {
                            (0..signature.parameter_types.len())
                                .map(|i| format!("args.push_back(reinterpret_cast<void*>(&arg{i}));"))
                                .collect::<Vec<_>>()
                                .join("\n    ")
                        },
                        cpp_return_type,
                        function.name,
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        // Close namespace
        code.push_str("} // namespace omni\n");
        
        Ok(code)
    }
}
