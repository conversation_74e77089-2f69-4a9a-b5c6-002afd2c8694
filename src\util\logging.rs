// src/util/logging.rs
//! Logging utilities for the OmniForge compiler.
//!
//! This module provides logging and performance tracking functionality
//! to support debugging and optimization of the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::time::{Duration, Instant};
use std::fmt::Debug;
use std::sync::atomic::{AtomicUsize, Ordering};

/// Log a message with timing information
///
/// # Arguments
///
/// * `level` - Log level (e.g., "INFO", "DEBUG")
/// * `message` - Message to log
/// * `duration` - Duration to include in the log
///
/// # Examples
///
/// ```
/// use std::time::{Duration, Instant};
/// use omni_forge::util::logging::log_timing;
///
/// let start = Instant::now();
/// // Perform some operation
/// let duration = start.elapsed();
/// log_timing("INFO", "Operation completed", duration);
/// ```
pub fn log_timing(level: &str, message: &str, duration: Duration) {
    let duration_ms = duration.as_secs() * 1000 + duration.subsec_millis() as u64;
    
    if level == "ERROR" || level == "WARN" {
        eprintln!("[{level}] {message} (took {duration_ms} ms)");
    } else {
        println!("[{level}] {message} (took {duration_ms} ms)");
    }
}

/// Execute a function and log timing information
///
/// # Arguments
///
/// * `level` - Log level (e.g., "INFO", "DEBUG")
/// * `message` - Message to log
/// * `f` - Function to execute
///
/// # Returns
///
/// * The result of the function
///
/// # Examples
///
/// ```
/// use omni_forge::util::logging::log_with_timing;
///
/// let result = log_with_timing("INFO", "Calculating factorial", || {
///     // Calculate factorial
///     let mut result = 1;
///     for i in 1..10 {
///         result *= i;
///     }
///     result
/// });
/// ```
pub fn log_with_timing<F, R>(level: &str, message: &str, f: F) -> R
where
    F: FnOnce() -> R,
{
    let start = Instant::now();
    let result = f();
    let duration = start.elapsed();
    
    log_timing(level, message, duration);
    
    result
}

/// Benchmark a function
///
/// # Arguments
///
/// * `f` - Function to benchmark
/// * `iterations` - Number of iterations to run
///
/// # Returns
///
/// * `(R, Duration)` - The result of the function and the average duration per iteration
///
/// # Examples
///
/// ```
/// use omni_forge::util::logging::benchmark_fn;
///
/// let (result, avg_duration) = benchmark_fn(|| {
///     // Function to benchmark
///     let mut sum = 0;
///     for i in 0..1000 {
///         sum += i;
///     }
///     sum
/// }, 100); // Run 100 iterations
///
/// println!("Result: {}, Average duration: {:?}", result, avg_duration);
/// ```
pub fn benchmark_fn<F, R>(f: F, iterations: usize) -> (R, Duration)
where
    F: Fn() -> R,
    R: Clone,
{
    // Run once to get the result
    let result = f();
    
    // Run the remaining iterations
    let start = Instant::now();
    for _ in 1..iterations {
        let _ = f();
    }
    let total_duration = start.elapsed();
    
    let avg_duration = if iterations > 1 {
        total_duration / (iterations as u32 - 1)
    } else {
        total_duration
    };
    
    (result, avg_duration)
}

/// Performance counter for tracking function calls and durations
#[derive(Debug)]
pub struct PerformanceCounter {
    /// Counter name (for debugging and reporting)
    #[allow(dead_code)]
    name: String,
    /// Number of calls
    calls: AtomicUsize,
    /// Total duration
    total_duration: std::sync::Mutex<Duration>,
    /// Minimum duration
    min_duration: std::sync::Mutex<Option<Duration>>,
    /// Maximum duration
    max_duration: std::sync::Mutex<Option<Duration>>,
}

#[allow(dead_code)]
impl PerformanceCounter {
    /// Create a new performance counter
    ///
    /// # Arguments
    ///
    /// * `name` - Counter name
    ///
    /// # Returns
    ///
    /// * `PerformanceCounter` - The new counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// ```
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            calls: AtomicUsize::new(0),
            total_duration: std::sync::Mutex::new(Duration::from_secs(0)),
            min_duration: std::sync::Mutex::new(None),
            max_duration: std::sync::Mutex::new(None),
        }
    }
    
    /// Record a function call with the given duration
    ///
    /// # Arguments
    ///
    /// * `duration` - Duration of the function call
    ///
    /// # Examples
    ///
    /// ```
    /// use std::time::Instant;
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let start = Instant::now();
    /// // Perform some operation
    /// let duration = start.elapsed();
    /// counter.record(duration);
    /// ```
    pub fn record(&self, duration: Duration) {
        self.calls.fetch_add(1, Ordering::Relaxed);
        
        // Update total duration
        {
            let mut total_duration = self.total_duration.lock().unwrap();
            *total_duration += duration;
        }
        
        // Update min duration
        {
            let mut min_duration = self.min_duration.lock().unwrap();
            match *min_duration {
                Some(min) if duration < min => *min_duration = Some(duration),
                None => *min_duration = Some(duration),
                _ => {}
            }
        }
        
        // Update max duration
        {
            let mut max_duration = self.max_duration.lock().unwrap();
            match *max_duration {
                Some(max) if duration > max => *max_duration = Some(duration),
                None => *max_duration = Some(duration),
                _ => {}
            }
        }
    }
    
    /// Execute a function and record its duration
    ///
    /// # Arguments
    ///
    /// * `f` - Function to execute
    ///
    /// # Returns
    ///
    /// * The result of the function
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let result = counter.execute(|| {
    ///     // Perform some operation
    ///     42
    /// });
    /// ```
    pub fn execute<F, R>(&self, f: F) -> R
    where
        F: FnOnce() -> R,
    {
        let start = Instant::now();
        let result = f();
        let duration = start.elapsed();
        
        self.record(duration);
        
        result
    }
    
    /// Get the number of calls
    ///
    /// # Returns
    ///
    /// * `usize` - Number of calls
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let calls = counter.get_calls();
    /// ```
    pub fn get_calls(&self) -> usize {
        self.calls.load(Ordering::Relaxed)
    }
    
    /// Get the total duration
    ///
    /// # Returns
    ///
    /// * `Duration` - Total duration
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let total_duration = counter.get_total_duration();
    /// ```
    pub fn get_total_duration(&self) -> Duration {
        *self.total_duration.lock().unwrap()
    }
    
    /// Get the average duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Average duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(avg_duration) = counter.get_average_duration() {
    ///     println!("Average duration: {:?}", avg_duration);
    /// }
    /// ```
    pub fn get_average_duration(&self) -> Option<Duration> {
        let calls = self.get_calls();
        if calls == 0 {
            None
        } else {
            let total_duration = self.get_total_duration();
            Some(total_duration / calls as u32)
        }
    }
    
    /// Get the minimum duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Minimum duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(min_duration) = counter.get_min_duration() {
    ///     println!("Minimum duration: {:?}", min_duration);
    /// }
    /// ```
    pub fn get_min_duration(&self) -> Option<Duration> {
        *self.min_duration.lock().unwrap()
    }
    
    /// Get the maximum duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Maximum duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(max_duration) = counter.get_max_duration() {
    ///     println!("Maximum duration: {:?}", max_duration);
    /// }
    /// ```
    pub fn get_max_duration(&self) -> Option<Duration> {
        *self.max_duration.lock().unwrap()
    }
    
    /// Reset the counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// // Record some calls
    /// counter.reset();
    /// ```
    pub fn reset(&self) {
        self.calls.store(0, Ordering::Relaxed);
        *self.total_duration.lock().unwrap() = Duration::from_secs(0);
        *self.min_duration.lock().unwrap() = None;
        *self.max_duration.lock().unwrap() = None;
    }
    
    /// Get a summary of the counter
    ///
    /// # Returns
    ///
    /// * `String` - Summary string
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// // Record some calls
    /// let summary = counter.summary();
    /// println!("{}", summary);
    /// ```
    pub fn summary(&self) -> String {
        let calls = self.get_calls();
        if calls == 0 {
            return format!("{}: No calls recorded", self.name);
        }
        
        let total_duration = self.get_total_duration();
        let avg_duration = self.get_average_duration().unwrap();
        let min_duration = self.get_min_duration().unwrap();
        let max_duration = self.get_max_duration().unwrap();
        
        format!(
            "{}: {} calls, total: {:?}, avg: {:?}, min: {:?}, max: {:?}",
            self.name, calls, total_duration, avg_duration, min_duration, max_duration
        )
    }
}

/// Performance tracer for tracing function calls with detailed timing information
#[allow(dead_code)]
pub struct PerformanceTracer {
    /// Tracer name
    name: String,
    /// Start time
    start: Instant,
    /// Current phase
    current_phase: String,
    /// Phase transitions
    phases: Vec<(String, Duration)>,
}

#[allow(dead_code)]
impl PerformanceTracer {
    /// Create a new performance tracer
    ///
    /// # Arguments
    ///
    /// * `name` - Tracer name
    ///
    /// # Returns
    ///
    /// * `PerformanceTracer` - The new tracer
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let tracer = PerformanceTracer::new("compilation");
    /// ```
    pub fn new(name: &str) -> Self {
        let start = Instant::now();
        Self {
            name: name.to_string(),
            start,
            current_phase: "start".to_string(),
            phases: vec![("start".to_string(), Duration::from_secs(0))],
        }
    }
    
    /// Start a new phase
    ///
    /// # Arguments
    ///
    /// * `phase` - Phase name
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// // Perform parsing
    /// tracer.start_phase("analysis");
    /// // Perform analysis
    /// ```
    pub fn start_phase(&mut self, phase: &str) {
        let elapsed = self.start.elapsed();
        self.phases.push((phase.to_string(), elapsed));
        self.current_phase = phase.to_string();
    }
    
    /// Finish the tracer and return a summary
    ///
    /// # Returns
    ///
    /// * `String` - Summary string
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// // Perform parsing
    /// tracer.start_phase("analysis");
    /// // Perform analysis
    /// let summary = tracer.finish();
    /// println!("{}", summary);
    /// ```
    pub fn finish(mut self) -> String {
        let elapsed = self.start.elapsed();
        self.phases.push(("end".to_string(), elapsed));
        
        let mut summary = format!("Performance trace for {}: total {:?}\n", self.name, elapsed);
        let mut prev_duration = Duration::from_secs(0);
        
        for (_i, (phase, duration)) in self.phases.iter().enumerate().skip(1) {
            let phase_duration = *duration - prev_duration;
            prev_duration = *duration;
            
            let percent = if elapsed.as_secs_f64() > 0.0 {
                phase_duration.as_secs_f64() / elapsed.as_secs_f64() * 100.0
            } else {
                0.0
            };
            
            summary.push_str(&format!(
                "  Phase {phase}: {phase_duration:?} ({percent:.2}%)\n"
            ));
        }
        
        summary
    }
    
    /// Get the elapsed time since the start of the tracer
    ///
    /// # Returns
    ///
    /// * `Duration` - Elapsed time
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let tracer = PerformanceTracer::new("compilation");
    /// let elapsed = tracer.elapsed();
    /// println!("Elapsed time: {:?}", elapsed);
    /// ```
    pub fn elapsed(&self) -> Duration {
        self.start.elapsed()
    }
    
    /// Get the current phase
    ///
    /// # Returns
    ///
    /// * `&str` - Current phase name
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// let current_phase = tracer.current_phase();
    /// println!("Current phase: {}", current_phase);
    /// ```
    pub fn current_phase(&self) -> &str {
        &self.current_phase
    }
}

/// Scope-based performance counter
///
/// This struct automatically records the duration of a scope when it is dropped.
pub struct ScopedPerformanceCounter<'a> {
    /// Counter to record the duration
    counter: &'a PerformanceCounter,
    /// Start time
    start: Instant,
}

#[allow(dead_code)]
impl<'a> ScopedPerformanceCounter<'a> {
    /// Create a new scoped performance counter
    ///
    /// # Arguments
    ///
    /// * `counter` - Performance counter to record the duration
    ///
    /// # Returns
    ///
    /// * `ScopedPerformanceCounter` - The new scoped counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::{PerformanceCounter, ScopedPerformanceCounter};
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// {
    ///     let _scoped_counter = ScopedPerformanceCounter::new(&counter);
    ///     // Perform some operation
    /// } // Duration is automatically recorded when _scoped_counter is dropped
    /// ```
    pub fn new(counter: &'a PerformanceCounter) -> Self {
        Self {
            counter,
            start: Instant::now(),
        }
    }
}

impl<'a> Drop for ScopedPerformanceCounter<'a> {
    fn drop(&mut self) {
        let duration = self.start.elapsed();
        self.counter.record(duration);
    }
}
