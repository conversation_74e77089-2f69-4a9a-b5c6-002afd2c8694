// src/binary_analyzer/macho_analyzer.rs
//! Mach-O binary analyzer for the OmniForge compiler.
//!
//! This module provides functionality for analyzing macOS Mach-O
//! files and extracting metadata.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use memmap2::Mmap;
use goblin::mach::{Mach, MachO, exports::Export, imports::Import, header::{MH_MAGIC_64, MH_CIGAM_64}};

use crate::error::{OmniError, OmniResult};
use super::{BinaryMetadata, BinaryType, ExportedFunction, ImportedFunction, CallingConvention};

/// Mach-O binary analyzer
pub struct MachOAnalyzer {
    // Configuration options can be added here
}

impl Default for MachOAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl MachOAnalyzer {
    /// Create a new Mach-O analyzer
    pub fn new() -> Self {
        Self {}
    }
    
    /// Analyze a Mach-O file and extract metadata
    pub fn analyze(&self, path: &Path) -> OmniResult<BinaryMetadata> {
        log::debug!("Analyzing Mach-O file: {}", path.display());
        
        // Open and memory map the file
        let file = File::open(path)?;
        let map = unsafe { Mmap::map(&file)? };
        
        // Parse the Mach-O file
        let mach = Mach::parse(&map)
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to parse Mach-O file: {e}")))?;
        
        match mach {
            Mach::Binary(macho) => self.analyze_macho(path, &macho),
            Mach::Fat(_fat) => {
                // For fat binaries, we'll skip detailed analysis for now
                // This is a simplified implementation
                Err(OmniError::BinaryFormat("Fat binary analysis not fully implemented".to_string()))
            }
        }
    }
    
    /// Analyze a Mach-O binary and extract metadata
    fn analyze_macho(&self, path: &Path, macho: &MachO) -> OmniResult<BinaryMetadata> {
        // Extract exports
        let exports = self.extract_exports(macho)?;
        
        // Extract imports
        let imports = self.extract_imports(macho)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(macho)?;
        
        // Build additional metadata
        let additional_metadata = self.extract_additional_metadata(macho)?;
        
        Ok(BinaryMetadata {
            binary_type: BinaryType::MachO,
            path: path.to_string_lossy().to_string(),
            exports,
            imports,
            dependencies,
            additional_metadata,
        })
    }
    
    /// Extract exported functions from the Mach-O file
    fn extract_exports(&self, macho: &MachO) -> OmniResult<Vec<ExportedFunction>> {
        let mut exports = Vec::new();
        
        for export in macho.exports()
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to get exports: {e}")))? {
            exports.push(self.convert_export(&export)?);
        }
        
        Ok(exports)
    }
    
    /// Convert a Mach-O export to an ExportedFunction
    fn convert_export(&self, export: &Export) -> OmniResult<ExportedFunction> {
        Ok(ExportedFunction {
            name: export.name.to_string(),
            address: export.offset,
            signature: None, // Cannot determine signature from Mach-O alone
            calling_convention: Some(CallingConvention::C),
            metadata: serde_json::json!({
                "offset": export.offset,
            }),
        })
    }
    
    /// Extract imported functions from the Mach-O file
    fn extract_imports(&self, macho: &MachO) -> OmniResult<Vec<ImportedFunction>> {
        let mut imports = Vec::new();
        
        for import in macho.imports()
            .map_err(|e| OmniError::BinaryFormat(format!("Failed to get imports: {e}")))? {
            imports.push(self.convert_import(&import)?);
        }
        
        Ok(imports)
    }
    
    /// Convert a Mach-O import to an ImportedFunction
    fn convert_import(&self, import: &Import) -> OmniResult<ImportedFunction> {
        let library = if import.dylib.is_empty() {
            "unknown".to_string()
        } else {
            import.dylib.to_string()
        };

        Ok(ImportedFunction {
            name: import.name.to_string(),
            library,
            signature: None, // Cannot determine signature from Mach-O alone
        })
    }
    
    /// Extract dependencies from the Mach-O file
    fn extract_dependencies(&self, macho: &MachO) -> OmniResult<Vec<String>> {
        let mut dependencies = Vec::new();
        
        for lib in &macho.libs {
            dependencies.push(lib.to_string());
        }
        
        Ok(dependencies)
    }
    
    /// Extract additional metadata from the Mach-O file
    fn extract_additional_metadata(&self, macho: &MachO) -> OmniResult<serde_json::Value> {
        Ok(serde_json::json!({
            "cputype": macho.header.cputype,
            "cpusubtype": macho.header.cpusubtype,
            "filetype": macho.header.filetype,
            "is_64": macho.header.magic == MH_MAGIC_64 || macho.header.magic == MH_CIGAM_64,
            "is_executable": macho.header.filetype == goblin::mach::header::MH_EXECUTE,
            "is_dylib": macho.header.filetype == goblin::mach::header::MH_DYLIB,
        }))
    }
}
