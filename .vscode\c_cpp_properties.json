{"configurations": [{"name": "Win32-MSVC", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/haal/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "AVX2_ENABLED", "_WIN32", "_CRT_SECURE_NO_WARNINGS"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "compilerArgs": ["/std:c++17", "/O2", "/arch:AVX2", "/openmp", "/W3"], "browse": {"path": ["${workspaceFolder}", "${workspaceFolder}/haal/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}, {"name": "Win32-MinG<PERSON>", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/haal/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include", "C:/msys64/ucrt64/include", "C:/msys64/ucrt64/include/c++/15.1.0", "C:/msys64/ucrt64/include/c++/15.1.0/x86_64-w64-mingw32"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "AVX2_ENABLED", "_WIN32", "__MINGW32__"], "compilerPath": "C:/msys64/ucrt64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "compilerArgs": ["-std=c++17", "-O3", "-mavx2", "-mfma", "-march=native", "-fopenmp", "-pthread", "-Wall", "-Wextra"], "browse": {"path": ["${workspaceFolder}", "${workspaceFolder}/haal/include", "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include", "C:/msys64/ucrt64/include"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}}], "version": 4}