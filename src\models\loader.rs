﻿// src/models/loader.rs
//! # Runtime Model Loader & Registry
//!
//! This module provides a runtime registry system that automatically dispatches
//! model loading to the appropriate backend based on file extension.
//!
//! ## Architecture
//!
//! The loader uses a static registry (`REGISTRY`) that maps file extensions to
//! loader functions. This allows for dynamic model format detection and loading
//! without requiring compile-time knowledge of the model format.
//!
//! ## Usage
//!
//! ```rust,no_run
//! use omni_forge::models::{load_model, LoadOptions, Device};
//! use std::path::Path;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! let options = LoadOptions {
//!     device: Device::Cpu,
//!     quantized: None,
//! };
//!
//! // Automatically detects format from extension
//! let model = load_model(Path::new("model.gguf"), options)?;
//! # Ok(())
//! # }
//! ```
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use std::path::Path;
use once_cell::sync::Lazy;

use crate::models::{Umlaiie, LoadOptions, UmlaiieError, UmlaiieResult};

/// Type alias for loader functions
///
/// Each loader function takes a path and load options, returning a boxed trait object
/// that implements the Umlaiie trait.
pub type Loader = Box<dyn Fn(&Path, LoadOptions) -> anyhow::Result<Box<dyn Umlaiie>> + Send + Sync>;

/// Global registry mapping file extensions to loader functions
///
/// This registry is initialized lazily and populated with all available model loaders.
/// New model formats can be registered by adding entries to this map.
static REGISTRY: Lazy<HashMap<&'static str, Loader>> = Lazy::new(|| {
    let mut registry: HashMap<&'static str, Loader> = HashMap::new();

    // Register GGUF loader
    let gguf_loader: Loader = Box::new(|path, options| {
        use crate::models::gguf::GgufModel;
        Ok(Box::new(GgufModel::load(path, options)?))
    });
    registry.insert("gguf", gguf_loader);

    // Register SafeTensors loader
    let safetensors_loader: Loader = Box::new(|path, options| {
        use crate::models::safetensors::SafeTensorsModel;
        Ok(Box::new(SafeTensorsModel::load(path, options)?))
    });
    registry.insert("safetensors", safetensors_loader);

    // Additional formats can be registered here
    // registry.insert("onnx", onnx_loader);
    // registry.insert("pt", pytorch_loader);

    registry
});

/// Load a model from the specified path, automatically detecting the format
///
/// This function examines the file extension to determine the appropriate model
/// format and dispatches to the corresponding loader.
///
/// # Arguments
///
/// * `path` - Path to the model file
/// * `options` - Loading options including device and quantization settings
///
/// # Returns
///
/// Returns a boxed trait object implementing [`Umlaiie`] if successful,
/// or an error if the format is unsupported or loading fails.
///
/// # Supported Extensions
///
/// - `.gguf` - GGUF format models (llama.cpp ecosystem)
/// - `.safetensors` - SafeTensors format models
///
/// # Examples
///
/// ```rust,no_run
/// use omni_forge::models::{load_model, LoadOptions, Device};
/// use std::path::Path;
///
/// # fn main() -> Result<(), Box<dyn std::error::Error>> {
/// let options = LoadOptions {
///     device: Device::Cpu,
///     quantized: None,
/// };
///
/// // Load a GGUF model
/// let gguf_model = load_model(Path::new("model.gguf"), options.clone())?;
///
/// // Load a SafeTensors model
/// let safetensors_model = load_model(Path::new("model.safetensors"), options)?;
/// # Ok(())
/// # }
/// ```
///
/// # Errors
///
/// Returns [`UmlaiieError::LoadError`] if:
/// - The file extension is not supported
/// - The file cannot be read
/// - The model format is invalid
/// - The specified device is not available
pub fn load_model(path: &Path, options: LoadOptions) -> UmlaiieResult<Box<dyn Umlaiie>> {
    // Extract file extension
    let extension = path
        .extension()
        .and_then(|ext| ext.to_str())
        .ok_or_else(|| {
            UmlaiieError::LoadError(format!(
                "Unable to determine file extension for path: {}",
                path.display()
            ))
        })?;
    
    // Look up the appropriate loader
    let loader = REGISTRY.get(extension).ok_or_else(|| {
        UmlaiieError::LoadError(format!(
            "Unsupported model format: .{}\nSupported formats: {}",
            extension,
            get_supported_formats().join(", ")
        ))
    })?;
    
    // Call the loader function
    loader(path, options).map_err(|e| {
        UmlaiieError::LoadError(format!(
            "Failed to load {} model from {}: {}",
            extension,
            path.display(),
            e
        ))
    })
}

/// Get a list of all supported model format extensions
///
/// # Returns
///
/// A vector of supported file extensions (without the leading dot).
///
/// # Examples
///
/// ```rust,no_run
/// use omni_forge::models::loader::get_supported_formats;
///
/// let formats = get_supported_formats();
/// println!("Supported formats: {}", formats.join(", "));
/// ```
pub fn get_supported_formats() -> Vec<String> {
    REGISTRY.keys().map(|&k| format!(".{}", k)).collect()
}

/// Register a new model format loader
///
/// This function allows runtime registration of new model format loaders.
/// Note that this modifies global state and should be used carefully.
///
/// # Arguments
///
/// * `extension` - File extension (without the dot) to register
/// * `loader` - Loader function for this format
///
/// # Examples
///
/// ```rust,no_run
/// use omni_forge::models::loader::register_loader;
/// use omni_forge::models::{Umlaiie, LoadOptions};
/// use std::path::Path;
///
/// // Example custom loader (this would need actual implementation)
/// fn custom_loader(path: &Path, options: LoadOptions) -> anyhow::Result<Box<dyn Umlaiie>> {
///     // Custom loading logic here
///     todo!("Implement custom loader")
/// }
///
/// // Register the custom format
/// register_loader("custom", custom_loader);
/// ```
///
/// # Safety
///
/// This function is marked as unsafe because it modifies global state.
/// It should only be called during application initialization.
pub unsafe fn register_loader(extension: &'static str, _loader: Loader) {
    // Note: This is a simplified example. In practice, you'd need a more
    // sophisticated approach to safely modify the static registry.
    // For now, we'll leave this as a placeholder for future enhancement.
    log::warn!("Dynamic loader registration not yet implemented for extension: {}", extension);
}

/// Check if a model format is supported
///
/// # Arguments
///
/// * `extension` - File extension to check (with or without leading dot)
///
/// # Returns
///
/// `true` if the format is supported, `false` otherwise.
///
/// # Examples
///
/// ```rust,no_run
/// use omni_forge::models::loader::is_format_supported;
///
/// assert!(is_format_supported("gguf"));
/// assert!(is_format_supported(".safetensors"));
/// assert!(!is_format_supported("unknown"));
/// ```
pub fn is_format_supported(extension: &str) -> bool {
    let ext = extension.strip_prefix('.').unwrap_or(extension);
    REGISTRY.contains_key(ext)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_supported_formats() {
        let formats = get_supported_formats();
        assert!(formats.contains(&".gguf".to_string()));
        assert!(formats.contains(&".safetensors".to_string()));
    }

    #[test]
    fn test_format_support_check() {
        assert!(is_format_supported("gguf"));
        assert!(is_format_supported(".gguf"));
        assert!(is_format_supported("safetensors"));
        assert!(!is_format_supported("unknown"));
    }
}
