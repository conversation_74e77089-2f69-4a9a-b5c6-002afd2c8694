// src/util/hash.rs
//! Hash utilities for the OmniForge compiler.
//!
//! This module provides functionality for computing hashes of files and strings,
//! which is useful for caching and validation in the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use std::io::{self, Read, BufReader};
use std::fmt::Write as FmtWrite;

/// Hash algorithm types
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum HashAlgorithm {
    /// MD5 hash algorithm
    MD5,
    /// SHA-1 hash algorithm
    SHA1,
    /// SHA-256 hash algorithm
    SHA256,
    /// SHA-512 hash algorithm
    SHA512,
    /// Blake2b hash algorithm
    Blake2b,
    /// Blake3 hash algorithm
    Blake3,
    /// xxHash algorithm
    XXHash,
}

impl HashAlgorithm {
    /// Get the string representation of the hash algorithm
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::MD5 => "md5",
            Self::SHA1 => "sha1",
            Self::SHA256 => "sha256",
            Self::SHA512 => "sha512",
            Self::Blake2b => "blake2b",
            Self::Blake3 => "blake3",
            Self::XXHash => "xxhash",
        }
    }
}

/// Compute the hash of a file
///
/// # Arguments
///
/// * `path` - Path to the file
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `io::Result<String>` - Hex-encoded hash string
///
/// # Examples
///
/// ```no_run
/// use std::path::Path;
/// use omni_forge::util::hash::{compute_file_hash, HashAlgorithm};
///
/// let hash = compute_file_hash(Path::new("src/main.rs"), HashAlgorithm::SHA256).unwrap();
/// println!("Hash: {}", hash);
/// ```
pub fn compute_file_hash<P: AsRef<Path>>(path: P, algorithm: HashAlgorithm) -> io::Result<String> {
    let file = File::open(path)?;
    let mut reader = BufReader::new(file);
    let mut buffer = Vec::new();
    reader.read_to_end(&mut buffer)?;
    
    Ok(compute_hash(&buffer, algorithm))
}

/// Compute the hash of a string
///
/// # Arguments
///
/// * `input` - Input string
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
///
/// # Examples
///
/// ```
/// use omni_forge::util::hash::{compute_string_hash, HashAlgorithm};
///
/// let hash = compute_string_hash("Hello, world!", HashAlgorithm::SHA256);
/// println!("Hash: {}", hash);
/// ```
pub fn compute_string_hash(input: &str, algorithm: HashAlgorithm) -> String {
    compute_hash(input.as_bytes(), algorithm)
}

/// Compute the hash of a byte slice
///
/// # Arguments
///
/// * `data` - Input data
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
fn compute_hash(data: &[u8], algorithm: HashAlgorithm) -> String {
    match algorithm {
        HashAlgorithm::MD5 => {
            let digest = md5::compute(data);
            format_hash_bytes(&digest.0)
        }
        HashAlgorithm::SHA1 => {
            use sha1::{Sha1, Digest};
            let mut hasher = Sha1::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA256 => {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA512 => {
            use sha2::{Sha512, Digest};
            let mut hasher = Sha512::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake2b => {
            use blake2::{Blake2b512, Digest};
            let mut hasher = Blake2b512::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake3 => {
            let hash = blake3::hash(data);
            hash.to_hex().to_string()
        }
        HashAlgorithm::XXHash => {
            use twox_hash::XxHash64;
            use std::hash::Hasher;
            let mut hasher = XxHash64::with_seed(0);
            hasher.write(data);
            format!("{:016x}", hasher.finish())
        }
    }
}

/// Format hash bytes as a hex string
///
/// # Arguments
///
/// * `bytes` - Hash bytes
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
fn format_hash_bytes(bytes: &[u8]) -> String {
    let mut s = String::with_capacity(bytes.len() * 2);
    for &b in bytes {
        write!(&mut s, "{b:02x}").unwrap();
    }
    s
}

/// Check if a file matches a hash
///
/// # Arguments
///
/// * `path` - Path to the file
/// * `expected_hash` - Expected hash
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `io::Result<bool>` - Whether the file matches the hash
///
/// # Examples
///
/// ```no_run
/// use std::path::Path;
/// use omni_forge::util::hash::{check_file_hash, HashAlgorithm};
///
/// let matches = check_file_hash(
///     Path::new("src/main.rs"),
///     "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
///     HashAlgorithm::SHA256
/// ).unwrap();
///
/// if matches {
///     println!("File hash matches");
/// } else {
///     println!("File hash does not match");
/// }
/// ```
#[allow(dead_code)]
pub fn check_file_hash<P: AsRef<Path>>(path: P, expected_hash: &str, algorithm: HashAlgorithm) -> io::Result<bool> {
    let hash = compute_file_hash(path, algorithm)?;
    Ok(hash == expected_hash)
}

/// Generate a hash of multiple inputs
///
/// # Arguments
///
/// * `inputs` - Input strings or byte arrays
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
///
/// # Examples
///
/// ```
/// use omni_forge::util::hash::{compute_combined_hash, HashAlgorithm};
///
/// let hash = compute_combined_hash(&["Hello", "world"], HashAlgorithm::SHA256);
/// println!("Hash: {}", hash);
/// ```
#[allow(dead_code)]
pub fn compute_combined_hash<I, B>(inputs: I, algorithm: HashAlgorithm) -> String
where
    I: IntoIterator<Item = B>,
    B: AsRef<[u8]>,
{
    match algorithm {
        HashAlgorithm::MD5 => {
            let mut context = md5::Context::new();
            for input in inputs {
                context.consume(input.as_ref());
            }
            let digest = context.finalize();
            format_hash_bytes(&digest.0)
        }
        HashAlgorithm::SHA1 => {
            use sha1::{Sha1, Digest};
            let mut hasher = Sha1::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA256 => {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA512 => {
            use sha2::{Sha512, Digest};
            let mut hasher = Sha512::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake2b => {
            use blake2::{Blake2b512, Digest};
            let mut hasher = Blake2b512::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake3 => {
            let mut hasher = blake3::Hasher::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            hasher.finalize().to_hex().to_string()
        }
        HashAlgorithm::XXHash => {
            use twox_hash::XxHash64;
            use std::hash::Hasher;
            let mut hasher = XxHash64::with_seed(0);
            for input in inputs {
                hasher.write(input.as_ref());
            }
            format!("{:016x}", hasher.finish())
        }
    }
}

/// Compute an incremental hash
///
/// This struct allows you to compute a hash incrementally, adding data in chunks.
#[allow(dead_code)]
pub struct IncrementalHasher {
    /// Hash algorithm
    algorithm: HashAlgorithm,
    /// Internal hasher state
    state: IncrementalHasherState,
}

/// Internal hasher state
#[allow(dead_code)]
enum IncrementalHasherState {
    /// MD5 hasher
    MD5(md5::Context),
    /// SHA-1 hasher
    SHA1(sha1::Sha1),
    /// SHA-256 hasher
    SHA256(sha2::Sha256),
    /// SHA-512 hasher
    SHA512(sha2::Sha512),
    /// Blake2b hasher
    Blake2b(blake2::Blake2b512),
    /// Blake3 hasher
    Blake3(blake3::Hasher),
    /// XXHash hasher
    XXHash(twox_hash::XxHash64),
}

#[allow(dead_code)]
impl IncrementalHasher {
    /// Create a new incremental hasher
    ///
    /// # Arguments
    ///
    /// * `algorithm` - Hash algorithm to use
    ///
    /// # Returns
    ///
    /// * `IncrementalHasher` - The new hasher
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::hash::{IncrementalHasher, HashAlgorithm};
    ///
    /// let mut hasher = IncrementalHasher::new(HashAlgorithm::SHA256);
    /// hasher.update("Hello, ");
    /// hasher.update("world!");
    /// let hash = hasher.finalize();
    /// println!("Hash: {}", hash);
    /// ```
    pub fn new(algorithm: HashAlgorithm) -> Self {
        let state = match algorithm {
            HashAlgorithm::MD5 => IncrementalHasherState::MD5(md5::Context::new()),
            HashAlgorithm::SHA1 => IncrementalHasherState::SHA1(sha1::Sha1::default()),
            HashAlgorithm::SHA256 => IncrementalHasherState::SHA256(sha2::Sha256::default()),
            HashAlgorithm::SHA512 => IncrementalHasherState::SHA512(sha2::Sha512::default()),
            HashAlgorithm::Blake2b => IncrementalHasherState::Blake2b(blake2::Blake2b512::default()),
            HashAlgorithm::Blake3 => IncrementalHasherState::Blake3(blake3::Hasher::new()),
            HashAlgorithm::XXHash => IncrementalHasherState::XXHash(twox_hash::XxHash64::with_seed(0)),
        };
        
        Self {
            algorithm,
            state,
        }
    }
    
    /// Update the hasher with more data
    ///
    /// # Arguments
    ///
    /// * `data` - Data to add to the hash
    pub fn update<T: AsRef<[u8]>>(&mut self, data: T) {
        match &mut self.state {
            IncrementalHasherState::MD5(hasher) => {
                hasher.consume(data.as_ref());
            }
            IncrementalHasherState::SHA1(hasher) => {
                use sha1::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::SHA256(hasher) => {
                use sha2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::SHA512(hasher) => {
                use sha2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::Blake2b(hasher) => {
                use blake2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::Blake3(hasher) => {
                hasher.update(data.as_ref());
            }
            IncrementalHasherState::XXHash(hasher) => {
                use std::hash::Hasher;
                hasher.write(data.as_ref());
            }
        }
    }
    
    /// Finalize the hash and return the result
    ///
    /// # Returns
    ///
    /// * `String` - Hex-encoded hash string
    pub fn finalize(self) -> String {
        match self.state {
            IncrementalHasherState::MD5(hasher) => {
                let result = hasher.finalize();
                format_hash_bytes(&result.0)
            }
            IncrementalHasherState::SHA1(hasher) => {
                use sha1::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::SHA256(hasher) => {
                use sha2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::SHA512(hasher) => {
                use sha2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::Blake2b(hasher) => {
                use blake2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::Blake3(hasher) => {
                hasher.finalize().to_hex().to_string()
            }
            IncrementalHasherState::XXHash(hasher) => {
                use std::hash::Hasher;
                format!("{:016x}", hasher.finish())
            }
        }
    }
    
    /// Get the hash algorithm
    ///
    /// # Returns
    ///
    /// * `HashAlgorithm` - Hash algorithm
    pub fn algorithm(&self) -> HashAlgorithm {
        self.algorithm
    }
}
