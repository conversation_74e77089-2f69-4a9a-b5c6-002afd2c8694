// src/metadata_extractor/ptx_metadata.rs
//! PTX metadata extractor for the OmniForge compiler.
//!
//! This module provides functionality for extracting metadata from
//! NVIDIA PTX files.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs;
use regex::Regex;

use crate::error::{OmniError, OmniResult};
use crate::binary_analyzer::BinaryMetadata;
use super::{ExtractedMetadata, ExtractedFunction, ExtractedType, ExtractedSignature, ExtractedParameter, FunctionType, LaunchParameters};

/// PTX metadata extractor
pub struct PTXMetadataExtractor {
    // Configuration options can be added here
}

impl Default for PTXMetadataExtractor {
    fn default() -> Self {
        Self::new()
    }
}

impl PTXMetadataExtractor {
    /// Create a new PTX metadata extractor
    pub fn new() -> Self {
        Self {}
    }
    
    /// Extract metadata from a PTX file
    pub fn extract_metadata(&self, path: &Path, binary_metadata: BinaryMetadata) -> OmniResult<ExtractedMetadata> {
        log::debug!("Extracting PTX metadata from: {}", path.display());
        
        // Read the PTX file
        let ptx_content = fs::read_to_string(path)?;
        
        // Extract functions
        let functions = self.extract_functions(&ptx_content, &binary_metadata)?;
        
        // Extract types
        let types = self.extract_types(&ptx_content)?;
        
        // Extract additional metadata
        let additional_metadata = self.extract_additional_metadata(&ptx_content)?;
        
        Ok(ExtractedMetadata {
            binary_metadata,
            functions,
            types,
            additional_metadata,
        })
    }
    
    /// Extract functions from the PTX content
    fn extract_functions(&self, ptx_content: &str, binary_metadata: &BinaryMetadata) -> OmniResult<Vec<ExtractedFunction>> {
        let mut functions = Vec::new();
        
        // Process each exported function from the binary metadata
        for export in &binary_metadata.exports {
            // Find the function in the PTX content
            let function_regex = Regex::new(&format!(r"\.(entry|func)\s+{}[\s\S]*?(?=\.entry|\.func|\Z)", regex::escape(&export.name)))
                .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
            
            if let Some(function_match) = function_regex.find(ptx_content) {
                let function_content = function_match.as_str();
                
                // Determine function type
                let function_type = if function_content.starts_with(".entry") {
                    FunctionType::Kernel
                } else {
                    FunctionType::Device
                };
                
                // Extract parameter information
                let params_regex = Regex::new(r"\((.*?)\)")
                    .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
                
                let signature = if let Some(params_match) = params_regex.captures(function_content) {
                    let params_str = params_match.get(1).unwrap().as_str();
                    
                    // Parse parameters
                    let parameter_types = self.parse_parameters(params_str)?;
                    
                    Some(ExtractedSignature {
                        return_type: ExtractedType {
                            name: "void".to_string(),
                            size: Some(0),
                            alignment: Some(1),
                            is_pointer: false,
                            is_array: false,
                            array_dimensions: Vec::new(),
                            is_const: false,
                            is_volatile: false,
                        },
                        parameter_types,
                        is_variadic: false,
                        calling_convention: if function_type == FunctionType::Kernel {
                            "cudaKernel".to_string()
                        } else {
                            "cudaDevice".to_string()
                        },
                    })
                } else {
                    None
                };
                
                // Extract launch parameters for kernels
                let launch_params = if function_type == FunctionType::Kernel {
                    // Extract shared memory usage
                    let shared_mem = self.extract_shared_memory(function_content);
                    
                    // Determine optimal launch configuration
                    Some(self.determine_launch_config(function_content, shared_mem))
                } else {
                    None
                };
                
                // Extract register usage
                let register_count = self.extract_register_count(function_content);
                
                functions.push(ExtractedFunction {
                    name: export.name.clone(),
                    signature,
                    function_type,
                    launch_params,
                    memory_layout: None,
                    metadata: serde_json::json!({
                        "register_count": register_count,
                        "is_kernel": function_type == FunctionType::Kernel,
                    }),
                });
            }
        }
        
        Ok(functions)
    }
    
    /// Parse parameters from the parameter string
    fn parse_parameters(&self, params_str: &str) -> OmniResult<Vec<ExtractedParameter>> {
        let mut parameters = Vec::new();
        
        if params_str.trim().is_empty() {
            return Ok(parameters);
        }
        
        for param in params_str.split(',') {
            let param = param.trim();
            
            // Parse parameter format: .param .type .ptr .align X .space .param_space param_name
            let param_regex = Regex::new(r"\.param\s+(\.\w+)(?:\s+\.ptr)?(?:\s+\.align\s+(\d+))?(?:\s+\.space\s+\.(\w+))?\s+(\w+)")
                .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
            
            if let Some(captures) = param_regex.captures(param) {
                let type_name = captures.get(1).unwrap().as_str().to_string();
                let alignment = captures.get(2).map(|m| m.as_str().parse::<usize>().unwrap_or(0));
                let space = captures.get(3).map(|m| m.as_str().to_string());
                let param_name = captures.get(4).unwrap().as_str().to_string();
                
                let mut attributes = Vec::new();
                if let Some(space) = space {
                    attributes.push(format!("space:{space}"));
                }
                
                parameters.push(ExtractedParameter {
                    name: param_name,
                    type_info: ExtractedType {
                        name: type_name,
                        size: None, // Cannot determine size from PTX alone
                        alignment,
                        is_pointer: param.contains(".ptr"),
                        is_array: false,
                        array_dimensions: Vec::new(),
                        is_const: false,
                        is_volatile: false,
                    },
                    attributes,
                });
            }
        }
        
        Ok(parameters)
    }
    
    /// Extract shared memory usage from the function content
    fn extract_shared_memory(&self, function_content: &str) -> usize {
        let shared_mem_regex = Regex::new(r"\.shared\s+\.align\s+\d+\s+\.b8\s+\w+\[(\d+)\]").ok();
        
        let mut total_shared_mem = 0;
        if let Some(regex) = shared_mem_regex {
            for captures in regex.captures_iter(function_content) {
                if let Some(size_match) = captures.get(1) {
                    if let Ok(size) = size_match.as_str().parse::<usize>() {
                        total_shared_mem += size;
                    }
                }
            }
        }
        
        total_shared_mem
    }
    
    /// Extract register count from the function content
    fn extract_register_count(&self, function_content: &str) -> Option<usize> {
        let reg_regex = Regex::new(r"// Function requires\s+(\d+)\s+registers").ok()?;
        
        if let Some(captures) = reg_regex.captures(function_content) {
            if let Some(count_match) = captures.get(1) {
                return count_match.as_str().parse::<usize>().ok();
            }
        }
        
        None
    }
    
    /// Determine optimal launch configuration based on function analysis
    fn determine_launch_config(&self, function_content: &str, shared_mem: usize) -> LaunchParameters {
        // This is a simplified implementation
        // In a real implementation, we would analyze the kernel code to determine
        // optimal grid and block dimensions
        
        // Estimate block size based on register usage
        let block_size = if let Some(reg_count) = self.extract_register_count(function_content) {
            if reg_count > 64 {
                // High register usage: smaller blocks
                128
            } else if reg_count > 32 {
                // Medium register usage: medium blocks
                256
            } else {
                // Low register usage: larger blocks
                512
            }
        } else {
            // Default
            256
        };
        
        LaunchParameters {
            grid_dim: [1, 1, 1], // Default
            block_dim: [block_size, 1, 1],
            shared_mem_bytes: shared_mem,
            dynamic_shared_mem: false,
        }
    }
    
    /// Extract types from the PTX content
    fn extract_types(&self, _ptx_content: &str) -> OmniResult<Vec<ExtractedType>> {
        // PTX doesn't have explicit type definitions, but we can extract basic types used
        
        let mut types = Vec::new();
        
        // Define standard PTX types
        let standard_types = [
            (".u8", 1), (".u16", 2), (".u32", 4), (".u64", 8),
            (".s8", 1), (".s16", 2), (".s32", 4), (".s64", 8),
            (".f16", 2), (".f32", 4), (".f64", 8),
            (".b8", 1), (".b16", 2), (".b32", 4), (".b64", 8),
            (".pred", 1),
        ];
        
        for (type_name, size) in standard_types.iter() {
            types.push(ExtractedType {
                name: type_name.to_string(),
                size: Some(*size),
                alignment: Some(*size),
                is_pointer: false,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            });
        }
        
        // Also add pointer versions
        for (type_name, _size) in standard_types.iter() {
            types.push(ExtractedType {
                name: format!("{type_name}_ptr"),
                size: Some(8), // 64-bit pointers
                alignment: Some(8),
                is_pointer: true,
                is_array: false,
                array_dimensions: Vec::new(),
                is_const: false,
                is_volatile: false,
            });
        }
        
        Ok(types)
    }
    
    /// Extract additional metadata from the PTX content
    fn extract_additional_metadata(&self, ptx_content: &str) -> OmniResult<serde_json::Value> {
        // Extract PTX version
        let version_regex = Regex::new(r"\.version\s+(\d+)\.(\d+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let version = if let Some(captures) = version_regex.captures(ptx_content) {
            let major = captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0);
            let minor = captures.get(2).unwrap().as_str().parse::<u32>().unwrap_or(0);
            format!("{major}.{minor}")
        } else {
            "unknown".to_string()
        };
        
        // Extract target architecture
        let target_regex = Regex::new(r"\.target\s+([\w\.]+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let target = if let Some(captures) = target_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().to_string()
        } else {
            "unknown".to_string()
        };
        
        // Extract addressing mode
        let address_size_regex = Regex::new(r"\.address_size\s+(\d+)")
            .map_err(|e| OmniError::MetadataExtraction(format!("Failed to compile regex: {e}")))?;
        
        let address_size = if let Some(captures) = address_size_regex.captures(ptx_content) {
            captures.get(1).unwrap().as_str().parse::<u32>().unwrap_or(0)
        } else {
            0
        };
        
        Ok(serde_json::json!({
            "ptx_version": version,
            "target_architecture": target,
            "address_size": address_size,
            "num_kernels": ptx_content.matches(".entry").count(),
            "num_device_functions": ptx_content.matches(".func").count() - ptx_content.matches(".entry").count(), // Adjust for false positives
        }))
    }
}
