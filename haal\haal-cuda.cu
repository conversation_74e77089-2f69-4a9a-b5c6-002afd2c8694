// haal-cuda.cu - CUDA Performance Benchmark Suite
/**
 * Multi-kernel CUDA benchmark implementing optimized compute kernels
 * for performance evaluation on NVIDIA Ada Lovelace architecture.
 *
 * IMPLEMENTED KERNELS:
 *
 * xOneTensorCoreKernel - WMMA-based FP16 tensor operations
 * xOnePersistentKernel - Persistent thread block computation
 * xOneVectorOptimizedKernel - Vectorized FP16 operations
 * xOneRegisterSaturationKernel - High register utilization compute
 * xOneRegisterOptimizedKernel - Register-optimized arithmetic
 *
 * CONFIGURATION:
 * - Memory allocation: 134,217,728 elements (512 MB)
 * - Iteration count: 600 per kernel
 * - Test runs: 3 iterations with timing
 * - Target architecture: sm_89 (RTX 4080)
 *
 * COMPILATION:
 * nvcc haal-cuda.cu -o haal-cuda.exe -arch=sm_89 -O3 -use_fast_math --maxrregcount=64
 *
 * EXECUTION:
 * ./haal-cuda.exe
 *▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <mma.h>
#include <cooperative_groups.h>
#include <iostream>
#include <chrono>
#include <thread>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <cmath>
#include <cfloat>

using namespace nvcuda;
namespace cg = cooperative_groups;

// CUDA Error Checking Macro - EXACT from ultima.cu
#define CUDA_CHECK(call)                                                  \
    do                                                                    \
    {                                                                     \
        cudaError_t error = call;                                         \
        if (error != cudaSuccess)                                         \
        {                                                                 \
            std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__  \
                      << " - " << cudaGetErrorString(error) << std::endl; \
            exit(1);                                                      \
        }                                                                 \
    } while (0)

// ============================================================================
// EXACT COPY 1: xOneTensorCoreKernel (123.19 TFLOPS BASELINE) - FROM ULTIMA.CU
// ============================================================================

__global__ void xOneTensorCoreKernel(half *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size / 16)
        return; // WMMA operates on 16x16 matrices

    // Declare WMMA fragments
    wmma::fragment<wmma::matrix_a, 16, 16, 16, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, 16, 16, 16, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, 16, 16, 16, half> acc_frag;

    // Initialize fragments
    wmma::fill_fragment(acc_frag, __float2half(1.0f));
    wmma::fill_fragment(a_frag, __float2half(0.9f + static_cast<float>(idx % 100) / 1000.0f));
    wmma::fill_fragment(b_frag, __float2half(1.1f + static_cast<float>(idx % 97) / 1000.0f));

// High-intensity tensor operations
#pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        // Update fragments for next iteration
        for (int j = 0; j < a_frag.num_elements; ++j)
        {
            a_frag.x[j] = __hfma(a_frag.x[j], __float2half(1.0001f), __float2half(0.0001f));
        }
        for (int j = 0; j < b_frag.num_elements; ++j)
        {
            b_frag.x[j] = __hfma(b_frag.x[j], __float2half(0.9999f), __float2half(0.0001f));
        }
    }

    // Store result back to global memory
    int matrix_offset = idx * 256; // 16x16 = 256 elements
    if (matrix_offset + 255 < size)
    {
        wmma::store_matrix_sync(data + matrix_offset, acc_frag, 16, wmma::mem_row_major);
    }
}

// ============================================================================
// OPTIMIZATION 3: COOPERATIVE GROUPS PERSISTENT KERNEL
// ============================================================================

__global__ void xOnePersistentKernel(float *__restrict__ data, int size, int iterations, int total_blocks)
{
    // Cooperative groups setup
    cg::thread_block block = cg::this_thread_block();
    cg::thread_block_tile<32> warp = cg::tiled_partition<32>(block);

    int block_id = blockIdx.x;
    int thread_id = threadIdx.x;
    int threads_per_block = blockDim.x;

    // Persistent block loop with cooperative groups optimization
    for (int persistent_block = block_id; persistent_block < total_blocks; persistent_block += gridDim.x)
    {
        int base_idx = persistent_block * threads_per_block + thread_id;

        if (base_idx >= size)
            continue;

        float x = data[base_idx];

        // 16 independent accumulator chains for maximum parallelism
        float acc[16];
#pragma unroll
        for (int i = 0; i < 16; ++i)
        {
            acc[i] = x * (0.9f + static_cast<float>(i) * 0.01f);
        }

// Ultra-aggressive computation with warp-level cooperation
#pragma unroll 32
        for (int iter = 0; iter < iterations; ++iter)
        {
            // Warp-level shuffle for improved data sharing every 50 iterations
            if (iter % 50 == 0)
            {
                float shared_val = warp.shfl_xor(x, 1);
                x = fmaf(x, 0.9999f, shared_val * 0.0001f);
            }

#pragma unroll
            for (int i = 0; i < 16; ++i)
            {
                acc[i] = fmaf(acc[i], x, (1.0001f + static_cast<float>(i) * 0.0001f));
            }
        }

        // Cooperative reduction tree with warp primitives
        float result = 0.0f;
#pragma unroll
        for (int i = 0; i < 16; ++i)
        {
            result += acc[i];
        }

        // Warp-level reduction for better performance
        result = warp.shfl_down(result, 16);
        result = warp.shfl_down(result, 8);
        result = warp.shfl_down(result, 4);
        result = warp.shfl_down(result, 2);
        result = warp.shfl_down(result, 1);

        data[base_idx] = result;
    }
}

// ============================================================================
// EXACT COPY 3: xOneVectorOptimizedKernel (15.44 TFLOPS) - FROM ULTIMA.CU
// ============================================================================

__global__ void xOneVectorOptimizedKernel(half2 *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size)
        return;

    half2 x = data[idx];
    half2 acc1 = x;
    half2 acc2 = __hmul2(x, __float2half2_rn(0.7071f));

    // OPTIMIZATION TEST: Advanced loop unrolling with instruction-level parallelism
#pragma unroll 32
    for (int i = 0; i < iterations; i += 4)
    {
        // Unroll 4 iterations manually for better ILP
        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0001f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9999f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0002f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9998f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0003f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9997f));

        acc1 = __hfma2(acc1, x, __float2half2_rn(1.0004f));
        acc2 = __hfma2(acc2, x, __float2half2_rn(0.9996f));
    }

    data[idx] = __hmul2(acc1, acc2);
}

// ============================================================================
// EXACT COPY 4: xOneRegisterSaturationKernel (24.63 TFLOPS) - FROM Z-BEST.CU
// ============================================================================

// Bleeding-edge register optimization with 99% utilization
__global__ void __launch_bounds__(256, 2)
    xOneRegisterSaturationKernel(float *__restrict__ data, int size, int iterations)
{

    const int tid = blockIdx.x * blockDim.x + threadIdx.x;
    if (tid >= size)
        return;

    // Optimized register utilization: 64 registers for good occupancy
    // Organized as register matrix for optimal instruction dispatch
    register float r[16][4]; // 64 registers total

    // Load base value with optimal memory access
    const float base_val = data[tid];

// Advanced register initialization with dependency optimization
#pragma unroll
    for (int i = 0; i < 16; i++)
    {
        const float scale_factor = 0.1f + (i * 0.0625f); // Optimized scaling
        r[i][0] = base_val * scale_factor;
        r[i][1] = base_val * (scale_factor + 0.25f);
        r[i][2] = base_val * (scale_factor + 0.50f);
        r[i][3] = base_val * (scale_factor + 0.75f);
    }

    // Precomputed FMA constants for maximum instruction throughput
    const float fma_constants[64] = {
        1.0000010f, 1.0000020f, 1.0000030f, 1.0000040f, 1.0000050f, 1.0000060f, 1.0000070f, 1.0000080f,
        1.0000090f, 1.0000100f, 1.0000110f, 1.0000120f, 1.0000130f, 1.0000140f, 1.0000150f, 1.0000160f,
        1.0000170f, 1.0000180f, 1.0000190f, 1.0000200f, 1.0000210f, 1.0000220f, 1.0000230f, 1.0000240f,
        1.0000250f, 1.0000260f, 1.0000270f, 1.0000280f, 1.0000290f, 1.0000300f, 1.0000310f, 1.0000320f,
        0.9999990f, 0.9999980f, 0.9999970f, 0.9999960f, 0.9999950f, 0.9999940f, 0.9999930f, 0.9999920f,
        0.9999910f, 0.9999900f, 0.9999890f, 0.9999880f, 0.9999870f, 0.9999860f, 0.9999850f, 0.9999840f,
        0.9999830f, 0.9999820f, 0.9999810f, 0.9999800f, 0.9999790f, 0.9999780f, 0.9999770f, 0.9999760f,
        0.9999750f, 0.9999740f, 0.9999730f, 0.9999720f, 0.9999710f, 0.9999700f, 0.9999690f, 0.9999680f};

    // Main computation with ultimate instruction-level parallelism
    for (int iter = 0; iter < iterations; iter++)
    {

// Quad-vectorized FMA operations for maximum ALU utilization
#pragma unroll 4
        for (int batch = 0; batch < 4; batch++)
        {
            const int reg_base = batch * 4;
            const float k1 = fma_constants[batch * 2];
            const float k2 = fma_constants[batch * 2 + 1];
            const float k3 = fma_constants[batch * 2 + 32];
            const float k4 = fma_constants[batch * 2 + 33];

// Process 4 register quads with different constants
#pragma unroll
            for (int q = 0; q < 4 && (reg_base + q) < 16; q++)
            {
                r[reg_base + q][0] = __fmaf_rn(r[reg_base + q][0], k1, 0.00000001f);
                r[reg_base + q][1] = __fmaf_rn(r[reg_base + q][1], k2, 0.00000002f);
                r[reg_base + q][2] = __fmaf_rn(r[reg_base + q][2], k3, 0.00000003f);
                r[reg_base + q][3] = __fmaf_rn(r[reg_base + q][3], k4, 0.00000004f);
            }
        }

        // Advanced cross-register dependencies (every 8 iterations)
        if ((iter & 0x7) == 0)
        {
#pragma unroll 2
            for (int i = 1; i < 16; i += 8)
            {
                r[i][0] += r[i - 1][3] * 0.0000001f;
                r[i][1] += r[i - 1][2] * 0.0000001f;
                r[i][2] += r[i - 1][1] * 0.0000001f;
                r[i][3] += r[i - 1][0] * 0.0000001f;
            }
        }

        // Complex mathematical operations (every 16 iterations)
        if ((iter & 0xF) == 0)
        {
#pragma unroll 2
            for (int complex_batch = 0; complex_batch < 2; complex_batch++)
            {
                const int idx = complex_batch * 8 + 1;
                if (idx < 16)
                {
                    r[idx][0] = __fmaf_rn(r[idx][0], sqrtf(fabsf(r[idx][1]) + 1.0f), 0.0000001f);
                    r[idx][1] = __fmaf_rn(r[idx][1], 1.0f / (fabsf(r[idx][2]) + 1.0f), 0.0000001f);
                    r[idx][2] = __fmaf_rn(r[idx][2], sinf(r[idx][3] * 0.001f), 0.0000001f);
                    r[idx][3] = __fmaf_rn(r[idx][3], cosf(r[idx][0] * 0.001f), 0.0000001f);
                }
            }
        }
    }

    // Ultra-optimized reduction with maximum parallelism
    float quad_sums[4] = {0.0f};

#pragma unroll
    for (int quad = 0; quad < 4; quad++)
    {
#pragma unroll
        for (int i = quad * 4; i < (quad + 1) * 4 && i < 16; i++)
        {
            quad_sums[quad] += r[i][0] + r[i][1] + r[i][2] + r[i][3];
        }
    }

    // Final reduction with instruction interleaving
    float result = (quad_sums[0] * quad_sums[1]) + (quad_sums[2] * quad_sums[3]);

    data[tid] = result;
}

// ============================================================================
// EXACT COPY 5: xOneRegisterOptimizedKernel (30.30 TFLOPS) - FROM ULTIMA.CU
// ============================================================================

// Ultimate Kernel 5: Register-Optimized Maximum Throughput
__global__ void xOneRegisterOptimizedKernel(float *__restrict__ data, int size, int iterations)
{
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= size)
        return;

    float x = data[idx];

    // 32 register variables for maximum ILP
    register float r0 = x, r1 = x * 0.9f, r2 = x * 0.8f, r3 = x * 0.7f;
    register float r4 = x * 0.6f, r5 = x * 0.5f, r6 = x * 0.4f, r7 = x * 0.3f;
    register float r8 = x * 0.2f, r9 = x * 0.1f, r10 = x * 1.1f, r11 = x * 1.2f;
    register float r12 = x * 1.3f, r13 = x * 1.4f, r14 = x * 1.5f, r15 = x * 1.6f;
    register float r16 = x * 0.95f, r17 = x * 0.85f, r18 = x * 0.75f, r19 = x * 0.65f;
    register float r20 = x * 0.55f, r21 = x * 0.45f, r22 = x * 0.35f, r23 = x * 0.25f;
    register float r24 = x * 0.15f, r25 = x * 0.05f, r26 = x * 1.05f, r27 = x * 1.15f;
    register float r28 = x * 1.25f, r29 = x * 1.35f, r30 = x * 1.45f, r31 = x * 1.55f;

#pragma unroll 16
    for (int i = 0; i < iterations; ++i)
    {
        // 32 parallel FMA operations
        r0 = r0 * x + 1.0001f;
        r1 = r1 * x + 0.9999f;
        r2 = r2 * x + 1.0002f;
        r3 = r3 * x + 0.9998f;
        r4 = r4 * x + 1.0003f;
        r5 = r5 * x + 0.9997f;
        r6 = r6 * x + 1.0004f;
        r7 = r7 * x + 0.9996f;
        r8 = r8 * x + 1.0005f;
        r9 = r9 * x + 0.9995f;
        r10 = r10 * x + 1.0006f;
        r11 = r11 * x + 0.9994f;
        r12 = r12 * x + 1.0007f;
        r13 = r13 * x + 0.9993f;
        r14 = r14 * x + 1.0008f;
        r15 = r15 * x + 0.9992f;
        r16 = r16 * x + 1.0009f;
        r17 = r17 * x + 0.9991f;
        r18 = r18 * x + 1.0010f;
        r19 = r19 * x + 0.9990f;
        r20 = r20 * x + 1.0011f;
        r21 = r21 * x + 0.9989f;
        r22 = r22 * x + 1.0012f;
        r23 = r23 * x + 0.9988f;
        r24 = r24 * x + 1.0013f;
        r25 = r25 * x + 0.9987f;
        r26 = r26 * x + 1.0014f;
        r27 = r27 * x + 0.9986f;
        r28 = r28 * x + 1.0015f;
        r29 = r29 * x + 0.9985f;
        r30 = r30 * x + 1.0016f;
        r31 = r31 * x + 0.9984f;
    }

    // Reduction tree
    float result = (r0 + r1 + r2 + r3) * (r4 + r5 + r6 + r7) +
                   (r8 + r9 + r10 + r11) * (r12 + r13 + r14 + r15) +
                   (r16 + r17 + r18 + r19) * (r20 + r21 + r22 + r23) +
                   (r24 + r25 + r26 + r27) * (r28 + r29 + r30 + r31);

    data[idx] = result;
}

// EXACT BENCHMARK FUNCTION FROM ULTIMA.CU
double runSingleKernelBenchmark(const char *name, int kernelType, void *d_data,
                                int size, int iterations, int testRuns)
{
    std::cout << "=== " << name << " ===" << std::endl;

    int blockSize = 256;
    int gridSize = (size + blockSize - 1) / blockSize;

    // Handle special cases - EXACT from ultima.cu
    if (kernelType == 1)
    {                     // Tensor Core
        size = size / 16; // WMMA operates on 16x16 matrices
        gridSize = (size + blockSize - 1) / blockSize;
    }
    else if (kernelType == 3)
    {                    // Vector optimized (now FP16)
        size = size / 2; // half2 uses 1/2 the elements
        gridSize = (size + blockSize - 1) / blockSize;
    }

    std::cout << "Elements: " << size << std::endl;
    std::cout << "Grid size: " << gridSize << " blocks" << std::endl;
    std::cout << std::endl;

    // Warmup - EXACT from ultima.cu
    switch (kernelType)
    {
    case 1:
        xOneTensorCoreKernel<<<gridSize, blockSize>>>((half *)d_data, size * 16, iterations);
        break;
    case 2:
    {
        int total_blocks = gridSize * 4; // Increase work for persistent threads
        xOnePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
        break;
    }
    case 3:
        xOneVectorOptimizedKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
        break;
    case 4:
        xOneRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    case 5:
        xOneRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
        break;
    }
    CUDA_CHECK(cudaDeviceSynchronize());

    double totalTime = 0.0;

    for (int run = 0; run < testRuns; ++run)
    {
        auto start = std::chrono::high_resolution_clock::now();

        switch (kernelType)
        {
        case 1:
            xOneTensorCoreKernel<<<gridSize, blockSize>>>((half *)d_data, size * 16, iterations);
            break;
        case 2:
        {
            int total_blocks = gridSize * 4;
            xOnePersistentKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations, total_blocks);
            break;
        }
        case 3:
            xOneVectorOptimizedKernel<<<gridSize, blockSize>>>((half2 *)d_data, size, iterations);
            break;
        case 4:
            xOneRegisterSaturationKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        case 5:
            xOneRegisterOptimizedKernel<<<gridSize, blockSize>>>((float *)d_data, size, iterations);
            break;
        }

        CUDA_CHECK(cudaDeviceSynchronize());

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double milliseconds = duration.count() / 1000.0;

        totalTime += milliseconds;
        std::cout << "Run " << (run + 1) << ": "
                  << std::fixed << std::setprecision(2) << milliseconds << " ms" << std::endl;
    }

    return totalTime;
}

// Main benchmark execution function
int main()
{
    std::cout << "CUDA Performance Benchmark Suite - Multi-Kernel Evaluation" << std::endl;
    std::cout << "=================================================================" << std::endl;
    std::cout << "TARGET: 95+ TFLOPS Performance Evaluation" << std::endl;
    std::cout << "=================================================================" << std::endl;

    // Benchmark parameters
    const int size = 128 * 1024 * 1024; // 128M elements (512 MB)
    const int iterations = 600;         // Optimized for sustained throughput
    const int testRuns = 3;

    std::cout << "\nBenchmark Configuration:" << std::endl;
    std::cout << "  Array Size: " << size << " elements ("
              << (size * sizeof(float)) / (1024 * 1024) << " MB)" << std::endl;
    std::cout << "  Iterations: " << iterations << std::endl;
    std::cout << "  Test Runs: " << testRuns << std::endl;

    // Memory allocation
    half *h_half_data = new half[size];
    float *h_float_data = new float[size];
    half2 *h_half2_data = new half2[size / 2];

    // Initialize data - EXACT from ultima.cu
    for (int i = 0; i < size; i++)
    {
        h_half_data[i] = __float2half(1.0f + (i % 1000) * 0.0001f);
        h_float_data[i] = 1.0f + (i % 1000) * 0.0001f;
        if (i < size / 2)
        {
            h_half2_data[i] = __float2half2_rn(1.0f + (i % 1000) * 0.0001f);
        }
    }

    half *d_half_data;
    float *d_float_data;
    half2 *d_half2_data;

    CUDA_CHECK(cudaMalloc(&d_half_data, size * sizeof(half)));
    CUDA_CHECK(cudaMalloc(&d_float_data, size * sizeof(float)));
    CUDA_CHECK(cudaMalloc(&d_half2_data, (size / 2) * sizeof(half2)));

    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    std::cout << "\nExecuting kernel performance evaluation..." << std::endl;

    // Execute benchmark kernels
    double time1 = runSingleKernelBenchmark("TENSOR CORE WMMA", 1, d_half_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half_data, h_half_data, size * sizeof(half), cudaMemcpyHostToDevice));

    double time2 = runSingleKernelBenchmark("PERSISTENT THREADS", 2, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time3 = runSingleKernelBenchmark("VECTOR OPTIMIZED", 3, d_half2_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_half2_data, h_half2_data, (size / 2) * sizeof(half2), cudaMemcpyHostToDevice));

    double time4 = runSingleKernelBenchmark("REGISTER SATURATION", 4, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    double time5 = runSingleKernelBenchmark("REGISTER OPTIMIZED", 5, d_float_data, size, iterations, testRuns);
    CUDA_CHECK(cudaMemcpy(d_float_data, h_float_data, size * sizeof(float), cudaMemcpyHostToDevice));

    // EXACT TFLOPS CALCULATION FROM ULTIMA.CU AND Z-BEST.CU
    // Tensor Core: Each warp does one 16x16x16 WMMA per iteration
    // 16x16x16 = 4096 MAC operations = 8192 FLOPs
    int num_warps = (size / 16) / 32; // Elements per 16x16 matrix, divided by warp size
    double tensorOps = static_cast<double>(num_warps) * iterations * 8192.0 * testRuns;
    double tensorTFLOPS = tensorOps / (time1 / 1000.0) / 1e12;

    // Persistent: 16 FMA ops per iteration = 32 FLOPs per iteration per thread
    double persistentOps = static_cast<double>(size) * iterations * 32.0 * testRuns;
    double persistentTFLOPS = persistentOps / (time2 / 1000.0) / 1e12;

    // Vector: 2 FMA ops per iteration on half2 = 4 FLOPs per iteration per thread
    double vectorOps = static_cast<double>(size / 2) * iterations * 4.0 * testRuns;
    double vectorTFLOPS = vectorOps / (time3 / 1000.0) / 1e12;

    // Register Saturation: 64 registers * 4 FMA ops per iteration = 256 FLOPs per iteration per thread
    double registerSatOps = static_cast<double>(size) * iterations * 256.0 * testRuns;
    double registerSatTFLOPS = registerSatOps / (time4 / 1000.0) / 1e12;

    // Register Optimized: 32 FMA ops per iteration = 64 FLOPs per iteration per thread
    double registerOptOps = static_cast<double>(size) * iterations * 64.0 * testRuns;
    double registerOptTFLOPS = registerOptOps / (time5 / 1000.0) / 1e12;

    std::cout << "\n=================================================================" << std::endl;
    std::cout << "ALL 5 KERNEL PERFORMANCE RESULTS" << std::endl;
    std::cout << "=================================================================" << std::endl;
    std::cout << "Tensor Core WMMA: " << std::fixed << std::setprecision(2) << tensorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Persistent Threads: " << std::fixed << std::setprecision(2) << persistentTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Vector Optimized (FP16): " << std::fixed << std::setprecision(2) << vectorTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Register Saturation: " << std::fixed << std::setprecision(2) << registerSatTFLOPS << " TFLOPS" << std::endl;
    std::cout << "Register Optimized: " << std::fixed << std::setprecision(2) << registerOptTFLOPS << " TFLOPS" << std::endl;

    double max_tflops = std::max(tensorTFLOPS, std::max(persistentTFLOPS, std::max(vectorTFLOPS, std::max(registerSatTFLOPS, registerOptTFLOPS))));
    std::cout << "\nMAXIMUM ACHIEVED: " << std::fixed << std::setprecision(2) << max_tflops << " TFLOPS" << std::endl;

    if (max_tflops >= 100.0)
    {
        std::cout << "\nPerformance target exceeded: 100+ TFLOPS achieved" << std::endl;
        std::cout << "Benchmark execution successful" << std::endl;
    }
    else if (max_tflops >= 80.0)
    {
        std::cout << "\nPerformance target achieved: 80+ TFLOPS" << std::endl;
        std::cout << "Benchmark execution successful" << std::endl;
    }
    else
    {
        std::cout << "\nPerformance baseline established" << std::endl;
        std::cout << "Performance: " << std::fixed << std::setprecision(1) << (max_tflops / 100.0) * 100.0 << "% of 100 TFLOPS target" << std::endl;
    }

    // Cleanup
    CUDA_CHECK(cudaFree(d_half_data));
    CUDA_CHECK(cudaFree(d_float_data));
    CUDA_CHECK(cudaFree(d_half2_data));
    delete[] h_half_data;
    delete[] h_float_data;
    delete[] h_half2_data;

    std::cout << "\nBenchmark suite execution complete" << std::endl;

    return 0;
}
