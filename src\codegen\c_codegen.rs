// src/codegen/c_codegen.rs
//! C code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating C code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// C code generator
pub struct CCodeGenerator {
    // Configuration options can be added here
}

impl Default for CCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl CCodeGenerator {
    /// Create a new C code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate C type from argument type
    fn generate_c_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "void".to_string(),
            super::ArgType::I8 => "int8_t".to_string(),
            super::ArgType::I16 => "int16_t".to_string(),
            super::ArgType::I32 => "int32_t".to_string(),
            super::ArgType::I64 => "int64_t".to_string(),
            super::ArgType::U8 => "uint8_t".to_string(),
            super::ArgType::U16 => "uint16_t".to_string(),
            super::ArgType::U32 => "uint32_t".to_string(),
            super::ArgType::U64 => "uint64_t".to_string(),
            super::ArgType::F32 => "float".to_string(),
            super::ArgType::F64 => "double".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "int8_t*".to_string(),
            super::ArgType::I16Ptr => "int16_t*".to_string(),
            super::ArgType::I32Ptr => "int32_t*".to_string(),
            super::ArgType::I64Ptr => "int64_t*".to_string(),
            super::ArgType::U8Ptr => "uint8_t*".to_string(),
            super::ArgType::U16Ptr => "uint16_t*".to_string(),
            super::ArgType::U32Ptr => "uint32_t*".to_string(),
            super::ArgType::U64Ptr => "uint64_t*".to_string(),
            super::ArgType::F32Ptr => "float*".to_string(),
            super::ArgType::F64Ptr => "double*".to_string(),
            super::ArgType::BoolPtr => "bool*".to_string(),
            super::ArgType::VoidPtr => "void*".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate C function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_c_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        Ok(format!("{} {}({})", return_type, function.name, params))
    }
    
    /// Generate function declarations
    fn generate_function_declarations(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        for function in functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration
            result.push_str(&format!("extern {signature};\n"));
        }
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("{{{}, {}, {}}}", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("{{{}, {}, {}}}", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "{1, 1, 1}".to_string(),
                "{256, 1, 1}".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch the CUDA kernel `{function_name}`
 */
void launch_{function_name}({params}) {{
    extern void {function_name}({params});
    
    // Launch parameters
    const uint32_t grid_dim[3] = {grid_dim};
    const uint32_t block_dim[3] = {block_dim};
    const size_t shared_mem = {shared_mem};
    
    // Actual CUDA kernel launch implementation
    #ifdef __CUDACC__
    // When compiled with nvcc, we can use the CUDA runtime API directly
    // Set up kernel launch configuration
    dim3 gridDim(grid_dim[0], grid_dim[1], grid_dim[2]);
    dim3 blockDim(block_dim[0], block_dim[1], block_dim[2]);

    // Prepare argument array
    void* kernel_args[] = {{{args}}};

    // Launch kernel using cudaLaunchKernel
    cudaError_t err = cudaLaunchKernel(
        (void*){function_name},
        gridDim,
        blockDim,
        kernel_args,
        shared_mem,
        0  // stream
    );

    if (err != cudaSuccess) {{
        fprintf(stderr, "CUDA kernel launch failed: %s\\n", cudaGetErrorString(err));
        return;
    }}

    // Optionally synchronize to catch execution errors
    err = cudaDeviceSynchronize();
    if (err != cudaSuccess) {{
        fprintf(stderr, "CUDA kernel execution failed: %s\\n", cudaGetErrorString(err));
    }}
    #else
    // Fallback to runtime wrapper when not compiled with nvcc
    cuda_runtime_launch_kernel(
        (void*){function_name},
        grid_dim,
        block_dim,
        shared_mem,
        (void*[]){{{args}}},
        {arg_count}
    );
    #endif
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("&arg{i}"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
            arg_count = if params == "void" { 0 } else { params.split(", ").count() },
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/**
 * Argument type enumeration
 */
typedef enum {
    OMNI_ARG_VOID,
    OMNI_ARG_I8,
    OMNI_ARG_I16,
    OMNI_ARG_I32,
    OMNI_ARG_I64,
    OMNI_ARG_U8,
    OMNI_ARG_U16,
    OMNI_ARG_U32,
    OMNI_ARG_U64,
    OMNI_ARG_F32,
    OMNI_ARG_F64,
    OMNI_ARG_BOOL,
    OMNI_ARG_I8_PTR,
    OMNI_ARG_I16_PTR,
    OMNI_ARG_I32_PTR,
    OMNI_ARG_I64_PTR,
    OMNI_ARG_U8_PTR,
    OMNI_ARG_U16_PTR,
    OMNI_ARG_U32_PTR,
    OMNI_ARG_U64_PTR,
    OMNI_ARG_F32_PTR,
    OMNI_ARG_F64_PTR,
    OMNI_ARG_BOOL_PTR,
    OMNI_ARG_VOID_PTR
} OmniArgType;

/**
 * Target type enumeration
 */
typedef enum {
    OMNI_TARGET_CPU,
    OMNI_TARGET_GPU,
    OMNI_TARGET_CPU_SIMD,
    OMNI_TARGET_TPU,
    OMNI_TARGET_FPGA,
    OMNI_TARGET_OTHER
} OmniTargetType;

/**
 * Compute metadata structure
 */
typedef struct {
    uint32_t grid_size[3];
    uint32_t block_size[3];
    size_t shared_mem;
    const OmniArgType* args_layout;
    size_t args_count;
} OmniComputeMetadata;

/**
 * OmniCodex entry structure
 */
typedef struct {
    const char* name;
    OmniTargetType target_type;
    void (*function_ptr)(void);
    OmniComputeMetadata metadata;
} OmniCodexEntry;
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> (String, String) {
        if entry.metadata.arg_layout.is_empty() {
            return ("NULL".to_string(), "0".to_string());
        }
        
        let var_name = format!("{}_args", entry.name);
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| match arg {
                super::ArgType::Void => "OMNI_ARG_VOID",
                super::ArgType::I8 => "OMNI_ARG_I8",
                super::ArgType::I16 => "OMNI_ARG_I16",
                super::ArgType::I32 => "OMNI_ARG_I32",
                super::ArgType::I64 => "OMNI_ARG_I64",
                super::ArgType::U8 => "OMNI_ARG_U8",
                super::ArgType::U16 => "OMNI_ARG_U16",
                super::ArgType::U32 => "OMNI_ARG_U32",
                super::ArgType::U64 => "OMNI_ARG_U64",
                super::ArgType::F32 => "OMNI_ARG_F32",
                super::ArgType::F64 => "OMNI_ARG_F64",
                super::ArgType::Bool => "OMNI_ARG_BOOL",
                super::ArgType::I8Ptr => "OMNI_ARG_I8_PTR",
                super::ArgType::I16Ptr => "OMNI_ARG_I16_PTR",
                super::ArgType::I32Ptr => "OMNI_ARG_I32_PTR",
                super::ArgType::I64Ptr => "OMNI_ARG_I64_PTR",
                super::ArgType::U8Ptr => "OMNI_ARG_U8_PTR",
                super::ArgType::U16Ptr => "OMNI_ARG_U16_PTR",
                super::ArgType::U32Ptr => "OMNI_ARG_U32_PTR",
                super::ArgType::U64Ptr => "OMNI_ARG_U64_PTR",
                super::ArgType::F32Ptr => "OMNI_ARG_F32_PTR",
                super::ArgType::F64Ptr => "OMNI_ARG_F64_PTR",
                super::ArgType::BoolPtr => "OMNI_ARG_BOOL_PTR",
                super::ArgType::VoidPtr => "OMNI_ARG_VOID_PTR",
                super::ArgType::Custom(_) => "OMNI_ARG_VOID_PTR", // Default to void* for custom types
            })
            .collect::<Vec<_>>()
            .join(", ");
        
        let array_def = format!("static const OmniArgType {var_name}[] = {{ {arg_types} }};");
        
        (var_name, array_def)
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        // Generate arg type arrays
        let mut arg_arrays = String::new();
        let mut arg_vars = HashMap::new();
        
        for entry in entries {
            let (var_name, array_def) = self.generate_arg_types(entry);
            if var_name != "NULL" {
                arg_arrays.push_str(&format!("{array_def}\n"));
                arg_vars.insert(entry.name.clone(), var_name);
            }
        }
        
        result.push_str(&arg_arrays);
        result.push('\n');
        
        // Generate table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("static const OmniCodexEntry OMNI_CODEX[] = {\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("{{{}, {}, {}}}", grid[0], grid[1], grid[2]),
                    format!("{{{}, {}, {}}}", block[0], block[1], block[2]),
                )
            } else {
                ("{1, 1, 1}".to_string(), "{256, 1, 1}".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = arg_vars.get(&entry.name).map_or("NULL".to_string(), |v| v.clone());
            let args_count = entry.metadata.arg_layout.len();
            
            // Generate target type
            let target_type = match entry.target_type {
                super::TargetType::CPU => "OMNI_TARGET_CPU",
                super::TargetType::GPU => "OMNI_TARGET_GPU",
                super::TargetType::CPUSIMD => "OMNI_TARGET_CPU_SIMD",
                super::TargetType::TPU => "OMNI_TARGET_TPU",
                super::TargetType::FPGA => "OMNI_TARGET_FPGA",
                super::TargetType::Other => "OMNI_TARGET_OTHER",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    {{
        /* name */ "{}",
        /* target_type */ {},
        /* function_ptr */ (void(*)())&{},
        /* metadata */ {{
            /* grid_size */ {},
            /* block_size */ {},
            /* shared_mem */ {},
            /* args_layout */ {},
            /* args_count */ {}
        }}
    }},
"#,
                entry.name,
                target_type,
                entry.function_pointer,
                grid_size,
                block_size,
                shared_mem,
                args_layout,
                args_count,
            ));
        }
        
        result.push_str("};\n\n");
        result.push_str("/**\n");
        result.push_str(" * Number of entries in the OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str(&format!("static const size_t OMNI_CODEX_SIZE = {};\n", entries.len()));
        
        result
    }
    
    /// Generate file header
    fn generate_file_header(&self) -> String {
        r#"/**
 * OmniCodex dispatch table generated by OmniForge.
 *
 * This file contains the OmniCodex dispatch table, which provides a zero-cost
 * abstraction for heterogeneous computing. It allows calling functions on
 * different backends (CPU, GPU, etc.) with a unified interface.
 */

#ifndef OMNICODEX_H
#define OMNICODEX_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

"#.to_string()
    }
    
    /// Generate file footer
    fn generate_file_footer(&self) -> String {
        r#"
#ifdef __cplusplus
} // extern "C"
#endif

#endif // OMNICODEX_H
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
static const OmniCodexEntry* omni_find_function(const char* name) {
    for (size_t i = 0; i < OMNI_CODEX_SIZE; i++) {
        if (strcmp(OMNI_CODEX[i].name, name) == 0) {
            return &OMNI_CODEX[i];
        }
    }
    return NULL;
}

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result) {
    // Find the function in the dispatch table
    const OmniCodexEntry* entry = omni_find_function(name);
    if (entry == NULL) {
        return OMNI_ERROR_FUNCTION_NOT_FOUND;
    }
    
    // Check argument count
    if (args_count != entry->metadata.args_count) {
        return OMNI_ERROR_ARGUMENT_COUNT_MISMATCH;
    }
    
    // Actual function execution implementation
    // Cast the function pointer to the appropriate type and call it
    switch (entry->target_type) {
        case OMNI_TARGET_CPU: {
            // CPU function execution
            // Cast to a generic function pointer that takes void** arguments
            void (*cpu_func)(void**) = (void (*)(void**))(entry->function_ptr);
            cpu_func(args);
            break;
        }
        case OMNI_TARGET_GPU: {
            // GPU/CUDA function execution
            // For GPU functions, we need to handle device memory and kernel launches
            void (*gpu_func)(void**) = (void (*)(void**))(entry->function_ptr);
            gpu_func(args);
            break;
        }
        case OMNI_TARGET_HYBRID: {
            // Hybrid execution - decide based on data size or other heuristics
            void (*hybrid_func)(void**) = (void (*)(void**))(entry->function_ptr);
            hybrid_func(args);
            break;
        }
        default:
            return OMNI_ERROR_UNSUPPORTED_TARGET;
    }

    // If we reach here, execution was successful
    return OMNI_ERROR_NONE;
}

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error) {
    switch (error) {
        case OMNI_ERROR_NONE:
            return "No error";
        case OMNI_ERROR_FUNCTION_NOT_FOUND:
            return "Function not found";
        case OMNI_ERROR_ARGUMENT_COUNT_MISMATCH:
            return "Argument count mismatch";
        case OMNI_ERROR_ARGUMENT_TYPE_MISMATCH:
            return "Argument type mismatch";
        case OMNI_ERROR_NOT_IMPLEMENTED:
            return "Not implemented";
        default:
            return "Unknown error";
    }
}

/**
 * CUDA runtime functions
 */

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
) {
    // This is a placeholder for the actual CUDA kernel launch
    // In a real implementation, this would call the CUDA runtime API
}
"#.to_string()
    }
    
    
    
    /// Generate header file
    fn generate_header(&self, entries: &[CodexEntry], functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut header = String::new();
        
        // Generate file header
        header.push_str(&self.generate_file_header());
        header.push('\n');
        
        // Generate imports
        header.push_str(r"#include <string.h>
#include <stdio.h>
#include <stdlib.h>
");
        header.push_str("\n\n");
        
        // Generate struct definitions
        header.push_str(&self.generate_codex_entry_struct());
        header.push('\n');
        
        // Generate function declarations
        header.push_str(&self.generate_function_declarations(functions)?);
        header.push('\n');

        // Generate kernel launchers
        for function in functions {
            header.push_str(&self.generate_kernel_launcher(function)?);
        }
        header.push('\n');

        // Generate helper function declarations
        header.push_str(r"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
const OmniCodexEntry* omni_find_function(const char* name);

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result);

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error);

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
);

/**
 * OmniCodex dispatch table
 */
extern const OmniCodexEntry OMNI_CODEX[];

/**
 * Number of entries in the OmniCodex dispatch table
 */
extern const size_t OMNI_CODEX_SIZE;
");
        
        // Function declarations
        for entry in entries {
            if entry.target_type == super::TargetType::GPU {
                header.push_str(&format!("void {}(void);\n", entry.function_pointer));
            }
        }
        
        // Generate file footer
        header.push_str(&self.generate_file_footer());
        
        Ok(header)
    }
}

impl CodeGenerator for CCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating C OmniCodex");
        
        // Generate header
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        let all_functions: Vec<_> = metadata.iter().flat_map(|m| &m.functions).cloned().collect();

        // Generate header file
        let header_code = self.generate_header(&entries, &all_functions)?;
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: Some(header_code),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating C wrappers");
        
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let c_return_type = self.generate_c_type(&return_type);
                    
                    // Extract parameter types
                    let params = if signature.parameter_types.is_empty() {
                        "void".to_string()
                    } else {
                        signature
                            .parameter_types
                            .iter()
                            .enumerate()
                            .map(|(i, param)| {
                                let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                                let c_type = self.generate_c_type(&arg_type);
                                format!("{c_type} arg{i}")
                            })
                            .collect::<Vec<_>>()
                            .join(", ")
                    };
                    
                    // Generate wrapper function
                    let func_name = format!("omni_{}", function.name.to_lowercase());
                    
                    code.push_str(&format!(
                        r#"/**
 * Wrapper for the `{}` function
 */
OmniError {}({}{}) {{
    {}
    void* args[] = {{{}}};
    return omni_execute("{}", args, {}, {});
}}

"#,
                        function.name,
                        func_name,
                        if params == "void" { "".to_string() } else { format!("{params}, ") },
                        if c_return_type == "void" { "void".to_string() } else { format!("{c_return_type} *result") },
                        if c_return_type == "void" { "".to_string() } else { "if (result == NULL) return OMNI_ERROR_ARGUMENT_TYPE_MISMATCH;".to_string() },
                        if params == "void" { "".to_string() } else {
                            (0..signature.parameter_types.len())
                                .map(|i| format!("&arg{i}"))
                                .collect::<Vec<_>>()
                                .join(", ")
                        },
                        function.name,
                        if params == "void" { 0 } else { signature.parameter_types.len() },
                        if c_return_type == "void" { "NULL" } else { "result" },
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        Ok(code)
    }
}
