// src/codegen/c_codegen.rs
//! C code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating C code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// C code generator
pub struct CCodeGenerator {
    // Configuration options can be added here
}

impl Default for CCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl CCodeGenerator {
    /// Create a new C code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate C type from argument type
    fn generate_c_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "void".to_string(),
            super::ArgType::I8 => "int8_t".to_string(),
            super::ArgType::I16 => "int16_t".to_string(),
            super::ArgType::I32 => "int32_t".to_string(),
            super::ArgType::I64 => "int64_t".to_string(),
            super::ArgType::U8 => "uint8_t".to_string(),
            super::ArgType::U16 => "uint16_t".to_string(),
            super::ArgType::U32 => "uint32_t".to_string(),
            super::ArgType::U64 => "uint64_t".to_string(),
            super::ArgType::F32 => "float".to_string(),
            super::ArgType::F64 => "double".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "int8_t*".to_string(),
            super::ArgType::I16Ptr => "int16_t*".to_string(),
            super::ArgType::I32Ptr => "int32_t*".to_string(),
            super::ArgType::I64Ptr => "int64_t*".to_string(),
            super::ArgType::U8Ptr => "uint8_t*".to_string(),
            super::ArgType::U16Ptr => "uint16_t*".to_string(),
            super::ArgType::U32Ptr => "uint32_t*".to_string(),
            super::ArgType::U64Ptr => "uint64_t*".to_string(),
            super::ArgType::F32Ptr => "float*".to_string(),
            super::ArgType::F64Ptr => "double*".to_string(),
            super::ArgType::BoolPtr => "bool*".to_string(),
            super::ArgType::VoidPtr => "void*".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate C function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_c_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        Ok(format!("{} {}({})", return_type, function.name, params))
    }
    
    /// Generate function declarations
    fn generate_function_declarations(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        for function in functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration
            result.push_str(&format!("extern {signature};\n"));
        }
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("{{{}, {}, {}}}", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("{{{}, {}, {}}}", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "{1, 1, 1}".to_string(),
                "{256, 1, 1}".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch the CUDA kernel `{function_name}`
 */
void launch_{function_name}({params}) {{
    extern void {function_name}({params});
    
    // Launch parameters
    const uint32_t grid_dim[3] = {grid_dim};
    const uint32_t block_dim[3] = {block_dim};
    const size_t shared_mem = {shared_mem};
    
    // Advanced CUDA kernel launch implementation with comprehensive validation
    #ifdef __CUDACC__
    {
        // Validate CUDA runtime initialization
        int device_count;
        cudaError_t device_count_err = cudaGetDeviceCount(&device_count);
        if (device_count_err != cudaSuccess || device_count == 0) {
            fprintf(stderr, "CUDA runtime error: No devices available (%s)\n", 
                    cudaGetErrorString(device_count_err));
            return;
        }

        // Get current device properties for validation
        int device_id;
        cudaError_t device_err = cudaGetDevice(&device_id);
        if (device_err != cudaSuccess) {
            fprintf(stderr, "Failed to get current CUDA device: %s\n", 
                    cudaGetErrorString(device_err));
            return;
        }

        struct cudaDeviceProp device_props;
        cudaError_t props_err = cudaGetDeviceProperties(&device_props, device_id);
        if (props_err != cudaSuccess) {
            fprintf(stderr, "Failed to get device properties: %s\n", 
                    cudaGetErrorString(props_err));
            return;
        }

        // Comprehensive launch parameter validation
        if (grid_dim[0] > (uint32_t)device_props.maxGridSize[0] ||
            grid_dim[1] > (uint32_t)device_props.maxGridSize[1] ||
            grid_dim[2] > (uint32_t)device_props.maxGridSize[2]) {
            fprintf(stderr, "Grid dimensions (%u,%u,%u) exceed device limits (%d,%d,%d)\n",
                    grid_dim[0], grid_dim[1], grid_dim[2],
                    device_props.maxGridSize[0], device_props.maxGridSize[1], device_props.maxGridSize[2]);
            return;
        }

        if (block_dim[0] > (uint32_t)device_props.maxThreadsDim[0] ||
            block_dim[1] > (uint32_t)device_props.maxThreadsDim[1] ||
            block_dim[2] > (uint32_t)device_props.maxThreadsDim[2]) {
            fprintf(stderr, "Block dimensions (%u,%u,%u) exceed device limits (%d,%d,%d)\n",
                    block_dim[0], block_dim[1], block_dim[2],
                    device_props.maxThreadsDim[0], device_props.maxThreadsDim[1], device_props.maxThreadsDim[2]);
            return;
        }

        uint32_t total_threads = block_dim[0] * block_dim[1] * block_dim[2];
        if (total_threads > (uint32_t)device_props.maxThreadsPerBlock) {
            fprintf(stderr, "Total threads per block (%u) exceeds device limit (%d)\n",
                    total_threads, device_props.maxThreadsPerBlock);
            return;
        }

        if (shared_mem > (size_t)device_props.sharedMemPerBlock) {
            fprintf(stderr, "Shared memory usage (%zu bytes) exceeds device limit (%zu bytes)\n",
                    shared_mem, (size_t)device_props.sharedMemPerBlock);
            return;
        }

        // Create CUDA events for performance monitoring
        cudaEvent_t start_event, stop_event;
        cudaError_t event_err;
        
        event_err = cudaEventCreate(&start_event);
        if (event_err != cudaSuccess) {
            fprintf(stderr, "Failed to create start event: %s\n", cudaGetErrorString(event_err));
            return;
        }
        
        event_err = cudaEventCreate(&stop_event);
        if (event_err != cudaSuccess) {
            cudaEventDestroy(start_event);
            fprintf(stderr, "Failed to create stop event: %s\n", cudaGetErrorString(event_err));
            return;
        }

        // Record start time
        cudaEventRecord(start_event, 0);

        // Set up optimized launch configuration
        dim3 gridDim = {grid_dim[0], grid_dim[1], grid_dim[2]};
        dim3 blockDim = {block_dim[0], block_dim[1], block_dim[2]};

        // Prepare kernel arguments with proper alignment
        void* kernel_args[] = {args};

        // Launch kernel with comprehensive error checking
        cudaError_t launch_err = cudaLaunchKernel(
            (void*)function_name,
            gridDim,
            blockDim,
            kernel_args,
            shared_mem,
            0  // Default stream
        );

        // Record stop time immediately after launch
        cudaEventRecord(stop_event, 0);

        if (launch_err != cudaSuccess) {
            cudaEventDestroy(start_event);
            cudaEventDestroy(stop_event);
            fprintf(stderr, "CUDA kernel launch failed for %s: %s\n", 
                    "{function_name}", cudaGetErrorString(launch_err));
            return;
        }

        // Synchronize and check for execution errors
        cudaError_t sync_err = cudaDeviceSynchronize();
        if (sync_err != cudaSuccess) {
            cudaEventDestroy(start_event);
            cudaEventDestroy(stop_event);
            fprintf(stderr, "CUDA kernel execution failed for %s: %s\n", 
                    "{function_name}", cudaGetErrorString(sync_err));
            return;
        }

        // Calculate and report execution time
        cudaEventSynchronize(stop_event);
        float execution_time_ms;
        cudaError_t time_err = cudaEventElapsedTime(&execution_time_ms, start_event, stop_event);
        if (time_err == cudaSuccess) {
            #ifdef OMNI_DEBUG
            printf("Kernel %s executed successfully in %.3f ms\n", 
                   "{function_name}", execution_time_ms);
            #endif
        }

        // Cleanup events
        cudaEventDestroy(start_event);
        cudaEventDestroy(stop_event);
    }
    #else
    // Advanced fallback implementation using CUDA runtime wrapper
    {
        // Validate input parameters
        if (!function_name) {
            fprintf(stderr, "Error: Null function pointer for kernel launch\n");
            return;
        }

        if (arg_count > 0 && !args) {
            fprintf(stderr, "Error: Null arguments array with non-zero argument count\n");
            return;
        }

        // Check for CUDA runtime availability
        static int runtime_checked = 0;
        static int runtime_available = 0;
        
        if (!runtime_checked) {
            int device_count;
            if (cudaGetDeviceCount(&device_count) == cudaSuccess && device_count > 0) {
                runtime_available = 1;
            }
            runtime_checked = 1;
        }

        if (!runtime_available) {
            fprintf(stderr, "Error: CUDA runtime not available for kernel %s\n", "{function_name}");
            return;
        }

        // Enhanced runtime kernel launch
        cuda_runtime_launch_kernel_advanced(
            (void*)function_name,
            grid_dim,
            block_dim,
            shared_mem,
            (void**)args,
            arg_count,
            "{function_name}"  // Function name for debugging
        );
    }
    #endif
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("&arg{i}"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
            arg_count = if params == "void" { 0 } else { params.split(", ").count() },
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/**
 * Argument type enumeration
 */
typedef enum {
    OMNI_ARG_VOID,
    OMNI_ARG_I8,
    OMNI_ARG_I16,
    OMNI_ARG_I32,
    OMNI_ARG_I64,
    OMNI_ARG_U8,
    OMNI_ARG_U16,
    OMNI_ARG_U32,
    OMNI_ARG_U64,
    OMNI_ARG_F32,
    OMNI_ARG_F64,
    OMNI_ARG_BOOL,
    OMNI_ARG_I8_PTR,
    OMNI_ARG_I16_PTR,
    OMNI_ARG_I32_PTR,
    OMNI_ARG_I64_PTR,
    OMNI_ARG_U8_PTR,
    OMNI_ARG_U16_PTR,
    OMNI_ARG_U32_PTR,
    OMNI_ARG_U64_PTR,
    OMNI_ARG_F32_PTR,
    OMNI_ARG_F64_PTR,
    OMNI_ARG_BOOL_PTR,
    OMNI_ARG_VOID_PTR
} OmniArgType;

/**
 * Target type enumeration
 */
typedef enum {
    OMNI_TARGET_CPU,
    OMNI_TARGET_GPU,
    OMNI_TARGET_CPU_SIMD,
    OMNI_TARGET_TPU,
    OMNI_TARGET_FPGA,
    OMNI_TARGET_OTHER
} OmniTargetType;

/**
 * Compute metadata structure
 */
typedef struct {
    uint32_t grid_size[3];
    uint32_t block_size[3];
    size_t shared_mem;
    const OmniArgType* args_layout;
    size_t args_count;
} OmniComputeMetadata;

/**
 * OmniCodex entry structure
 */
typedef struct {
    const char* name;
    OmniTargetType target_type;
    void (*function_ptr)(void);
    OmniComputeMetadata metadata;
} OmniCodexEntry;
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> (String, String) {
        if entry.metadata.arg_layout.is_empty() {
            return ("NULL".to_string(), "0".to_string());
        }
        
        let var_name = format!("{}_args", entry.name);
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| match arg {
                super::ArgType::Void => "OMNI_ARG_VOID",
                super::ArgType::I8 => "OMNI_ARG_I8",
                super::ArgType::I16 => "OMNI_ARG_I16",
                super::ArgType::I32 => "OMNI_ARG_I32",
                super::ArgType::I64 => "OMNI_ARG_I64",
                super::ArgType::U8 => "OMNI_ARG_U8",
                super::ArgType::U16 => "OMNI_ARG_U16",
                super::ArgType::U32 => "OMNI_ARG_U32",
                super::ArgType::U64 => "OMNI_ARG_U64",
                super::ArgType::F32 => "OMNI_ARG_F32",
                super::ArgType::F64 => "OMNI_ARG_F64",
                super::ArgType::Bool => "OMNI_ARG_BOOL",
                super::ArgType::I8Ptr => "OMNI_ARG_I8_PTR",
                super::ArgType::I16Ptr => "OMNI_ARG_I16_PTR",
                super::ArgType::I32Ptr => "OMNI_ARG_I32_PTR",
                super::ArgType::I64Ptr => "OMNI_ARG_I64_PTR",
                super::ArgType::U8Ptr => "OMNI_ARG_U8_PTR",
                super::ArgType::U16Ptr => "OMNI_ARG_U16_PTR",
                super::ArgType::U32Ptr => "OMNI_ARG_U32_PTR",
                super::ArgType::U64Ptr => "OMNI_ARG_U64_PTR",
                super::ArgType::F32Ptr => "OMNI_ARG_F32_PTR",
                super::ArgType::F64Ptr => "OMNI_ARG_F64_PTR",
                super::ArgType::BoolPtr => "OMNI_ARG_BOOL_PTR",
                super::ArgType::VoidPtr => "OMNI_ARG_VOID_PTR",
                super::ArgType::Custom(_) => "OMNI_ARG_VOID_PTR", // Default to void* for custom types
            })
            .collect::<Vec<_>>()
            .join(", ");
        
        let array_def = format!("static const OmniArgType {var_name}[] = {{ {arg_types} }};");
        
        (var_name, array_def)
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        // Generate arg type arrays
        let mut arg_arrays = String::new();
        let mut arg_vars = HashMap::new();
        
        for entry in entries {
            let (var_name, array_def) = self.generate_arg_types(entry);
            if var_name != "NULL" {
                arg_arrays.push_str(&format!("{array_def}\n"));
                arg_vars.insert(entry.name.clone(), var_name);
            }
        }
        
        result.push_str(&arg_arrays);
        result.push('\n');
        
        // Generate table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("static const OmniCodexEntry OMNI_CODEX[] = {\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("{{{}, {}, {}}}", grid[0], grid[1], grid[2]),
                    format!("{{{}, {}, {}}}", block[0], block[1], block[2]),
                )
            } else {
                ("{1, 1, 1}".to_string(), "{256, 1, 1}".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = arg_vars.get(&entry.name).map_or("NULL".to_string(), |v| v.clone());
            let args_count = entry.metadata.arg_layout.len();
            
            // Generate target type
            let target_type = match entry.target_type {
                super::TargetType::CPU => "OMNI_TARGET_CPU",
                super::TargetType::GPU => "OMNI_TARGET_GPU",
                super::TargetType::CPUSIMD => "OMNI_TARGET_CPU_SIMD",
                super::TargetType::TPU => "OMNI_TARGET_TPU",
                super::TargetType::FPGA => "OMNI_TARGET_FPGA",
                super::TargetType::Other => "OMNI_TARGET_OTHER",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    {{
        /* name */ "{}",
        /* target_type */ {},
        /* function_ptr */ (void(*)())&{},
        /* metadata */ {{
            /* grid_size */ {},
            /* block_size */ {},
            /* shared_mem */ {},
            /* args_layout */ {},
            /* args_count */ {}
        }}
    }},
"#,
                entry.name,
                target_type,
                entry.function_pointer,
                grid_size,
                block_size,
                shared_mem,
                args_layout,
                args_count,
            ));
        }
        
        result.push_str("};\n\n");
        result.push_str("/**\n");
        result.push_str(" * Number of entries in the OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str(&format!("static const size_t OMNI_CODEX_SIZE = {};\n", entries.len()));
        
        result
    }
    
    /// Generate file header
    fn generate_file_header(&self) -> String {
        r#"/**
 * OmniCodex dispatch table generated by OmniForge.
 *
 * This file contains the OmniCodex dispatch table, which provides a zero-cost
 * abstraction for heterogeneous computing. It allows calling functions on
 * different backends (CPU, GPU, etc.) with a unified interface.
 */

#ifndef OMNICODEX_H
#define OMNICODEX_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

"#.to_string()
    }
    
    /// Generate file footer
    fn generate_file_footer(&self) -> String {
        r#"
#ifdef __cplusplus
} // extern "C"
#endif

#endif // OMNICODEX_H
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
static const OmniCodexEntry* omni_find_function(const char* name) {
    for (size_t i = 0; i < OMNI_CODEX_SIZE; i++) {
        if (strcmp(OMNI_CODEX[i].name, name) == 0) {
            return &OMNI_CODEX[i];
        }
    }
    return NULL;
}

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result) {
    // Find the function in the dispatch table
    const OmniCodexEntry* entry = omni_find_function(name);
    if (entry == NULL) {
        return OMNI_ERROR_FUNCTION_NOT_FOUND;
    }
    
    // Check argument count
    if (args_count != entry->metadata.args_count) {
        return OMNI_ERROR_ARGUMENT_COUNT_MISMATCH;
    }
    
    // Advanced function execution implementation with comprehensive error handling
    {
        // Pre-execution validation and timing
        struct timespec execution_start, execution_end;
        clock_gettime(CLOCK_MONOTONIC, &execution_start);

        // Validate function pointer
        if (!entry->function_ptr) {
            return OMNI_ERROR_INVALID_FUNCTION_POINTER;
        }

        switch (entry->target_type) {
            case OMNI_TARGET_CPU: {
                // CPU function execution with optimization hints
                #ifdef __GNUC__
                __builtin_prefetch(entry->function_ptr, 0, 3); // Prefetch function code
                #endif

                // Set CPU affinity for better cache performance
                #ifdef __linux__
                cpu_set_t cpuset;
                CPU_ZERO(&cpuset);
                CPU_SET(0, &cpuset);  // Bind to CPU 0
                int affinity_result = sched_setaffinity(0, sizeof(cpu_set_t), &cpuset);
                if (affinity_result != 0) {
                    // Log warning but continue execution
                    fprintf(stderr, "Warning: Failed to set CPU affinity\n");
                }
                #endif

                // Execute with proper calling convention handling
                const OmniComputeMetadata* metadata = &entry->metadata;
                if (metadata->args_count == 0) {
                    // No arguments - direct function call
                    void (*cpu_func_void)(void) = (void (*)(void))(entry->function_ptr);
                    cpu_func_void();
                } else if (metadata->args_count <= 8) {
                    // Small number of arguments - direct parameter passing
                    switch (metadata->args_count) {
                        case 1: {
                            void (*cpu_func_1)(void*) = (void (*)(void*))(entry->function_ptr);
                            cpu_func_1(args[0]);
                            break;
                        }
                        case 2: {
                            void (*cpu_func_2)(void*, void*) = (void (*)(void*, void*))(entry->function_ptr);
                            cpu_func_2(args[0], args[1]);
                            break;
                        }
                        case 3: {
                            void (*cpu_func_3)(void*, void*, void*) = (void (*)(void*, void*, void*))(entry->function_ptr);
                            cpu_func_3(args[0], args[1], args[2]);
                            break;
                        }
                        case 4: {
                            void (*cpu_func_4)(void*, void*, void*, void*) = 
                                (void (*)(void*, void*, void*, void*))(entry->function_ptr);
                            cpu_func_4(args[0], args[1], args[2], args[3]);
                            break;
                        }
                        default: {
                            // Fallback to array-based calling
                            void (*cpu_func)(void**) = (void (*)(void**))(entry->function_ptr);
                            cpu_func(args);
                            break;
                        }
                    }
                } else {
                    // Large number of arguments - use array-based approach
                    void (*cpu_func)(void**) = (void (*)(void**))(entry->function_ptr);
                    cpu_func(args);
                }
                break;
            }

            case OMNI_TARGET_GPU: {
                // Advanced GPU/CUDA function execution with memory management
                
                // Verify CUDA context
                CUcontext current_context;
                CUresult context_result = cuCtxGetCurrent(&current_context);
                if (context_result != CUDA_SUCCESS || !current_context) {
                    return OMNI_ERROR_CUDA_CONTEXT_ERROR;
                }

                // Allocate and manage device memory for arguments
                CUdeviceptr* device_ptrs = malloc(args_count * sizeof(CUdeviceptr));
                if (!device_ptrs) {
                    return OMNI_ERROR_MEMORY_ALLOCATION;
                }

                // Initialize device pointers array
                memset(device_ptrs, 0, args_count * sizeof(CUdeviceptr));

                // Transfer arguments to device memory
                for (size_t i = 0; i < args_count; i++) {
                    if (i >= entry->metadata.args_count) {
                        break; // Safety check
                    }

                    OmniArgType arg_type = entry->metadata.args_layout[i];
                    size_t arg_size = omni_get_type_size(arg_type);

                    // Allocate device memory
                    CUresult alloc_result = cuMemAlloc(&device_ptrs[i], arg_size);
                    if (alloc_result != CUDA_SUCCESS) {
                        // Cleanup previously allocated memory
                        for (size_t j = 0; j < i; j++) {
                            if (device_ptrs[j]) {
                                cuMemFree(device_ptrs[j]);
                            }
                        }
                        free(device_ptrs);
                        return OMNI_ERROR_CUDA_MEMORY_ERROR;
                    }

                    // Copy input data to device
                    if (omni_is_input_argument(arg_type)) {
                        CUresult copy_result = cuMemcpyHtoD(device_ptrs[i], args[i], arg_size);
                        if (copy_result != CUDA_SUCCESS) {
                            // Cleanup all allocated memory
                            for (size_t j = 0; j <= i; j++) {
                                if (device_ptrs[j]) {
                                    cuMemFree(device_ptrs[j]);
                                }
                            }
                            free(device_ptrs);
                            return OMNI_ERROR_CUDA_MEMORY_ERROR;
                        }
                    }
                }

                // Prepare kernel launch parameters
                const OmniComputeMetadata* metadata = &entry->metadata;
                void** kernel_args = malloc(args_count * sizeof(void*));
                if (!kernel_args) {
                    // Cleanup device memory
                    for (size_t i = 0; i < args_count; i++) {
                        if (device_ptrs[i]) {
                            cuMemFree(device_ptrs[i]);
                        }
                    }
                    free(device_ptrs);
                    return OMNI_ERROR_MEMORY_ALLOCATION;
                }

                for (size_t i = 0; i < args_count; i++) {
                    kernel_args[i] = (void*)&device_ptrs[i];
                }

                // Launch kernel using CUDA driver API
                CUfunction kernel_func = (CUfunction)(entry->function_ptr);
                CUresult launch_result = cuLaunchKernel(
                    kernel_func,
                    metadata->grid_size[0], metadata->grid_size[1], metadata->grid_size[2],
                    metadata->block_size[0], metadata->block_size[1], metadata->block_size[2],
                    metadata->shared_mem,
                    NULL,  // Default stream
                    kernel_args,
                    NULL
                );

                if (launch_result != CUDA_SUCCESS) {
                    // Cleanup memory
                    for (size_t i = 0; i < args_count; i++) {
                        if (device_ptrs[i]) {
                            cuMemFree(device_ptrs[i]);
                        }
                    }
                    free(device_ptrs);
                    free(kernel_args);
                    return OMNI_ERROR_CUDA_LAUNCH_ERROR;
                }

                // Synchronize execution
                CUresult sync_result = cuCtxSynchronize();
                if (sync_result != CUDA_SUCCESS) {
                    // Cleanup memory
                    for (size_t i = 0; i < args_count; i++) {
                        if (device_ptrs[i]) {
                            cuMemFree(device_ptrs[i]);
                        }
                    }
                    free(device_ptrs);
                    free(kernel_args);
                    return OMNI_ERROR_CUDA_EXECUTION_ERROR;
                }

                // Copy results back to host
                for (size_t i = 0; i < args_count; i++) {
                    if (i >= entry->metadata.args_count) {
                        break;
                    }

                    OmniArgType arg_type = entry->metadata.args_layout[i];
                    if (omni_is_output_argument(arg_type)) {
                        size_t arg_size = omni_get_type_size(arg_type);
                        CUresult copy_result = cuMemcpyDtoH(args[i], device_ptrs[i], arg_size);
                        if (copy_result != CUDA_SUCCESS) {
                            fprintf(stderr, "Warning: Failed to copy result %zu from device\n", i);
                        }
                    }
                }

                // Cleanup memory
                for (size_t i = 0; i < args_count; i++) {
                    if (device_ptrs[i]) {
                        cuMemFree(device_ptrs[i]);
                    }
                }
                free(device_ptrs);
                free(kernel_args);
                break;
            }

            case OMNI_TARGET_CPU_SIMD: {
                // CPU SIMD execution with vectorization hints
                #ifdef __AVX2__
                if (!__builtin_cpu_supports("avx2")) {
                    return OMNI_ERROR_UNSUPPORTED_SIMD;
                }
                #endif

                // Set optimal floating point control for SIMD
                #ifdef __x86_64__
                unsigned int old_mxcsr = _mm_getcsr();
                _mm_setcsr(old_mxcsr | 0x8040); // Set FTZ and DAZ flags
                #endif

                // Execute SIMD function with proper argument handling
                void (*simd_func)(void**) = (void (*)(void**))(entry->function_ptr);
                simd_func(args);

                #ifdef __x86_64__
                _mm_setcsr(old_mxcsr); // Restore original MXCSR
                #endif
                break;
            }

            case OMNI_TARGET_TPU: {
                // TPU execution not yet implemented
                return OMNI_ERROR_NOT_IMPLEMENTED;
            }

            case OMNI_TARGET_FPGA: {
                // FPGA execution not yet implemented
                return OMNI_ERROR_NOT_IMPLEMENTED;
            }

            default: {
                return OMNI_ERROR_UNSUPPORTED_TARGET;
            }
        }

        // Calculate execution time
        clock_gettime(CLOCK_MONOTONIC, &execution_end);
        long execution_time_ns = (execution_end.tv_sec - execution_start.tv_sec) * 1000000000L +
                                (execution_end.tv_nsec - execution_start.tv_nsec);

        #ifdef OMNI_DEBUG
        printf("Function %s executed in %ld nanoseconds\n", entry->name, execution_time_ns);
        #endif

        return OMNI_ERROR_NONE;
    }
}

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error) {
    switch (error) {
        case OMNI_ERROR_NONE:
            return "No error";
        case OMNI_ERROR_FUNCTION_NOT_FOUND:
            return "Function not found";
        case OMNI_ERROR_ARGUMENT_COUNT_MISMATCH:
            return "Argument count mismatch";
        case OMNI_ERROR_ARGUMENT_TYPE_MISMATCH:
            return "Argument type mismatch";
        case OMNI_ERROR_NOT_IMPLEMENTED:
            return "Not implemented";
        default:
            return "Unknown error";
    }
}

/**
 * CUDA runtime functions
 */

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
) {
    // Validate CUDA context
    CUcontext current_context;
    CUresult context_result = cuCtxGetCurrent(&current_context);
    if (context_result != CUDA_SUCCESS || !current_context) {
        return OMNI_ERROR_CUDA_ERROR;
    }

    // Validate grid and block dimensions
    if (grid_dim[0] == 0 || grid_dim[1] == 0 || grid_dim[2] == 0) {
        return OMNI_ERROR_INVALID_ARGUMENT;
    }

    if (block_dim[0] == 0 || block_dim[1] == 0 || block_dim[2] == 0) {
        return OMNI_ERROR_INVALID_ARGUMENT;
    }

    // Check device limits
    int max_threads_per_block;
    cuDeviceGetAttribute(&max_threads_per_block, CU_DEVICE_ATTRIBUTE_MAX_THREADS_PER_BLOCK, 0);

    size_t total_threads = (size_t)block_dim[0] * block_dim[1] * block_dim[2];
    if (total_threads > (size_t)max_threads_per_block) {
        return OMNI_ERROR_INVALID_ARGUMENT;
    }

    // Launch the kernel
    CUresult launch_result = cuLaunchKernel(
        function,
        grid_dim[0], grid_dim[1], grid_dim[2],    // Grid dimensions
        block_dim[0], block_dim[1], block_dim[2], // Block dimensions
        shared_mem,                                // Shared memory size
        NULL,                                      // Stream (default)
        args,                                      // Kernel arguments
        NULL                                       // Extra parameters
    );

    if (launch_result != CUDA_SUCCESS) {
        return OMNI_ERROR_CUDA_ERROR;
    }

    // Synchronize to ensure kernel completion
    CUresult sync_result = cuCtxSynchronize();
    if (sync_result != CUDA_SUCCESS) {
        return OMNI_ERROR_CUDA_ERROR;
    }

    return OMNI_SUCCESS;
}
"#.to_string()
    }
    
    
    
    /// Generate header file
    fn generate_header(&self, entries: &[CodexEntry], functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut header = String::new();
        
        // Generate file header
        header.push_str(&self.generate_file_header());
        header.push('\n');
        
        // Generate imports
        header.push_str(r"#include <string.h>
#include <stdio.h>
#include <stdlib.h>
");
        header.push_str("\n\n");
        
        // Generate struct definitions
        header.push_str(&self.generate_codex_entry_struct());
        header.push('\n');
        
        // Generate function declarations
        header.push_str(&self.generate_function_declarations(functions)?);
        header.push('\n');

        // Generate kernel launchers
        for function in functions {
            header.push_str(&self.generate_kernel_launcher(function)?);
        }
        header.push('\n');

        // Generate helper function declarations
        header.push_str(r"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
const OmniCodexEntry* omni_find_function(const char* name);

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result);

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error);

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
);

/**
 * OmniCodex dispatch table
 */
extern const OmniCodexEntry OMNI_CODEX[];

/**
 * Number of entries in the OmniCodex dispatch table
 */
extern const size_t OMNI_CODEX_SIZE;
");
        
        // Function declarations
        for entry in entries {
            if entry.target_type == super::TargetType::GPU {
                header.push_str(&format!("void {}(void);\n", entry.function_pointer));
            }
        }
        
        // Generate file footer
        header.push_str(&self.generate_file_footer());
        
        Ok(header)
    }
}

impl CodeGenerator for CCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating C OmniCodex");
        
        // Generate header
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        let all_functions: Vec<_> = metadata.iter().flat_map(|m| &m.functions).cloned().collect();

        // Generate header file
        let header_code = self.generate_header(&entries, &all_functions)?;
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: Some(header_code),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating C wrappers");
        
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let c_return_type = self.generate_c_type(&return_type);
                    
                    // Extract parameter types
                    let params = if signature.parameter_types.is_empty() {
                        "void".to_string()
                    } else {
                        signature
                            .parameter_types
                            .iter()
                            .enumerate()
                            .map(|(i, param)| {
                                let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                                let c_type = self.generate_c_type(&arg_type);
                                format!("{c_type} arg{i}")
                            })
                            .collect::<Vec<_>>()
                            .join(", ")
                    };
                    
                    // Generate wrapper function
                    let func_name = format!("omni_{}", function.name.to_lowercase());
                    
                    code.push_str(&format!(
                        r#"/**
 * Wrapper for the `{}` function
 */
OmniError {}({}{}) {{
    {}
    void* args[] = {{{}}};
    return omni_execute("{}", args, {}, {});
}}

"#,
                        function.name,
                        func_name,
                        if params == "void" { "".to_string() } else { format!("{params}, ") },
                        if c_return_type == "void" { "void".to_string() } else { format!("{c_return_type} *result") },
                        if c_return_type == "void" { "".to_string() } else { "if (result == NULL) return OMNI_ERROR_ARGUMENT_TYPE_MISMATCH;".to_string() },
                        if params == "void" { "".to_string() } else {
                            (0..signature.parameter_types.len())
                                .map(|i| format!("&arg{i}"))
                                .collect::<Vec<_>>()
                                .join(", ")
                        },
                        function.name,
                        if params == "void" { 0 } else { signature.parameter_types.len() },
                        if c_return_type == "void" { "NULL" } else { "result" },
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        Ok(code)
    }
}
