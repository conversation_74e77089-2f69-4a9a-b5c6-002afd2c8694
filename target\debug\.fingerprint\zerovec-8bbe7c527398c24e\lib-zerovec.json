{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 10124880680406691258, "deps": [[9620753569207166497, "zerovec_derive", false, 6696233395791397226], [10706449961930108323, "yoke", false, 1041423135009623050], [17046516144589451410, "zerofrom", false, 18342336021650219110]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-8bbe7c527398c24e\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}