
Directory: src\codegen
File: cpp_codegen.rs
====================
// src/codegen/cpp_codegen.rs
//! C++ code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating C++ code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables and modern C++ features.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON>yn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// C++ code generator
pub struct CppCodeGenerator {
    // Configuration options can be added here
}

impl Default for CppCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl CppCodeGenerator {
    /// Create a new C++ code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate C++ type from argument type
    fn generate_cpp_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "void".to_string(),
            super::ArgType::I8 => "std::int8_t".to_string(),
            super::ArgType::I16 => "std::int16_t".to_string(),
            super::ArgType::I32 => "std::int32_t".to_string(),
            super::ArgType::I64 => "std::int64_t".to_string(),
            super::ArgType::U8 => "std::uint8_t".to_string(),
            super::ArgType::U16 => "std::uint16_t".to_string(),
            super::ArgType::U32 => "std::uint32_t".to_string(),
            super::ArgType::U64 => "std::uint64_t".to_string(),
            super::ArgType::F32 => "float".to_string(),
            super::ArgType::F64 => "double".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "std::int8_t*".to_string(),
            super::ArgType::I16Ptr => "std::int16_t*".to_string(),
            super::ArgType::I32Ptr => "std::int32_t*".to_string(),
            super::ArgType::I64Ptr => "std::int64_t*".to_string(),
            super::ArgType::U8Ptr => "std::uint8_t*".to_string(),
            super::ArgType::U16Ptr => "std::uint16_t*".to_string(),
            super::ArgType::U32Ptr => "std::uint32_t*".to_string(),
            super::ArgType::U64Ptr => "std::uint64_t*".to_string(),
            super::ArgType::F32Ptr => "float*".to_string(),
            super::ArgType::F64Ptr => "double*".to_string(),
            super::ArgType::BoolPtr => "bool*".to_string(),
            super::ArgType::VoidPtr => "void*".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate C++ function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_cpp_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cpp_type = self.generate_cpp_type(&arg_type);
                        format!("{cpp_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        Ok(format!("{} {}({})", return_type, function.name, params))
    }
    
    /// Generate function declarations
    fn generate_function_declarations(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        for function in functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration
            result.push_str(&format!("extern \"C\" {signature};\n"));
        }
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cpp_type = self.generate_cpp_type(&arg_type);
                        format!("{cpp_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("{{{}, {}, {}}}", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("{{{}, {}, {}}}", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "{1, 1, 1}".to_string(),
                "{256, 1, 1}".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch the CUDA kernel `{function_name}`
 */
void launch_{function_name}({params}) {{
    extern "C" void {function_name}({params});
    
    // Launch parameters
    constexpr std::array<std::uint32_t, 3> grid_dim = {grid_dim};
    constexpr std::array<std::uint32_t, 3> block_dim = {block_dim};
    constexpr std::size_t shared_mem = {shared_mem};
    
    // TODO: Replace with actual CUDA launch implementation
    // This is a placeholder for the actual CUDA kernel launch
    cuda::launch_kernel(
        reinterpret_cast<void*>({function_name}),
        grid_dim,
        block_dim,
        shared_mem,
        std::vector<void*>{{{args}}}
    );
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("reinterpret_cast<void*>(&arg{i})"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/**
 * Argument type enumeration
 */
enum class OmniArgType {
    Void,
    I8,
    I16,
    I32,
    I64,
    U8,
    U16,
    U32,
    U64,
    F32,
    F64,
    Bool,
    I8Ptr,
    I16Ptr,
    I32Ptr,
    I64Ptr,
    U8Ptr,
    U16Ptr,
    U32Ptr,
    U64Ptr,
    F32Ptr,
    F64Ptr,
    BoolPtr,
    VoidPtr
};

/**
 * Target type enumeration
 */
enum class OmniTargetType {
    CPU,
    GPU,
    CPUSIMD,
    TPU,
    FPGA,
    Other
};

/**
 * Compute metadata structure
 */
struct OmniComputeMetadata {
    std::array<std::uint32_t, 3> grid_size;
    std::array<std::uint32_t, 3> block_size;
    std::size_t shared_mem;
    std::vector<OmniArgType> args_layout;
    
    // Convenience constructors
    static OmniComputeMetadata cpu() {
        return {
            {1, 1, 1},
            {1, 1, 1},
            0,
            {}
        };
    }
    
    static OmniComputeMetadata cpu_simd() {
        return {
            {1, 1, 1},
            {1, 1, 1},
            0,
            {}
        };
    }
    
    static OmniComputeMetadata gpu(
        const std::array<std::uint32_t, 3>& grid_size,
        const std::array<std::uint32_t, 3>& block_size,
        std::size_t shared_mem,
        const std::vector<OmniArgType>& args_layout
    ) {
        return {
            grid_size,
            block_size,
            shared_mem,
            args_layout
        };
    }
};

/**
 * Function pointer type
 */
using OmniFunctionPtr = void(*)();

/**
 * OmniCodex entry structure
 */
struct OmniCodexEntry {
    std::string name;
    OmniTargetType target_type;
    OmniFunctionPtr function_ptr;
    OmniComputeMetadata metadata;
};
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> String {
        if entry.metadata.arg_layout.is_empty() {
            return "{}".to_string();
        }
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| match arg {
                super::ArgType::Void => "OmniArgType::Void",
                super::ArgType::I8 => "OmniArgType::I8",
                super::ArgType::I16 => "OmniArgType::I16",
                super::ArgType::I32 => "OmniArgType::I32",
                super::ArgType::I64 => "OmniArgType::I64",
                super::ArgType::U8 => "OmniArgType::U8",
                super::ArgType::U16 => "OmniArgType::U16",
                super::ArgType::U32 => "OmniArgType::U32",
                super::ArgType::U64 => "OmniArgType::U64",
                super::ArgType::F32 => "OmniArgType::F32",
                super::ArgType::F64 => "OmniArgType::F64",
                super::ArgType::Bool => "OmniArgType::Bool",
                super::ArgType::I8Ptr => "OmniArgType::I8Ptr",
                super::ArgType::I16Ptr => "OmniArgType::I16Ptr",
                super::ArgType::I32Ptr => "OmniArgType::I32Ptr",
                super::ArgType::I64Ptr => "OmniArgType::I64Ptr",
                super::ArgType::U8Ptr => "OmniArgType::U8Ptr",
                super::ArgType::U16Ptr => "OmniArgType::U16Ptr",
                super::ArgType::U32Ptr => "OmniArgType::U32Ptr",
                super::ArgType::U64Ptr => "OmniArgType::U64Ptr",
                super::ArgType::F32Ptr => "OmniArgType::F32Ptr",
                super::ArgType::F64Ptr => "OmniArgType::F64Ptr",
                super::ArgType::BoolPtr => "OmniArgType::BoolPtr",
                super::ArgType::VoidPtr => "OmniArgType::VoidPtr",
                super::ArgType::Custom(_) => "OmniArgType::VoidPtr", // Default to void* for custom types
            })
            .collect::<Vec<_>>()
            .join(", ");
        
        format!("{{{arg_types}}}")
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        // Generate table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("const std::vector<OmniCodexEntry> OMNI_CODEX = {\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_dim, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("{{{}, {}, {}}}", grid[0], grid[1], grid[2]),
                    format!("{{{}, {}, {}}}", block[0], block[1], block[2]),
                )
            } else {
                ("{1, 1, 1}".to_string(), "{256, 1, 1}".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = self.generate_arg_types(entry);
            
            // Generate target type
            let target_type = match entry.target_type {
                super::TargetType::CPU => "OmniTargetType::CPU",
                super::TargetType::GPU => "OmniTargetType::GPU",
                super::TargetType::CPUSIMD => "OmniTargetType::CPUSIMD",
                super::TargetType::TPU => "OmniTargetType::TPU",
                super::TargetType::FPGA => "OmniTargetType::FPGA",
                super::TargetType::Other => "OmniTargetType::Other",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    {{
        /* name */ "{}",
        /* target_type */ {},
        /* function_ptr */ reinterpret_cast<OmniFunctionPtr>(&{}),
        /* metadata */ {{
            /* grid_size */ {},
            /* block_size */ {},
            /* shared_mem */ {},
            /* args_layout */ {}
        }}
    }},
"#,
                entry.name,
                target_type,
                entry.function_pointer,
                grid_dim,
                block_size,
                shared_mem,
                args_layout,
            ));
        }
        
        result.push_str("};\n");
        
        result
    }
    
    /// Generate file header
    fn generate_file_header(&self) -> String {
        r#"/**
 * OmniCodex dispatch table generated by OmniForge.
 *
 * This file contains the OmniCodex dispatch table, which provides a zero-cost
 * abstraction for heterogeneous computing. It allows calling functions on
 * different backends (CPU, GPU, etc.) with a unified interface.
 */

#ifndef OMNICODEX_HPP
#define OMNICODEX_HPP

#include <cstdint>
#include <array>
#include <vector>
#include <string>
#include <stdexcept>
#include <optional>
#include <type_traits>
#include <functional>
#include <memory>
#include <variant>

namespace omni {

"#.to_string()
    }
    
    /// Generate file footer
    fn generate_file_footer(&self) -> String {
        r#"

} // namespace omni

#endif // OMNICODEX_HPP
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/**
 * Error codes for OmniCodex operations
 */
enum class OmniError {
    None,
    FunctionNotFound,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    NotImplemented
};

/**
 * OmniCodex exception
 */
class OmniException : public std::runtime_error {
public:
    OmniException(OmniError error, const std::string& message)
        : std::runtime_error(message), error_(error) {}
    
    OmniError error() const { return error_; }
    
private:
    OmniError error_;
};

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or nullptr if not found
 */
const OmniCodexEntry* find_function(const std::string& name) {
    for (const auto& entry : OMNI_CODEX) {
        if (entry.name == name) {
            return &entry;
        }
    }
    return nullptr;
}

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
template<typename T>
T execute(const std::string& name, const std::vector<void*>& args) {
    // Find the function in the dispatch table
    const OmniCodexEntry* entry = find_function(name);
    if (entry == nullptr) {
        throw OmniException(
            OmniError::FunctionNotFound,
            "Function not found: " + name
        );
    }
    
    // Check argument count
    if (args.size() != entry->metadata.args_layout.size()) {
        throw OmniException(
            OmniError::ArgumentCountMismatch,
            "Argument count mismatch: expected " + 
            std::to_string(entry->metadata.args_layout.size()) + 
            ", got " + std::to_string(args.size())
        );
    }
    
    // TODO: Implement actual function execution
    // This is a placeholder for the actual function execution
    
    // For now, throw not implemented
    throw OmniException(
        OmniError::NotImplemented,
        "Not implemented"
    );
}

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
std::string error_message(OmniError error) {
    switch (error) {
        case OmniError::None:
            return "No error";
        case OmniError::FunctionNotFound:
            return "Function not found";
        case OmniError::ArgumentCountMismatch:
            return "Argument count mismatch";
        case OmniError::ArgumentTypeMismatch:
            return "Argument type mismatch";
        case OmniError::NotImplemented:
            return "Not implemented";
        default:
            return "Unknown error";
    }
}

/**
 * CUDA runtime functions
 */
namespace cuda {
    /**
     * Launch a CUDA kernel
     *
     * @param kernel Kernel function pointer
     * @param grid_dim Grid dimensions
     * @param block_dim Block dimensions
     * @param shared_mem Shared memory size
     * @param args Kernel arguments
     */
    void launch_kernel(
        void* kernel,
        const std::array<std::uint32_t, 3>& grid_dim,
        const std::array<std::uint32_t, 3>& block_dim,
        std::size_t shared_mem,
        const std::vector<void*>& args
    ) {
        // This is a placeholder for the actual CUDA kernel launch
        // In a real implementation, this would call the CUDA runtime API
    }
} // namespace cuda
"#.to_string()
    }
    
    
    
    /// Generate header file
    fn generate_header(&self, _entries: &[CodexEntry], functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut header = String::new();
        
        // Generate file header
        header.push_str(&self.generate_file_header());
        header.push('\n');
        
        // Generate struct definitions
        header.push_str(&self.generate_codex_entry_struct());
        header.push('\n');
        
        // Generate forward declarations
        header.push_str(&self.generate_function_declarations(functions)?);
        header.push('\n');

        // Generate kernel launchers
        for function in functions {
            header.push_str(&self.generate_kernel_launcher(function)?);
        }
        header.push('\n');
        
        // Declare the OmniCodex table
        header.push_str("/**\n");
        header.push_str(" * OmniCodex dispatch table\n");
        header.push_str(" */\n");
        header.push_str("extern const std::vector<OmniCodexEntry> OMNI_CODEX;\n\n");
        
        // Generate helper function declarations
        header.push_str(r#"/**
 * Error codes for OmniCodex operations
 */
enum class OmniError {
    None,
    FunctionNotFound,
    ArgumentCountMismatch,
    ArgumentTypeMismatch,
    NotImplemented
};

/**
 * OmniCodex exception
 */
class OmniException : public std::runtime_error {
public:
    OmniException(OmniError error, const std::string& message);
    OmniError error() const;
    
private:
    OmniError error_;
};

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or nullptr if not found
 */
const OmniCodexEntry* find_function(const std::string& name);

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
template<typename T>
T execute(const std::string& name, const std::vector<void*>& args);

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
std::string error_message(OmniError error);

/**
 * CUDA runtime functions
 */
namespace cuda {
    /**
     * Launch a CUDA kernel
     *
     * @param kernel Kernel function pointer
     * @param grid_dim Grid dimensions
     * @param block_dim Block dimensions
     * @param shared_mem Shared memory size
     * @param args Kernel arguments
     */
    void launch_kernel(
        void* kernel,
        const std::array<std::uint32_t, 3>& grid_dim,
        const std::array<std::uint32_t, 3>& block_dim,
        std::size_t shared_mem,
        const std::vector<void*>& args
    );
} // namespace cuda
"#);
        
        // Generate file footer
        header.push_str(&self.generate_file_footer());
        
        Ok(header)
    }
}

impl CodeGenerator for CppCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating C++ OmniCodex");
        
        // Generate header
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.hpp\"\n\n");
        
        // Add namespace
        code.push_str("namespace omni {\n\n");
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        // Close namespace
        code.push_str("\n} // namespace omni\n");
        
        let all_functions: Vec<_> = metadata.iter().flat_map(|m| &m.functions).cloned().collect();

        // Generate header file
        let header_code = self.generate_header(&entries, &all_functions)?;
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: Some(header_code),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating C++ wrappers");
        
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.hpp\"\n\n");
        
        // Add namespace
        code.push_str("namespace omni {\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let cpp_return_type = self.generate_cpp_type(&return_type);
                    
                    // Extract parameter types
                    let params = if signature.parameter_types.is_empty() {
                        "".to_string()
                    } else {
                        signature
                            .parameter_types
                            .iter()
                            .enumerate()
                            .map(|(i, param)| {
                                let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                                let cpp_type = self.generate_cpp_type(&arg_type);
                                format!("{cpp_type} arg{i}")
                            })
                            .collect::<Vec<_>>()
                            .join(", ")
                    };
                    
                    // Generate wrapper function
                    let func_name = function.name.to_lowercase();
                    
                    code.push_str(&format!(
                        r#"/**
 * Wrapper for the `{}` function
 *
 * @return Result of the function call
 * @throws OmniException if an error occurs
 */
{} {}({}) {{
    std::vector<void*> args;
    {}
    return execute<{}>("{}", args);
}}

"#,
                        function.name,
                        cpp_return_type,
                        func_name,
                        params,
                        if signature.parameter_types.is_empty() {
                            "// No arguments".to_string()
                        } else {
                            (0..signature.parameter_types.len())
                                .map(|i| format!("args.push_back(reinterpret_cast<void*>(&arg{i}));"))
                                .collect::<Vec<_>>()
                                .join("\n    ")
                        },
                        cpp_return_type,
                        function.name,
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        // Close namespace
        code.push_str("} // namespace omni\n");
        
        Ok(code)
    }
}



Directory: src\codegen
File: c_codegen.rs
==================
// src/codegen/c_codegen.rs
//! C code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating C code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// C code generator
pub struct CCodeGenerator {
    // Configuration options can be added here
}

impl Default for CCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl CCodeGenerator {
    /// Create a new C code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate C type from argument type
    fn generate_c_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "void".to_string(),
            super::ArgType::I8 => "int8_t".to_string(),
            super::ArgType::I16 => "int16_t".to_string(),
            super::ArgType::I32 => "int32_t".to_string(),
            super::ArgType::I64 => "int64_t".to_string(),
            super::ArgType::U8 => "uint8_t".to_string(),
            super::ArgType::U16 => "uint16_t".to_string(),
            super::ArgType::U32 => "uint32_t".to_string(),
            super::ArgType::U64 => "uint64_t".to_string(),
            super::ArgType::F32 => "float".to_string(),
            super::ArgType::F64 => "double".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "int8_t*".to_string(),
            super::ArgType::I16Ptr => "int16_t*".to_string(),
            super::ArgType::I32Ptr => "int32_t*".to_string(),
            super::ArgType::I64Ptr => "int64_t*".to_string(),
            super::ArgType::U8Ptr => "uint8_t*".to_string(),
            super::ArgType::U16Ptr => "uint16_t*".to_string(),
            super::ArgType::U32Ptr => "uint32_t*".to_string(),
            super::ArgType::U64Ptr => "uint64_t*".to_string(),
            super::ArgType::F32Ptr => "float*".to_string(),
            super::ArgType::F64Ptr => "double*".to_string(),
            super::ArgType::BoolPtr => "bool*".to_string(),
            super::ArgType::VoidPtr => "void*".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate C function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_c_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        Ok(format!("{} {}({})", return_type, function.name, params))
    }
    
    /// Generate function declarations
    fn generate_function_declarations(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        for function in functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration
            result.push_str(&format!("extern {signature};\n"));
        }
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let c_type = self.generate_c_type(&arg_type);
                        format!("{c_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("{{{}, {}, {}}}", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("{{{}, {}, {}}}", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "{1, 1, 1}".to_string(),
                "{256, 1, 1}".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch the CUDA kernel `{function_name}`
 */
void launch_{function_name}({params}) {{
    extern void {function_name}({params});
    
    // Launch parameters
    const uint32_t grid_dim[3] = {grid_dim};
    const uint32_t block_dim[3] = {block_dim};
    const size_t shared_mem = {shared_mem};
    
    // TODO: Replace with actual CUDA launch implementation
    // This is a placeholder for the actual CUDA kernel launch
    cuda_runtime_launch_kernel(
        (void*){function_name},
        grid_dim,
        block_dim,
        shared_mem,
        (void*[]){{{args}}},
        {arg_count}
    );
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("&arg{i}"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
            arg_count = if params == "void" { 0 } else { params.split(", ").count() },
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/**
 * Argument type enumeration
 */
typedef enum {
    OMNI_ARG_VOID,
    OMNI_ARG_I8,
    OMNI_ARG_I16,
    OMNI_ARG_I32,
    OMNI_ARG_I64,
    OMNI_ARG_U8,
    OMNI_ARG_U16,
    OMNI_ARG_U32,
    OMNI_ARG_U64,
    OMNI_ARG_F32,
    OMNI_ARG_F64,
    OMNI_ARG_BOOL,
    OMNI_ARG_I8_PTR,
    OMNI_ARG_I16_PTR,
    OMNI_ARG_I32_PTR,
    OMNI_ARG_I64_PTR,
    OMNI_ARG_U8_PTR,
    OMNI_ARG_U16_PTR,
    OMNI_ARG_U32_PTR,
    OMNI_ARG_U64_PTR,
    OMNI_ARG_F32_PTR,
    OMNI_ARG_F64_PTR,
    OMNI_ARG_BOOL_PTR,
    OMNI_ARG_VOID_PTR
} OmniArgType;

/**
 * Target type enumeration
 */
typedef enum {
    OMNI_TARGET_CPU,
    OMNI_TARGET_GPU,
    OMNI_TARGET_CPU_SIMD,
    OMNI_TARGET_TPU,
    OMNI_TARGET_FPGA,
    OMNI_TARGET_OTHER
} OmniTargetType;

/**
 * Compute metadata structure
 */
typedef struct {
    uint32_t grid_size[3];
    uint32_t block_size[3];
    size_t shared_mem;
    const OmniArgType* args_layout;
    size_t args_count;
} OmniComputeMetadata;

/**
 * OmniCodex entry structure
 */
typedef struct {
    const char* name;
    OmniTargetType target_type;
    void (*function_ptr)(void);
    OmniComputeMetadata metadata;
} OmniCodexEntry;
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> (String, String) {
        if entry.metadata.arg_layout.is_empty() {
            return ("NULL".to_string(), "0".to_string());
        }
        
        let var_name = format!("{}_args", entry.name);
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| match arg {
                super::ArgType::Void => "OMNI_ARG_VOID",
                super::ArgType::I8 => "OMNI_ARG_I8",
                super::ArgType::I16 => "OMNI_ARG_I16",
                super::ArgType::I32 => "OMNI_ARG_I32",
                super::ArgType::I64 => "OMNI_ARG_I64",
                super::ArgType::U8 => "OMNI_ARG_U8",
                super::ArgType::U16 => "OMNI_ARG_U16",
                super::ArgType::U32 => "OMNI_ARG_U32",
                super::ArgType::U64 => "OMNI_ARG_U64",
                super::ArgType::F32 => "OMNI_ARG_F32",
                super::ArgType::F64 => "OMNI_ARG_F64",
                super::ArgType::Bool => "OMNI_ARG_BOOL",
                super::ArgType::I8Ptr => "OMNI_ARG_I8_PTR",
                super::ArgType::I16Ptr => "OMNI_ARG_I16_PTR",
                super::ArgType::I32Ptr => "OMNI_ARG_I32_PTR",
                super::ArgType::I64Ptr => "OMNI_ARG_I64_PTR",
                super::ArgType::U8Ptr => "OMNI_ARG_U8_PTR",
                super::ArgType::U16Ptr => "OMNI_ARG_U16_PTR",
                super::ArgType::U32Ptr => "OMNI_ARG_U32_PTR",
                super::ArgType::U64Ptr => "OMNI_ARG_U64_PTR",
                super::ArgType::F32Ptr => "OMNI_ARG_F32_PTR",
                super::ArgType::F64Ptr => "OMNI_ARG_F64_PTR",
                super::ArgType::BoolPtr => "OMNI_ARG_BOOL_PTR",
                super::ArgType::VoidPtr => "OMNI_ARG_VOID_PTR",
                super::ArgType::Custom(_) => "OMNI_ARG_VOID_PTR", // Default to void* for custom types
            })
            .collect::<Vec<_>>()
            .join(", ");
        
        let array_def = format!("static const OmniArgType {var_name}[] = {{ {arg_types} }};");
        
        (var_name, array_def)
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        // Generate arg type arrays
        let mut arg_arrays = String::new();
        let mut arg_vars = HashMap::new();
        
        for entry in entries {
            let (var_name, array_def) = self.generate_arg_types(entry);
            if var_name != "NULL" {
                arg_arrays.push_str(&format!("{array_def}\n"));
                arg_vars.insert(entry.name.clone(), var_name);
            }
        }
        
        result.push_str(&arg_arrays);
        result.push('\n');
        
        // Generate table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("static const OmniCodexEntry OMNI_CODEX[] = {\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("{{{}, {}, {}}}", grid[0], grid[1], grid[2]),
                    format!("{{{}, {}, {}}}", block[0], block[1], block[2]),
                )
            } else {
                ("{1, 1, 1}".to_string(), "{256, 1, 1}".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = arg_vars.get(&entry.name).map_or("NULL".to_string(), |v| v.clone());
            let args_count = entry.metadata.arg_layout.len();
            
            // Generate target type
            let target_type = match entry.target_type {
                super::TargetType::CPU => "OMNI_TARGET_CPU",
                super::TargetType::GPU => "OMNI_TARGET_GPU",
                super::TargetType::CPUSIMD => "OMNI_TARGET_CPU_SIMD",
                super::TargetType::TPU => "OMNI_TARGET_TPU",
                super::TargetType::FPGA => "OMNI_TARGET_FPGA",
                super::TargetType::Other => "OMNI_TARGET_OTHER",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    {{
        /* name */ "{}",
        /* target_type */ {},
        /* function_ptr */ (void(*)())&{},
        /* metadata */ {{
            /* grid_size */ {},
            /* block_size */ {},
            /* shared_mem */ {},
            /* args_layout */ {},
            /* args_count */ {}
        }}
    }},
"#,
                entry.name,
                target_type,
                entry.function_pointer,
                grid_size,
                block_size,
                shared_mem,
                args_layout,
                args_count,
            ));
        }
        
        result.push_str("};\n\n");
        result.push_str("/**\n");
        result.push_str(" * Number of entries in the OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str(&format!("static const size_t OMNI_CODEX_SIZE = {};\n", entries.len()));
        
        result
    }
    
    /// Generate file header
    fn generate_file_header(&self) -> String {
        r#"/**
 * OmniCodex dispatch table generated by OmniForge.
 *
 * This file contains the OmniCodex dispatch table, which provides a zero-cost
 * abstraction for heterogeneous computing. It allows calling functions on
 * different backends (CPU, GPU, etc.) with a unified interface.
 */

#ifndef OMNICODEX_H
#define OMNICODEX_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

"#.to_string()
    }
    
    /// Generate file footer
    fn generate_file_footer(&self) -> String {
        r#"
#ifdef __cplusplus
} // extern "C"
#endif

#endif // OMNICODEX_H
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
static const OmniCodexEntry* omni_find_function(const char* name) {
    for (size_t i = 0; i < OMNI_CODEX_SIZE; i++) {
        if (strcmp(OMNI_CODEX[i].name, name) == 0) {
            return &OMNI_CODEX[i];
        }
    }
    return NULL;
}

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result) {
    // Find the function in the dispatch table
    const OmniCodexEntry* entry = omni_find_function(name);
    if (entry == NULL) {
        return OMNI_ERROR_FUNCTION_NOT_FOUND;
    }
    
    // Check argument count
    if (args_count != entry->metadata.args_count) {
        return OMNI_ERROR_ARGUMENT_COUNT_MISMATCH;
    }
    
    // TODO: Implement actual function execution
    // This is a placeholder for the actual function execution
    
    // For now, return not implemented
    return OMNI_ERROR_NOT_IMPLEMENTED;
}

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error) {
    switch (error) {
        case OMNI_ERROR_NONE:
            return "No error";
        case OMNI_ERROR_FUNCTION_NOT_FOUND:
            return "Function not found";
        case OMNI_ERROR_ARGUMENT_COUNT_MISMATCH:
            return "Argument count mismatch";
        case OMNI_ERROR_ARGUMENT_TYPE_MISMATCH:
            return "Argument type mismatch";
        case OMNI_ERROR_NOT_IMPLEMENTED:
            return "Not implemented";
        default:
            return "Unknown error";
    }
}

/**
 * CUDA runtime functions
 */

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
) {
    // This is a placeholder for the actual CUDA kernel launch
    // In a real implementation, this would call the CUDA runtime API
}
"#.to_string()
    }
    
    
    
    /// Generate header file
    fn generate_header(&self, entries: &[CodexEntry], functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut header = String::new();
        
        // Generate file header
        header.push_str(&self.generate_file_header());
        header.push('\n');
        
        // Generate imports
        header.push_str(r"#include <string.h>
#include <stdio.h>
#include <stdlib.h>
");
        header.push_str("\n\n");
        
        // Generate struct definitions
        header.push_str(&self.generate_codex_entry_struct());
        header.push('\n');
        
        // Generate function declarations
        header.push_str(&self.generate_function_declarations(functions)?);
        header.push('\n');

        // Generate kernel launchers
        for function in functions {
            header.push_str(&self.generate_kernel_launcher(function)?);
        }
        header.push('\n');

        // Generate helper function declarations
        header.push_str(r"
/**
 * Error codes for OmniCodex operations
 */
typedef enum {
    OMNI_ERROR_NONE,
    OMNI_ERROR_FUNCTION_NOT_FOUND,
    OMNI_ERROR_ARGUMENT_COUNT_MISMATCH,
    OMNI_ERROR_ARGUMENT_TYPE_MISMATCH,
    OMNI_ERROR_NOT_IMPLEMENTED
} OmniError;

/**
 * Find a function in the OmniCodex dispatch table
 *
 * @param name Function name
 * @return Pointer to the OmniCodexEntry, or NULL if not found
 */
const OmniCodexEntry* omni_find_function(const char* name);

/**
 * Execute a function by name
 *
 * @param name Function name
 * @param args Function arguments
 * @param args_count Number of arguments
 * @param result Pointer to store the result
 * @return Error code
 */
OmniError omni_execute(const char* name, void* args[], size_t args_count, void* result);

/**
 * Get error message for an error code
 *
 * @param error Error code
 * @return Error message
 */
const char* omni_error_message(OmniError error);

/**
 * Launch a CUDA kernel
 *
 * @param kernel Kernel function pointer
 * @param grid_dim Grid dimensions
 * @param block_dim Block dimensions
 * @param shared_mem Shared memory size
 * @param args Kernel arguments
 * @param args_count Number of arguments
 */
void cuda_runtime_launch_kernel(
    void* kernel,
    const uint32_t grid_dim[3],
    const uint32_t block_dim[3],
    size_t shared_mem,
    void* args[],
    size_t args_count
);

/**
 * OmniCodex dispatch table
 */
extern const OmniCodexEntry OMNI_CODEX[];

/**
 * Number of entries in the OmniCodex dispatch table
 */
extern const size_t OMNI_CODEX_SIZE;
");
        
        // Function declarations
        for entry in entries {
            if entry.target_type == super::TargetType::GPU {
                header.push_str(&format!("void {}(void);\n", entry.function_pointer));
            }
        }
        
        // Generate file footer
        header.push_str(&self.generate_file_footer());
        
        Ok(header)
    }
}

impl CodeGenerator for CCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating C OmniCodex");
        
        // Generate header
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        let all_functions: Vec<_> = metadata.iter().flat_map(|m| &m.functions).cloned().collect();

        // Generate header file
        let header_code = self.generate_header(&entries, &all_functions)?;
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: Some(header_code),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating C wrappers");
        
        let mut code = String::new();
        
        // Include header file
        code.push_str("#include \"omnicodex.h\"\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let c_return_type = self.generate_c_type(&return_type);
                    
                    // Extract parameter types
                    let params = if signature.parameter_types.is_empty() {
                        "void".to_string()
                    } else {
                        signature
                            .parameter_types
                            .iter()
                            .enumerate()
                            .map(|(i, param)| {
                                let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                                let c_type = self.generate_c_type(&arg_type);
                                format!("{c_type} arg{i}")
                            })
                            .collect::<Vec<_>>()
                            .join(", ")
                    };
                    
                    // Generate wrapper function
                    let func_name = format!("omni_{}", function.name.to_lowercase());
                    
                    code.push_str(&format!(
                        r#"/**
 * Wrapper for the `{}` function
 */
OmniError {}({}{}) {{
    {}
    void* args[] = {{{}}};
    return omni_execute("{}", args, {}, {});
}}

"#,
                        function.name,
                        func_name,
                        if params == "void" { "".to_string() } else { format!("{params}, ") },
                        if c_return_type == "void" { "void".to_string() } else { format!("{c_return_type} *result") },
                        if c_return_type == "void" { "".to_string() } else { "if (result == NULL) return OMNI_ERROR_ARGUMENT_TYPE_MISMATCH;".to_string() },
                        if params == "void" { "".to_string() } else {
                            (0..signature.parameter_types.len())
                                .map(|i| format!("&arg{i}"))
                                .collect::<Vec<_>>()
                                .join(", ")
                        },
                        function.name,
                        if params == "void" { 0 } else { signature.parameter_types.len() },
                        if c_return_type == "void" { "NULL" } else { "result" },
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        Ok(code)
    }
}



Directory: src\codegen
File: py_codegen.rs
===================
// src/codegen/py_codegen.rs
//! Python code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating Python code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of heterogeneous
//! computing capabilities into Python applications, with support for NumPy, CuPy,
//! and CFFI for interfacing with native code.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen, ArgType, TargetType};

/// Python code generator
pub struct PythonCodeGenerator {
    /// Use NumPy for array operations
    use_numpy: bool,
    
    /// Use CuPy for GPU array operations
    use_cupy: bool,
    
    /// Use CFFI for interfacing with C code
    use_cffi: bool,
    
    /// Use type hints (Python 3.6+)
    use_type_hints: bool,
    
    /// Target Python version
    target_version: String,
    
    /// Include debug logging
    include_debug_logging: bool,
}

#[allow(dead_code)]
impl PythonCodeGenerator {
    /// Create a new Python code generator with default settings
    pub fn new() -> Self {
        Self {
            use_numpy: true,
            use_cupy: true,
            use_cffi: true,
            use_type_hints: true,
            target_version: "3.9".to_string(),
            include_debug_logging: true,
        }
    }
    
    /// Create a new Python code generator with specific configuration
    pub fn with_config(
        use_numpy: bool,
        use_cupy: bool,
        use_cffi: bool,
        use_type_hints: bool,
        target_version: String,
        include_debug_logging: bool,
    ) -> Self {
        Self {
            use_numpy,
            use_cupy,
            use_cffi,
            use_type_hints,
            target_version,
            include_debug_logging,
        }
    }
    
    /// Generate Python type from argument type
    fn generate_py_type(&self, arg_type: &ArgType) -> String {
        if !self.use_type_hints {
            return String::new();
        }
        
        match arg_type {
            ArgType::Void => "None".to_string(),
            ArgType::I8 => "int".to_string(),
            ArgType::I16 => "int".to_string(),
            ArgType::I32 => "int".to_string(),
            ArgType::I64 => "int".to_string(),
            ArgType::U8 => "int".to_string(),
            ArgType::U16 => "int".to_string(),
            ArgType::U32 => "int".to_string(),
            ArgType::U64 => "int".to_string(),
            ArgType::F32 => "float".to_string(),
            ArgType::F64 => "float".to_string(),
            ArgType::Bool => "bool".to_string(),
            ArgType::I8Ptr => "np.ndarray".to_string(),
            ArgType::I16Ptr => "np.ndarray".to_string(),
            ArgType::I32Ptr => "np.ndarray".to_string(),
            ArgType::I64Ptr => "np.ndarray".to_string(),
            ArgType::U8Ptr => "np.ndarray".to_string(),
            ArgType::U16Ptr => "np.ndarray".to_string(),
            ArgType::U32Ptr => "np.ndarray".to_string(),
            ArgType::U64Ptr => "np.ndarray".to_string(),
            ArgType::F32Ptr => "np.ndarray".to_string(),
            ArgType::F64Ptr => "np.ndarray".to_string(),
            ArgType::BoolPtr => "np.ndarray".to_string(),
            ArgType::VoidPtr => "ctypes.c_void_p".to_string(),
            ArgType::Custom(name) => format!("Any # {name}"),
        }
    }
    
    /// Generate Python function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if self.use_type_hints {
            if let Some(signature) = &function.signature {
                let py_type = self.generate_py_type(&Codegen::map_type_to_arg_type(
                    &signature.return_type.name,
                    signature.return_type.is_pointer,
                ));
                
                format!(" -> {py_type}")
            } else {
                " -> None".to_string()
            }
        } else {
            String::new()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                String::new()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        if self.use_type_hints {
                            let py_type = self.generate_py_type(&arg_type);
                            format!("arg{i}: {py_type}")
                        } else {
                            format!("arg{i}")
                        }
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            String::new()
        };
        
        Ok(format!("def {}({}){}", function.name, params, return_type))
    }
    
    /// Generate OmniCodex module
    fn generate_module(&self, entries: &[CodexEntry]) -> OmniResult<String> {
        let mut result = String::new();
        
        // Generate header with comments
        result.push_str(&format!(
            r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex Python Integration

Generated by OmniForge - The OmniCodex Compiler Framework

This module provides Python interfaces and implementations for
heterogeneous computing with the OmniCodex framework.

Target Python Version: {target_version}
NumPy: {numpy}
CuPy: {cupy}
CFFI: {cffi}
Type Hints: {type_hints}
"""

from __future__ import annotations
import os
import sys
import enum
import logging
import ctypes
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, TypeVar, Generic, cast
{numpy_import}
{cupy_import}
{cffi_import}

# Configure logging
{logging_config}

{enums}
"#,
            target_version = self.target_version,
            numpy = if self.use_numpy { "Enabled" } else { "Disabled" },
            cupy = if self.use_cupy { "Enabled" } else { "Disabled" },
            cffi = if self.use_cffi { "Enabled" } else { "Disabled" },
            type_hints = if self.use_type_hints { "Enabled" } else { "Disabled" },
            numpy_import = if self.use_numpy { 
                "import numpy as np" 
            } else { 
                "# NumPy integration disabled" 
            },
            cupy_import = if self.use_cupy { 
                "try:\n    import cupy as cp\nexcept ImportError:\n    logger.warning(\"CuPy not found. GPU operations will not be available.\")\n    cp = None" 
            } else { 
                "# CuPy integration disabled" 
            },
            cffi_import = if self.use_cffi { 
                "try:\n    import cffi\n    _ffi = cffi.FFI()\nexcept ImportError:\n    logger.warning(\"CFFI not found. Native code integration will be limited.\")\n    _ffi = None" 
            } else { 
                "# CFFI integration disabled" 
            },
            logging_config = if self.include_debug_logging {
                r#"logger = logging.getLogger("omnicodex")
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)"#
            } else {
                r#"logger = logging.getLogger("omnicodex")
logger.addHandler(logging.NullHandler())"#
            },
            enums = self.generate_enums(),
        ));
        
        // Generate core classes
        result.push_str(r#"
class ComputeMetadata:
    """Function metadata for compute operations"""
    
    def __init__(
        self, 
        grid_size: Tuple[int, int, int],
        block_size: Tuple[int, int, int],
        shared_mem: int,
        args_layout: List[ArgType]
    ):
        self.grid_size = grid_size
        self.block_size = block_size
        self.shared_mem = shared_mem
        self.args_layout = args_layout
    
    @classmethod
    def cpu(cls) -> 'ComputeMetadata':
        """Create CPU metadata"""
        return cls(
            grid_size=(1, 1, 1),
            block_size=(1, 1, 1),
            shared_mem=0,
            args_layout=[]
        )
    
    @classmethod
    def cpu_simd(cls) -> 'ComputeMetadata':
        """Create CPU SIMD metadata"""
        return cls(
            grid_size=(1, 1, 1),
            block_size=(1, 1, 1),
            shared_mem=0,
            args_layout=[]
        )
    
    @classmethod
    def gpu(
        cls,
        grid_size: Tuple[int, int, int],
        block_size: Tuple[int, int, int],
        shared_mem: int,
        args_layout: List[ArgType]
    ) -> 'ComputeMetadata':
        """Create GPU metadata"""
        return cls(
            grid_size=grid_size,
            block_size=block_size,
            shared_mem=shared_mem,
            args_layout=args_layout
        )
    
    def __repr__(self) -> str:
        return (
            f"ComputeMetadata(grid_size={self.grid_size}, "
            f"block_size={self.block_size}, "
            f"shared_mem={self.shared_mem}, "
            f"args_layout={self.args_layout})"
        )


class OmniCodexEntry:
    """Entry in the OmniCodex dispatch table"""
    
    def __init__(
        self,
        name: str,
        target: OmniTarget,
        impl: Callable,
        metadata: ComputeMetadata
    ):
        self.name = name
        self.target = target
        self.impl = impl
        self.metadata = metadata
    
    def __repr__(self) -> str:
        return f"OmniCodexEntry(name='{self.name}', target={self.target})"


class OmniError(Exception):
    """OmniCodex error"""
    
    def __init__(self, code: OmniErrorCode, message: str):
        self.code = code
        self.message = message
        super().__init__(f"{code.name}: {message}")


# Type variable for generic return types
T = TypeVar('T')

"#);
        
        // Generate OmniCodex dispatch table
        result.push_str("# OmniCodex dispatch table\nOMNI_CODEX = [\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("({}, {}, {})", grid[0], grid[1], grid[2]),
                    format!("({}, {}, {})", block[0], block[1], block[2]),
                )
            } else {
                ("(1, 1, 1)".to_string(), "(256, 1, 1)".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = entry
                .metadata
                .arg_layout
                .iter()
                .map(|arg| format!("ArgType.{arg}"))
                .collect::<Vec<_>>()
                .join(", ");
            
            // Generate target type
            let target_type = match entry.target_type {
                TargetType::CPU => "OmniTarget.CPU",
                TargetType::GPU => "OmniTarget.GPU",
                TargetType::CPUSIMD => "OmniTarget.CPUSIMD",
                TargetType::TPU => "OmniTarget.TPU",
                TargetType::FPGA => "OmniTarget.FPGA",
                TargetType::Other => "OmniTarget.OTHER",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"    # {name} - {target_desc}
    OmniCodexEntry(
        name='{name}',
        target={target},
        impl={impl_name},
        metadata=ComputeMetadata(
            grid_size={grid_size},
            block_size={block_size},
            shared_mem={shared_mem},
            args_layout=[{args_layout}]
        )
    ),
"#,
                name = entry.name,
                target_desc = entry.target_type,
                target = target_type,
                impl_name = format!("{}_{}", entry.target_type.to_string().to_lowercase(), entry.name),
                grid_size = grid_size,
                block_size = block_size,
                shared_mem = shared_mem,
                args_layout = args_layout,
            ));
        }
        
        result.push_str("]\n\n");
        
        // Generate platform-specific implementations
        result.push_str("# CPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::CPU) {
            result.push_str(&format!(
                r#"def cpu_{name}({args}){return_type}:
    """CPU implementation of {name}"""
    raise OmniError(OmniErrorCode.NOT_IMPLEMENTED, "CPU implementation not provided")

"#,
                name = entry.name,
                args = if entry.metadata.arg_layout.is_empty() {
                    String::new()
                } else {
                    (0..entry.metadata.arg_layout.len())
                        .map(|i| format!("arg{i}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                },
                return_type = if self.use_type_hints {
                    format!(" -> {}", self.generate_py_type(&entry.metadata.return_type))
                } else {
                    String::new()
                },
            ));
        }
        
        result.push_str("# GPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::GPU) {
            result.push_str(&format!(
                r#"def gpu_{name}({args}){return_type}:
    """GPU implementation of {name}"""
    if cp is None:
        raise OmniError(OmniErrorCode.GPU_NOT_AVAILABLE, "CuPy not available")
    raise OmniError(OmniErrorCode.NOT_IMPLEMENTED, "GPU implementation not provided")

"#,
                name = entry.name,
                args = if entry.metadata.arg_layout.is_empty() {
                    String::new()
                } else {
                    (0..entry.metadata.arg_layout.len())
                        .map(|i| format!("arg{i}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                },
                return_type = if self.use_type_hints {
                    format!(" -> {}", self.generate_py_type(&entry.metadata.return_type))
                } else {
                    String::new()
                },
            ));
        }
        
        // Generate utility functions
        result.push_str(r#"
def find_function(name: str) -> Optional[OmniCodexEntry]:
    """
    Find a function in the OmniCodex dispatch table
    
    Args:
        name: Function name
        
    Returns:
        OmniCodexEntry or None if not found
    """
    for entry in OMNI_CODEX:
        if entry.name == name:
            return entry
    return None


def execute(name: str, *args: Any) -> Any:
    """
    Execute a function by name
    
    Args:
        name: Function name
        args: Function arguments
        
    Returns:
        Function result
        
    Raises:
        OmniError: If an error occurs during execution
    """
    # Find the function in the dispatch table
    entry = find_function(name)
    if entry is None:
        raise OmniError(
            OmniErrorCode.FUNCTION_NOT_FOUND,
            f"Function not found: {name}"
        )
    
    # Check argument count
    if len(args) != len(entry.metadata.args_layout):
        raise OmniError(
            OmniErrorCode.ARGUMENT_COUNT_MISMATCH,
            f"Argument count mismatch: expected {len(entry.metadata.args_layout)}, got {len(args)}"
        )
    
    # Execute function
    try:
        logger.debug(f"Executing {name} with {len(args)} arguments")
        return entry.impl(*args)
    except Exception as e:
        logger.error(f"Error executing {name}: {e}")
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Runtime error: {str(e)}"
        ) from e


class OmniCodex:
    """OmniCodex API class"""
    
    def __init__(self):
        self._entries = {entry.name: entry for entry in OMNI_CODEX}
    
    def __getattr__(self, name: str) -> Callable:
        """
        Get a function by name
        
        Args:
            name: Function name
            
        Returns:
            Function wrapper
            
        Raises:
            AttributeError: If function not found
        """
        if name in self._entries:
            def wrapper(*args: Any) -> Any:
                return execute(name, *args)
            return wrapper
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def available_functions(self) -> List[str]:
        """
        Get list of available functions
        
        Returns:
            List of function names
        """
        return list(self._entries.keys())
    
    def function_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a function
        
        Args:
            name: Function name
            
        Returns:
            Function information or None if not found
        """
        entry = self._entries.get(name)
        if entry is None:
            return None
        
        return {
            "name": entry.name,
            "target": entry.target.name,
            "grid_size": entry.metadata.grid_size,
            "block_size": entry.metadata.block_size,
            "shared_mem": entry.metadata.shared_mem,
            "args_layout": [arg.name for arg in entry.metadata.args_layout],
        }


# Create API instance
api = OmniCodex()


def is_gpu_available() -> bool:
    """
    Check if GPU is available
    
    Returns:
        True if GPU is available
    """
    return cp is not None


def is_cffi_available() -> bool:
    """
    Check if CFFI is available
    
    Returns:
        True if CFFI is available
    """
    return _ffi is not None


# Module exports
__all__ = [
    'OmniTarget',
    'ArgType',
    'OmniErrorCode',
    'OmniError',
    'ComputeMetadata',
    'OmniCodexEntry',
    'OMNI_CODEX',
    'find_function',
    'execute',
    'api',
    'is_gpu_available',
    'is_cffi_available',
]
"#);
        
        Ok(result)
    }
    
    /// Generate Python enums
    fn generate_enums(&self) -> String {
        r#"class OmniTarget(enum.Enum):
    """Target platform for computation"""
    CPU = "CPU"           # Central Processing Unit
    GPU = "GPU"           # Graphics Processing Unit
    CPUSIMD = "CPUSIMD"   # CPU with SIMD instructions
    TPU = "TPU"           # Tensor Processing Unit
    FPGA = "FPGA"         # Field-Programmable Gate Array
    OTHER = "OTHER"       # Other compute device


class ArgType(enum.Enum):
    """Argument type enumeration"""
    Void = "Void"         # Void type
    I8 = "I8"             # 8-bit signed integer
    I16 = "I16"           # 16-bit signed integer
    I32 = "I32"           # 32-bit signed integer
    I64 = "I64"           # 64-bit signed integer
    U8 = "U8"             # 8-bit unsigned integer
    U16 = "U16"           # 16-bit unsigned integer
    U32 = "U32"           # 32-bit unsigned integer
    U64 = "U64"           # 64-bit unsigned integer
    F32 = "F32"           # 32-bit floating point
    F64 = "F64"           # 64-bit floating point
    Bool = "Bool"         # Boolean
    I8Ptr = "I8Ptr"       # Pointer to 8-bit signed integer
    I16Ptr = "I16Ptr"     # Pointer to 16-bit signed integer
    I32Ptr = "I32Ptr"     # Pointer to 32-bit signed integer
    I64Ptr = "I64Ptr"     # Pointer to 64-bit signed integer
    U8Ptr = "U8Ptr"       # Pointer to 8-bit unsigned integer
    U16Ptr = "U16Ptr"     # Pointer to 16-bit unsigned integer
    U32Ptr = "U32Ptr"     # Pointer to 32-bit unsigned integer
    U64Ptr = "U64Ptr"     # Pointer to 64-bit unsigned integer
    F32Ptr = "F32Ptr"     # Pointer to 32-bit floating point
    F64Ptr = "F64Ptr"     # Pointer to 64-bit floating point
    BoolPtr = "BoolPtr"   # Pointer to boolean
    VoidPtr = "VoidPtr"   # Pointer to void


class OmniErrorCode(enum.Enum):
    """Error codes for OmniCodex operations"""
    NONE = "NONE"                           # No error
    FUNCTION_NOT_FOUND = "FUNCTION_NOT_FOUND"  # Function not found in OmniCodex
    ARGUMENT_COUNT_MISMATCH = "ARGUMENT_COUNT_MISMATCH"  # Argument count mismatch
    ARGUMENT_TYPE_MISMATCH = "ARGUMENT_TYPE_MISMATCH"  # Argument type mismatch
    NOT_IMPLEMENTED = "NOT_IMPLEMENTED"     # Not implemented
    GPU_NOT_AVAILABLE = "GPU_NOT_AVAILABLE"  # GPU not available
    CFFI_NOT_AVAILABLE = "CFFI_NOT_AVAILABLE"  # CFFI not available
    RUNTIME_ERROR = "RUNTIME_ERROR"         # Runtime error
"#.to_string()
    }
    
    /// Generate CFFI integration
    fn generate_cffi_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate CFFI integration if enabled
        if !self.use_cffi {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex CFFI Integration

This module provides CFFI integration for the OmniCodex framework.
"""

import os
import sys
import logging
import ctypes
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import cffi
    _ffi = cffi.FFI()
except ImportError:
    raise ImportError("CFFI not found. Please install it with 'pip install cffi'")

from .omnicodex import OmniError, OmniErrorCode, logger

# CFFI library handles
_lib_handles = {}


def load_library(lib_name: str, lib_path: Optional[str] = None) -> Any:
    """
    Load a native library using CFFI
    
    Args:
        lib_name: Library name
        lib_path: Library path (optional)
        
    Returns:
        CFFI library handle
        
    Raises:
        OmniError: If library cannot be loaded
    """
    if lib_name in _lib_handles:
        return _lib_handles[lib_name]
    
    try:
        if lib_path:
            lib = _ffi.dlopen(lib_path)
        else:
            lib = _ffi.dlopen(lib_name)
        
        _lib_handles[lib_name] = lib
        return lib
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to load library {lib_name}: {str(e)}"
        ) from e


def unload_library(lib_name: str) -> None:
    """
    Unload a native library
    
    Args:
        lib_name: Library name
    """
    if lib_name in _lib_handles:
        # CFFI doesn't have an explicit unload mechanism, but we can
        # remove the reference to allow garbage collection
        del _lib_handles[lib_name]


def define_function(lib_name: str, func_name: str, signature: str) -> Callable:
    """
    Define a function in a library
    
    Args:
        lib_name: Library name
        func_name: Function name
        signature: Function signature
        
    Returns:
        Function wrapper
        
    Raises:
        OmniError: If function cannot be defined
    """
    lib = _lib_handles.get(lib_name)
    if lib is None:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Library {lib_name} not loaded"
        )
    
    try:
        _ffi.cdef(f"{signature};")
        func = getattr(lib, func_name)
        
        def wrapper(*args: Any) -> Any:
            try:
                return func(*args)
            except Exception as e:
                raise OmniError(
                    OmniErrorCode.RUNTIME_ERROR,
                    f"Error calling {func_name}: {str(e)}"
                ) from e
        
        return wrapper
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to define function {func_name}: {str(e)}"
        ) from e


def array_to_ptr(array: Any) -> Any:
    """
    Convert a Python array to a pointer
    
    Args:
        array: Python array (numpy.ndarray, list, etc.)
        
    Returns:
        CFFI pointer
        
    Raises:
        OmniError: If array cannot be converted
    """
    import numpy as np
    
    try:
        if isinstance(array, np.ndarray):
            # NumPy array
            return _ffi.cast("void*", array.ctypes.data)
        elif isinstance(array, (list, tuple)):
            # Convert list/tuple to NumPy array
            arr = np.array(array)
            return _ffi.cast("void*", arr.ctypes.data)
        else:
            raise TypeError(f"Unsupported array type: {type(array)}")
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to pointer: {str(e)}"
        ) from e


def ptr_to_array(ptr: Any, shape: Tuple[int, ...], dtype: Any) -> Any:
    """
    Convert a pointer to a NumPy array
    
    Args:
        ptr: CFFI pointer
        shape: Array shape
        dtype: NumPy dtype
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If pointer cannot be converted
    """
    import numpy as np
    
    try:
        # Calculate total size
        size = 1
        for dim in shape:
            size *= dim
        
        # Get pointer address
        addr = int(_ffi.cast("uintptr_t", ptr))
        
        # Create NumPy array from memory
        arr = np.ndarray(
            shape=shape,
            dtype=dtype,
            buffer=_ffi.buffer(ptr, size * np.dtype(dtype).itemsize)
        )
        
        # Return a copy to avoid memory issues
        return arr.copy()
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert pointer to array: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'load_library',
    'unload_library',
    'define_function',
    'array_to_ptr',
    'ptr_to_array',
]
"#);
        
        Ok(result)
    }
    
    /// Generate NumPy integration
    fn generate_numpy_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate NumPy integration if enabled
        if !self.use_numpy {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex NumPy Integration

This module provides NumPy integration for the OmniCodex framework.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import numpy as np
except ImportError:
    raise ImportError("NumPy not found. Please install it with 'pip install numpy'")

from .omnicodex import OmniError, OmniErrorCode, ArgType, logger

# Type mapping from ArgType to NumPy dtype
TYPE_MAPPING = {
    ArgType.I8: np.int8,
    ArgType.I16: np.int16,
    ArgType.I32: np.int32,
    ArgType.I64: np.int64,
    ArgType.U8: np.uint8,
    ArgType.U16: np.uint16,
    ArgType.U32: np.uint32,
    ArgType.U64: np.uint64,
    ArgType.F32: np.float32,
    ArgType.F64: np.float64,
    ArgType.Bool: np.bool_,
    ArgType.I8Ptr: np.int8,
    ArgType.I16Ptr: np.int16,
    ArgType.I32Ptr: np.int32,
    ArgType.I64Ptr: np.int64,
    ArgType.U8Ptr: np.uint8,
    ArgType.U16Ptr: np.uint16,
    ArgType.U32Ptr: np.uint32,
    ArgType.U64Ptr: np.uint64,
    ArgType.F32Ptr: np.float32,
    ArgType.F64Ptr: np.float64,
    ArgType.BoolPtr: np.bool_,
}


def arg_type_to_dtype(arg_type: ArgType) -> np.dtype:
    """
    Convert ArgType to NumPy dtype
    
    Args:
        arg_type: ArgType
        
    Returns:
        NumPy dtype
        
    Raises:
        OmniError: If arg_type cannot be converted
    """
    if arg_type in TYPE_MAPPING:
        return TYPE_MAPPING[arg_type]
    
    raise OmniError(
        OmniErrorCode.ARGUMENT_TYPE_MISMATCH,
        f"Cannot convert {arg_type} to NumPy dtype"
    )


def ensure_array(arg: Any, arg_type: ArgType) -> np.ndarray:
    """
    Ensure that an argument is a NumPy array of the correct type
    
    Args:
        arg: Argument
        arg_type: Expected ArgType
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If arg cannot be converted
    """
    try:
        dtype = arg_type_to_dtype(arg_type)
        
        if isinstance(arg, np.ndarray):
            # Already a NumPy array, just convert dtype if needed
            if arg.dtype != dtype:
                return arg.astype(dtype)
            return arg
        
        # Convert to NumPy array
        return np.array(arg, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.ARGUMENT_TYPE_MISMATCH,
            f"Failed to convert argument to NumPy array: {str(e)}"
        ) from e


def create_array(shape: Tuple[int, ...], arg_type: ArgType) -> np.ndarray:
    """
    Create a new NumPy array
    
    Args:
        shape: Array shape
        arg_type: ArgType
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If array cannot be created
    """
    try:
        dtype = arg_type_to_dtype(arg_type)
        return np.zeros(shape, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to create NumPy array: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'TYPE_MAPPING',
    'arg_type_to_dtype',
    'ensure_array',
    'create_array',
]
"#);
        
        Ok(result)
    }
    
    /// Generate CuPy integration
    fn generate_cupy_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate CuPy integration if enabled
        if !self.use_cupy {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex CuPy Integration

This module provides CuPy integration for the OmniCodex framework.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

try:
    import numpy as np
    import cupy as cp
except ImportError:
    raise ImportError("CuPy not found. Please install it with 'pip install cupy'")

from .omnicodex import OmniError, OmniErrorCode, ArgType, logger
from .numpy_utils import arg_type_to_dtype

# Check if CuPy is available and CUDA is installed
try:
    _cuda_available = cp.cuda.is_available()
except Exception:
    _cuda_available = False

# Cached CUDA kernels
_kernel_cache = {}


def is_cuda_available() -> bool:
    """
    Check if CUDA is available
    
    Returns:
        True if CUDA is available
    """
    return _cuda_available


def to_gpu(arr: Any) -> cp.ndarray:
    """
    Convert an array to a CuPy array
    
    Args:
        arr: NumPy array or compatible
        
    Returns:
        CuPy array
        
    Raises:
        OmniError: If arr cannot be converted
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        if isinstance(arr, cp.ndarray):
            return arr
        return cp.asarray(arr)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to GPU: {str(e)}"
        ) from e


def to_cpu(arr: cp.ndarray) -> np.ndarray:
    """
    Convert a CuPy array to a NumPy array
    
    Args:
        arr: CuPy array
        
    Returns:
        NumPy array
        
    Raises:
        OmniError: If arr cannot be converted
    """
    try:
        if isinstance(arr, np.ndarray):
            return arr
        return arr.get()
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to convert array to CPU: {str(e)}"
        ) from e


def create_gpu_array(shape: Tuple[int, ...], arg_type: ArgType) -> cp.ndarray:
    """
    Create a new CuPy array
    
    Args:
        shape: Array shape
        arg_type: ArgType
        
    Returns:
        CuPy array
        
    Raises:
        OmniError: If array cannot be created
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        dtype = arg_type_to_dtype(arg_type)
        return cp.zeros(shape, dtype=dtype)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to create GPU array: {str(e)}"
        ) from e


def compile_kernel(kernel_code: str, kernel_name: str) -> Callable:
    """
    Compile a CUDA kernel
    
    Args:
        kernel_code: CUDA kernel code
        kernel_name: Kernel name
        
    Returns:
        Compiled kernel
        
    Raises:
        OmniError: If kernel cannot be compiled
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    # Check cache
    cache_key = f"{kernel_name}_{hash(kernel_code)}"
    if cache_key in _kernel_cache:
        return _kernel_cache[cache_key]
    
    try:
        # Compile kernel
        module = cp.RawModule(code=kernel_code)
        kernel = module.get_function(kernel_name)
        
        # Cache kernel
        _kernel_cache[cache_key] = kernel
        
        return kernel
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to compile CUDA kernel: {str(e)}"
        ) from e


def launch_kernel(
    kernel: Callable,
    grid: Tuple[int, int, int],
    block: Tuple[int, int, int],
    args: List[Any],
    shared_mem: int = 0
) -> None:
    """
    Launch a CUDA kernel
    
    Args:
        kernel: Compiled kernel
        grid: Grid dimensions
        block: Block dimensions
        args: Kernel arguments
        shared_mem: Shared memory size
        
    Raises:
        OmniError: If kernel cannot be launched
    """
    if not _cuda_available:
        raise OmniError(
            OmniErrorCode.GPU_NOT_AVAILABLE,
            "CUDA is not available"
        )
    
    try:
        # Convert grid and block to tuples
        grid_tuple = (int(grid[0]), int(grid[1]), int(grid[2]))
        block_tuple = (int(block[0]), int(block[1]), int(block[2]))
        
        # Launch kernel
        kernel(grid_tuple, block_tuple, args, shared_mem=shared_mem)
    except Exception as e:
        raise OmniError(
            OmniErrorCode.RUNTIME_ERROR,
            f"Failed to launch CUDA kernel: {str(e)}"
        ) from e


# Module exports
__all__ = [
    'is_cuda_available',
    'to_gpu',
    'to_cpu',
    'create_gpu_array',
    'compile_kernel',
    'launch_kernel',
]
"#);
        
        Ok(result)
    }
}

impl CodeGenerator for PythonCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating Python OmniCodex");
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate main module
        let main_module = self.generate_module(&entries)?;
        
        // Generate additional modules
        let mut additional_modules = Vec::new();
        
        // Generate CFFI integration if enabled
        if self.use_cffi {
            let cffi_module = self.generate_cffi_integration(&entries)?;
            if !cffi_module.is_empty() {
                additional_modules.push(("cffi_utils.py", cffi_module));
            }
        }
        
        // Generate NumPy integration if enabled
        if self.use_numpy {
            let numpy_module = self.generate_numpy_integration(&entries)?;
            if !numpy_module.is_empty() {
                additional_modules.push(("numpy_utils.py", numpy_module));
            }
        }
        
        // Generate CuPy integration if enabled
        if self.use_cupy {
            let cupy_module = self.generate_cupy_integration(&entries)?;
            if !cupy_module.is_empty() {
                additional_modules.push(("cupy_utils.py", cupy_module));
            }
        }
        
        // Generate wrapper code
        let mut wrapper_code = String::new();
        
        for (filename, content) in additional_modules {
            wrapper_code.push_str(&format!("# File: {filename}\n\n"));
            wrapper_code.push_str(&content);
            wrapper_code.push_str("\n\n");
        }
        
        // Generate __init__.py
        wrapper_code.push_str("# File: __init__.py\n\n");
        wrapper_code.push_str(r#"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OmniCodex Python Integration

Generated by OmniForge - The OmniCodex Compiler Framework
"""

from .omnicodex import (
    OmniTarget,
    ArgType,
    OmniErrorCode,
    OmniError,
    ComputeMetadata,
    OmniCodexEntry,
    OMNI_CODEX,
    find_function,
    execute,
    api,
    is_gpu_available,
    is_cffi_available,
)

# Version
__version__ = '0.1.0'
"#);
        
        if self.use_numpy {
            wrapper_code.push_str("\n# NumPy integration\nfrom .numpy_utils import (\n    arg_type_to_dtype,\n    ensure_array,\n    create_array,\n)\n");
        }
        
        if self.use_cupy {
            wrapper_code.push_str("\n# CuPy integration\ntry:\n    from .cupy_utils import (\n        is_cuda_available,\n        to_gpu,\n        to_cpu,\n        create_gpu_array,\n        compile_kernel,\n        launch_kernel,\n    )\nexcept ImportError:\n    pass\n");
        }
        
        if self.use_cffi {
            wrapper_code.push_str("\n# CFFI integration\ntry:\n    from .cffi_utils import (\n        load_library,\n        unload_library,\n        define_function,\n        array_to_ptr,\n        ptr_to_array,\n    )\nexcept ImportError:\n    pass\n");
        }
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code: main_module,
            wrapper_code: Some(wrapper_code),
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, _metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating Python wrappers");
        
        // This is handled in the main codex generation
        Ok(String::new())
    }
}

impl Default for PythonCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}



Directory: src\codegen
File: cu_codegen.rs
===================
// src/codegen/cu_codegen.rs
//! CUDA code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating CUDA code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of CUDA
//! kernels into the OmniCodex framework with appropriate launch configurations.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::fmt::Write as FmtWrite;
use crate::error::{OmniError, OmniResult};
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction, FunctionType};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, Codegen, ArgType};

/// CUDA code generator
pub struct CudaCodeGenerator {
    /// CUDA compute capability
    compute_capability: String,
    
    /// Use unified memory (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_unified_memory: bool,

    /// Use managed memory (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_managed_memory: bool,

    /// Use CUDA dynamic parallelism (planned for future CUDA feature support)
    #[allow(dead_code)]
    use_dynamic_parallelism: bool,
}

impl CudaCodeGenerator {
    /// Create a new CUDA code generator
    pub fn new() -> Self {
        Self {
            compute_capability: "7.5".to_string(),
            use_unified_memory: true,
            use_managed_memory: false,
            use_dynamic_parallelism: false,
        }
    }
    
    /// Create a new CUDA code generator with specific configuration
    pub fn with_config(
        compute_capability: String,
        use_unified_memory: bool,
        use_managed_memory: bool,
        use_dynamic_parallelism: bool,
    ) -> Self {
        Self {
            compute_capability,
            use_unified_memory,
            use_managed_memory,
            use_dynamic_parallelism,
        }
    }
    
    /// Generate CUDA type from argument type
    fn generate_cuda_type(&self, arg_type: &ArgType) -> String {
        match arg_type {
            ArgType::Void => "void".to_string(),
            ArgType::I8 => "int8_t".to_string(),
            ArgType::I16 => "int16_t".to_string(),
            ArgType::I32 => "int32_t".to_string(),
            ArgType::I64 => "int64_t".to_string(),
            ArgType::U8 => "uint8_t".to_string(),
            ArgType::U16 => "uint16_t".to_string(),
            ArgType::U32 => "uint32_t".to_string(),
            ArgType::U64 => "uint64_t".to_string(),
            ArgType::F32 => "float".to_string(),
            ArgType::F64 => "double".to_string(),
            ArgType::Bool => "bool".to_string(),
            ArgType::I8Ptr => "int8_t*".to_string(),
            ArgType::I16Ptr => "int16_t*".to_string(),
            ArgType::I32Ptr => "int32_t*".to_string(),
            ArgType::I64Ptr => "int64_t*".to_string(),
            ArgType::U8Ptr => "uint8_t*".to_string(),
            ArgType::U16Ptr => "uint16_t*".to_string(),
            ArgType::U32Ptr => "uint32_t*".to_string(),
            ArgType::U64Ptr => "uint64_t*".to_string(),
            ArgType::F32Ptr => "float*".to_string(),
            ArgType::F64Ptr => "double*".to_string(),
            ArgType::BoolPtr => "bool*".to_string(),
            ArgType::VoidPtr => "void*".to_string(),
            ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate CUDA function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Add appropriate CUDA qualifiers
        let qualifier = match function.function_type {
            FunctionType::Kernel => "__global__",
            FunctionType::Device => "__device__",
            FunctionType::Host => "",
        };
        
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            
            self.generate_cuda_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ))
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cuda_type = self.generate_cuda_type(&arg_type);
                        format!("{cuda_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        if params == "void" {
            Ok(format!("{} {} {}()", qualifier, return_type, function.name))
        } else {
            Ok(format!("{} {} {}({})", qualifier, return_type, function.name, params))
        }
    }
    
    /// Generate kernel launch wrapper
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "void".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let cuda_type = self.generate_cuda_type(&arg_type);
                        format!("{cuda_type} arg{i}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "void".to_string()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("dim3({}, {}, {})", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("dim3({}, {}, {})", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "dim3(1, 1, 1)".to_string(),
                "dim3(256, 1, 1)".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/**
 * Launch wrapper for the CUDA kernel `{function_name}`
 * 
 * This function encapsulates the kernel launch configuration and
 * provides a C-compatible interface for the OmniCodex framework.
 */
extern "C" void launch_{function_name}({params}) {{
    // Launch parameters
    {grid_dim_def} gridDim = {grid_dim};
    {block_dim_def} blockDim = {block_dim};
    size_t sharedMemBytes = {shared_mem};
    
    // Configure CUDA stream (optional)
    cudaStream_t stream = 0;
    
    // Launch kernel
    {function_name}<<<gridDim, blockDim, sharedMemBytes, stream>>>({args});
    
    // Check for launch errors (optional)
    cudaError_t err = cudaGetLastError();
    if (err != cudaSuccess) {{
        // Handle error
        printf("CUDA Error: %s\n", cudaGetErrorString(err));
    }}
    
    // Synchronize (comment out for asynchronous operation)
    cudaDeviceSynchronize();
}}
"#,
            function_name = function.name,
            params = if params == "void" { "".to_string() } else { params.to_string() },
            grid_dim_def = "const dim3",
            block_dim_def = "const dim3",
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = if params == "void" {
                "".to_string()
            } else {
                (0..params.split(", ").count())
                    .map(|i| format!("arg{i}"))
                    .collect::<Vec<_>>()
                    .join(", ")
            },
        ));
        
        Ok(result)
    }
    
    /// Generate header content
    fn generate_header(&self) -> String {
        format!(
            r#"/**
 * OmniCodex CUDA integration generated by OmniForge.
 *
 * This file contains CUDA kernel declarations and launch wrappers
 * for the OmniCodex framework. It enables seamless integration of
 * CUDA kernels with the OmniCodex dispatch system.
 *
 * Target compute capability: {compute_capability}
 */

#ifndef OMNICODEX_CUDA_H
#define OMNICODEX_CUDA_H

#include <cuda_runtime.h>
#include <stdint.h>
#include <stdio.h>

// Forward declarations of kernel functions
"#,
            compute_capability = self.compute_capability,
        )
    }
    
    /// Generate implementation content header
    fn generate_implementation_header(&self) -> String {
        format!(
            r#"/**
 * OmniCodex CUDA integration generated by OmniForge.
 *
 * This file contains CUDA kernel implementations and launch wrappers
 * for the OmniCodex framework. It enables seamless integration of
 * CUDA kernels with the OmniCodex dispatch system.
 *
 * Target compute capability: {compute_capability}
 */

#include "omnicodex_cuda.h"

// Utility macros and functions
#define CUDA_CHECK(call) \
    do {{ \
        cudaError_t err = call; \
        if (err != cudaSuccess) {{ \
            printf("CUDA error in %s at line %d: %s\n", \
                   __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        }} \
    }} while(0)

// Implementation of kernel launch wrappers
"#,
            compute_capability = self.compute_capability,
        )
    }
    
    /// Generate footer content
    fn generate_footer(&self) -> String {
        r#"
#endif // OMNICODEX_CUDA_H
"#.to_string()
    }
    
    /// Generate OmniCodex utilities for CUDA
    fn generate_utilities(&self) -> String {
        r#"
/**
 * OmniCodex CUDA utilities
 */
namespace omnicodex {
namespace cuda {

/**
 * Initialize CUDA runtime
 * 
 * @return cudaError_t Result of initialization
 */
inline cudaError_t initialize() {
    cudaError_t err = cudaFree(0);  // Simple call to initialize context
    return err;
}

/**
 * Get device properties
 * 
 * @param prop Pointer to cudaDeviceProp structure
 * @param device Device ID (default: 0)
 * @return cudaError_t Result of operation
 */
inline cudaError_t get_device_properties(cudaDeviceProp* prop, int device = 0) {
    return cudaGetDeviceProperties(prop, device);
}

/**
 * Get optimal block size for a kernel
 * 
 * @param kernel Kernel function pointer
 * @return dim3 Optimal block dimensions
 */
template<typename KernelFunc>
inline dim3 get_optimal_block_size(KernelFunc kernel) {
    int blockSize = 0;
    int minGridSize = 0;
    
    cudaOccupancyMaxPotentialBlockSize(&minGridSize, &blockSize, kernel, 0, 0);
    
    return dim3(blockSize, 1, 1);
}

/**
 * Calculate grid size based on problem size and block size
 * 
 * @param problemSize Total number of threads needed
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size(size_t problemSize, dim3 blockSize) {
    size_t numBlocks = (problemSize + blockSize.x - 1) / blockSize.x;
    return dim3(numBlocks, 1, 1);
}

/**
 * Calculate 2D grid size based on problem dimensions and block size
 * 
 * @param width Width of the problem
 * @param height Height of the problem
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size_2d(size_t width, size_t height, dim3 blockSize) {
    dim3 gridSize;
    gridSize.x = (width + blockSize.x - 1) / blockSize.x;
    gridSize.y = (height + blockSize.y - 1) / blockSize.y;
    gridSize.z = 1;
    return gridSize;
}

/**
 * Calculate 3D grid size based on problem dimensions and block size
 * 
 * @param width Width of the problem
 * @param height Height of the problem
 * @param depth Depth of the problem
 * @param blockSize Block dimensions
 * @return dim3 Grid dimensions
 */
inline dim3 calculate_grid_size_3d(size_t width, size_t height, size_t depth, dim3 blockSize) {
    dim3 gridSize;
    gridSize.x = (width + blockSize.x - 1) / blockSize.x;
    gridSize.y = (height + blockSize.y - 1) / blockSize.y;
    gridSize.z = (depth + blockSize.z - 1) / blockSize.z;
    return gridSize;
}

} // namespace cuda
} // namespace omnicodex
"#.to_string()
    }
    
    /// Generate kernels and launch wrappers
    fn generate_kernels(&self, metadata: &[ExtractedMetadata]) -> OmniResult<(String, String)> {
        let mut header = String::new();
        let mut implementation = String::new();
        
        // Collect all kernel functions
        let mut kernel_functions = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    kernel_functions.push(function);
                }
            }
        }
        
        // Generate kernel declarations
        for function in &kernel_functions {
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add declaration to header
            writeln!(header, "{signature};").map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
        }
        
        // Generate kernel launch wrappers
        header.push_str("\n// Kernel launch wrapper declarations\n");
        
        for function in &kernel_functions {
            // Extract parameter types
            let params = if let Some(signature) = &function.signature {
                if signature.parameter_types.is_empty() {
                    "void".to_string()
                } else {
                    signature
                        .parameter_types
                        .iter()
                        .enumerate()
                        .map(|(i, param)| {
                            let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                            let cuda_type = self.generate_cuda_type(&arg_type);
                            format!("{cuda_type} arg{i}")
                        })
                        .collect::<Vec<_>>()
                        .join(", ")
                }
            } else {
                "void".to_string()
            };
            
            // Add declaration to header
            if params == "void" {
                writeln!(header, "extern \"C\" void launch_{}();", function.name).map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
            } else {
                writeln!(header, "extern \"C\" void launch_{}({});", function.name, params).map_err(|e| OmniError::General(format!("Failed to write to string: {e}")))?;
            }
            
            // Add implementation
            implementation.push_str(&self.generate_kernel_launcher(function)?);
        }
        
        Ok((header, implementation))
    }
}

impl CodeGenerator for CudaCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating CUDA OmniCodex");
        
        // Generate header
        let mut header_content = self.generate_header();
        
        // Generate implementation header
        let mut implementation_content = self.generate_implementation_header();
        
        // Generate kernels and launch wrappers
        let (kernel_declarations, kernel_implementations) = self.generate_kernels(metadata)?;
        
        // Add kernel declarations to header
        header_content.push_str(&kernel_declarations);
        
        // Add utilities to header
        header_content.push_str(&self.generate_utilities());
        
        // Add footer to header
        header_content.push_str(&self.generate_footer());
        
        // Add kernel implementations to implementation
        implementation_content.push_str(&kernel_implementations);
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                        entries.push(entry);
                    } else {
                        log::warn!("Failed to map function {} to codex entry", function.name);
                    }
                }
            }
        }
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CUDA_CODEX".to_string(),
            entries,
            code: implementation_content,
            wrapper_code: None,
            header_code: Some(header_content),
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating CUDA wrappers");
        
        let mut wrappers = String::new();
        
        wrappers.push_str(r#"/**
 * OmniCodex CUDA wrapper functions generated by OmniForge.
 *
 * This file contains wrapper functions for CUDA kernels in the
 * OmniCodex framework.
 */

#include "omnicodex_cuda.h"

/**
 * Utility functions for wrapping CUDA operations
 */
"#);
        
        // Generate wrapper functions
        for meta in metadata {
            for function in &meta.functions {
                if function.function_type == FunctionType::Kernel {
                    // We've already generated the launch wrappers in the main file
                    // This is just for additional utility functions
                }
            }
        }
        
        Ok(wrappers)
    }
}

impl Default for CudaCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}



Directory: src\codegen
File: rs_codegen.rs
===================
// src/codegen/rs_codegen.rs
//! Rust code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating Rust code for the OmniCodex
//! dispatch tables and wrapper functions. It creates zero-cost abstractions for
//! heterogeneous computing using static dispatch tables.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen};

/// Rust code generator
pub struct RustCodeGenerator {
    // Configuration options can be added here
}

impl Default for RustCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[allow(dead_code)]
impl RustCodeGenerator {
    /// Create a new Rust code generator
    pub fn new() -> Self {
        Self {}
    }
    
    /// Generate Rust type from argument type
    fn generate_rust_type(&self, arg_type: &super::ArgType) -> String {
        match arg_type {
            super::ArgType::Void => "()".to_string(),
            super::ArgType::I8 => "i8".to_string(),
            super::ArgType::I16 => "i16".to_string(),
            super::ArgType::I32 => "i32".to_string(),
            super::ArgType::I64 => "i64".to_string(),
            super::ArgType::U8 => "u8".to_string(),
            super::ArgType::U16 => "u16".to_string(),
            super::ArgType::U32 => "u32".to_string(),
            super::ArgType::U64 => "u64".to_string(),
            super::ArgType::F32 => "f32".to_string(),
            super::ArgType::F64 => "f64".to_string(),
            super::ArgType::Bool => "bool".to_string(),
            super::ArgType::I8Ptr => "*mut i8".to_string(),
            super::ArgType::I16Ptr => "*mut i16".to_string(),
            super::ArgType::I32Ptr => "*mut i32".to_string(),
            super::ArgType::I64Ptr => "*mut i64".to_string(),
            super::ArgType::U8Ptr => "*mut u8".to_string(),
            super::ArgType::U16Ptr => "*mut u16".to_string(),
            super::ArgType::U32Ptr => "*mut u32".to_string(),
            super::ArgType::U64Ptr => "*mut u64".to_string(),
            super::ArgType::F32Ptr => "*mut f32".to_string(),
            super::ArgType::F64Ptr => "*mut f64".to_string(),
            super::ArgType::BoolPtr => "*mut bool".to_string(),
            super::ArgType::VoidPtr => "*mut std::ffi::c_void".to_string(),
            super::ArgType::Custom(name) => name.clone(),
        }
    }
    
    /// Generate Rust function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let rust_type = self.generate_rust_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if rust_type == "()" {
                "".to_string()
            } else {
                format!(" -> {rust_type}")
            }
        } else {
            "".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            signature
                .parameter_types
                .iter()
                .enumerate()
                .map(|(i, param)| {
                    let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                    let rust_type = self.generate_rust_type(&arg_type);
                    format!("arg{i}: {rust_type}")
                })
                .collect::<Vec<_>>()
                .join(", ")
        } else {
            String::new()
        };
        
        Ok(format!("fn {}({}){}", function.name, params, return_type))
    }
    
    /// Generate extern "C" block for FFI functions
    fn generate_extern_block(&self, functions: &[ExtractedFunction]) -> OmniResult<String> {
        let mut result = String::new();
        
        result.push_str("extern \"C\" {\n");
        
        for function in functions {
            // Skip non-host functions
            if function.function_type != crate::metadata_extractor::FunctionType::Host {
                continue;
            }
            
            // Generate function signature
            let signature = self.generate_function_signature(function)?;
            
            // Add extern declaration
            result.push_str(&format!("    {signature};\n"));
        }
        
        result.push_str("}\n");
        
        Ok(result)
    }
    
    /// Generate CUDA kernel launch function
    fn generate_kernel_launcher(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Skip non-kernel functions
        if function.function_type != crate::metadata_extractor::FunctionType::Kernel {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            signature
                .parameter_types
                .iter()
                .enumerate()
                .map(|(i, param)| {
                    let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                    let rust_type = self.generate_rust_type(&arg_type);
                    format!("arg{i}: {rust_type}")
                })
                .collect::<Vec<_>>()
                .join(", ")
        } else {
            String::new()
        };
        
        // Extract launch parameters
        let (grid_dim, block_dim, shared_mem) = if let Some(launch_params) = &function.launch_params {
            (
                format!("[{}, {}, {}]", launch_params.grid_dim[0], launch_params.grid_dim[1], launch_params.grid_dim[2]),
                format!("[{}, {}, {}]", launch_params.block_dim[0], launch_params.block_dim[1], launch_params.block_dim[2]),
                launch_params.shared_mem_bytes,
            )
        } else {
            (
                "[1, 1, 1]".to_string(),
                "[256, 1, 1]".to_string(),
                0,
            )
        };
        
        // Generate function
        result.push_str(&format!(
            r#"
/// Launch the CUDA kernel `{function_name}`
///
/// # Safety
///
/// This function is unsafe because it launches a CUDA kernel, which involves
/// raw pointers and FFI calls.
pub unsafe fn launch_{function_name}({params}) {{
    extern "C" {{
        fn {function_name}({params});
    }}
    
    // Launch parameters
    const GRID_DIM: [u32; 3] = {grid_dim};
    const BLOCK_DIM: [u32; 3] = {block_dim};
    const SHARED_MEM: usize = {shared_mem};
    
    // TODO: Replace with actual CUDA launch implementation
    // This is a placeholder for the actual CUDA kernel launch
    cuda_runtime::launch_kernel(
        {function_name} as *const std::ffi::c_void,
        GRID_DIM,
        BLOCK_DIM,
        SHARED_MEM,
        &[{args}],
    );
}}
"#,
            function_name = function.name,
            params = params,
            grid_dim = grid_dim,
            block_dim = block_dim,
            shared_mem = shared_mem,
            args = (0..params.split(", ").count())
                .map(|i| format!("&arg{i} as *const _ as *const std::ffi::c_void"))
                .collect::<Vec<_>>()
                .join(", "),
        ));
        
        Ok(result)
    }
    
    /// Generate OmniCodex entry struct definition
    fn generate_codex_entry_struct(&self) -> String {
        r#"
/// Entry in the OmniCodex dispatch table
#[derive(Debug, Clone)]
pub struct OmniCodexEntry {
    /// Function name
    pub name: &'static str,
    
    /// Target type (CPU, GPU, etc.)
    pub target: OmniTarget,
    
    /// Metadata for the function
    pub metadata: ComputeMetadata,
}

/// Target type
#[derive(Debug, Clone)]
pub enum OmniTarget {
    /// CPU function
    CPU(unsafe fn()),
    
    /// GPU function (CUDA kernel)
    GPU(unsafe fn()),
    
    /// CPU with SIMD
    CPUSIMD(unsafe fn()),
}

/// Compute metadata
#[derive(Debug, Clone)]
pub struct ComputeMetadata {
    /// Grid size (for CUDA kernels)
    pub grid_size: (u32, u32, u32),
    
    /// Block size (for CUDA kernels)
    pub block_size: (u32, u32, u32),
    
    /// Shared memory size (for CUDA kernels)
    pub shared_mem: usize,
    
    /// Argument types
    pub args_layout: &'static [ArgType],
}

/// Argument type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ArgType {
    /// Void
    Void,
    /// 8-bit integer
    I8,
    /// 16-bit integer
    I16,
    /// 32-bit integer
    I32,
    /// 64-bit integer
    I64,
    /// 8-bit unsigned integer
    U8,
    /// 16-bit unsigned integer
    U16,
    /// 32-bit unsigned integer
    U32,
    /// 64-bit unsigned integer
    U64,
    /// 32-bit float
    F32,
    /// 64-bit float
    F64,
    /// Boolean
    Bool,
    /// Pointer to 8-bit integer
    I8Ptr,
    /// Pointer to 16-bit integer
    I16Ptr,
    /// Pointer to 32-bit integer
    I32Ptr,
    /// Pointer to 64-bit integer
    I64Ptr,
    /// Pointer to 8-bit unsigned integer
    U8Ptr,
    /// Pointer to 16-bit unsigned integer
    U16Ptr,
    /// Pointer to 32-bit unsigned integer
    U32Ptr,
    /// Pointer to 64-bit unsigned integer
    U64Ptr,
    /// Pointer to 32-bit float
    F32Ptr,
    /// Pointer to 64-bit float
    F64Ptr,
    /// Pointer to boolean
    BoolPtr,
    /// Pointer to void
    VoidPtr,
}

impl ComputeMetadata {
    /// Create CPU metadata
    pub const fn cpu() -> Self {
        Self {
            grid_size: (1, 1, 1),
            block_size: (1, 1, 1),
            shared_mem: 0,
            args_layout: &[],
        }
    }
    
    /// Create CPU SIMD metadata
    pub const fn cpu_simd() -> Self {
        Self {
            grid_size: (1, 1, 1),
            block_size: (1, 1, 1),
            shared_mem: 0,
            args_layout: &[],
        }
    }
    
    /// Create GPU metadata
    pub const fn gpu(grid_size: (u32, u32, u32), block_size: (u32, u32, u32), shared_mem: usize, args_layout: &'static [ArgType]) -> Self {
        Self {
            grid_size,
            block_size,
            shared_mem,
            args_layout,
        }
    }
}
"#.to_string()
    }
    
    /// Generate ArgType array for a function
    fn generate_arg_types(&self, entry: &CodexEntry) -> String {
        if entry.metadata.arg_layout.is_empty() {
            return "&[]".to_string();
        }
        
        let arg_types = entry
            .metadata
            .arg_layout
            .iter()
            .map(|arg| format!("ArgType::{arg}"))
            .collect::<Vec<_>>()
            .join(", ");
        
        format!("&[{arg_types}]")
    }
    
    /// Generate OmniCodex dispatch table
    fn generate_dispatch_table(&self, entries: &[CodexEntry]) -> String {
        let mut result = String::new();
        
        result.push_str("/// OmniCodex dispatch table\npub static OMNI_CODEX: &[OmniCodexEntry] = &[\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("({}, {}, {})", grid[0], grid[1], grid[2]),
                    format!("({}, {}, {})", block[0], block[1], block[2]),
                )
            } else {
                ("(1, 1, 1)".to_string(), "(256, 1, 1)".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = self.generate_arg_types(entry);
            
            // Generate entry
            result.push_str(&format!(
                r#"    OmniCodexEntry {{
        name: "{}",
        target: OmniTarget::{}({}),
        metadata: ComputeMetadata {{
            grid_size: {},
            block_size: {},
            shared_mem: {},
            args_layout: {},
        }},
    }},
"#,
                entry.name,
                entry.target_type,
                entry.function_pointer,
                grid_size,
                block_size,
                shared_mem,
                args_layout,
            ));
        }
        
        result.push_str("];\n");
        
        result
    }
    
    /// Generate module documentation
    fn generate_module_doc(&self) -> String {
        r#"//! OmniCodex dispatch table generated by OmniForge.
//!
//! This module contains the OmniCodex dispatch table, which provides a zero-cost
//! abstraction for heterogeneous computing. It allows calling functions on
//! different backends (CPU, GPU, etc.) with a unified interface.
//!
//! # Example
//!
//! ```
//! use omnicodex::execute;
//!
//! fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // Execute a function by name
//!     let result = unsafe { execute::<f32>("dot_product", &[&a, &b, &c])? };
//!     println!("Result: {}", result);
//!     Ok(())
//! }
//! ```
"#.to_string()
    }
    
    /// Generate helper functions for the OmniCodex
    fn generate_helper_functions(&self) -> String {
        r#"
/// Execute a function by name
///
/// # Safety
///
/// This function is unsafe because it involves FFI calls and raw pointers.
///
/// # Arguments
///
/// * `name` - Function name
/// * `args` - Function arguments
///
/// # Returns
///
/// * `Result<T, OmniCodexError>` - Function result
pub unsafe fn execute<T>(name: &str, args: &[&dyn std::any::Any]) -> Result<T, OmniCodexError> {
    // Find the function in the dispatch table
    let entry = OMNI_CODEX
        .iter()
        .find(|e| e.name == name)
        .ok_or(OmniCodexError::FunctionNotFound(name.to_string()))?;
    
    // Check argument count
    if args.len() != entry.metadata.args_layout.len() {
        return Err(OmniCodexError::ArgumentCountMismatch {
            expected: entry.metadata.args_layout.len(),
            actual: args.len(),
        });
    }
    
    // TODO: Implement actual function execution
    // This is a placeholder for the actual function execution
    match entry.target {
        OmniTarget::CPU(func) => {
            // Execute CPU function
            // ...
        }
        OmniTarget::GPU(func) => {
            // Execute GPU function
            // ...
        }
        OmniTarget::CPUSIMD(func) => {
            // Execute CPU SIMD function
            // ...
        }
    }
    
    // Placeholder for return value
    Err(OmniCodexError::NotImplemented)
}

/// OmniCodex error
#[derive(Debug, thiserror::Error)]
pub enum OmniCodexError {
    /// Function not found
    #[error("Function not found: {0}")]
    FunctionNotFound(String),
    
    /// Argument count mismatch
    #[error("Argument count mismatch: expected {expected}, got {actual}")]
    ArgumentCountMismatch {
        /// Expected argument count
        expected: usize,
        
        /// Actual argument count
        actual: usize,
    },
    
    /// Argument type mismatch
    #[error("Argument type mismatch at index {index}: expected {expected}, got {actual}")]
    ArgumentTypeMismatch {
        /// Argument index
        index: usize,
        
        /// Expected type
        expected: String,
        
        /// Actual type
        actual: String,
    },
    
    /// Not implemented
    #[error("Not implemented")]
    NotImplemented,
}

/// Simple CUDA runtime module for launching kernels
///
/// This is a placeholder for the actual CUDA runtime implementation.
#[doc(hidden)]
pub mod cuda_runtime {
    /// Launch a CUDA kernel
    ///
    /// # Safety
    ///
    /// This function is unsafe because it launches a CUDA kernel, which involves
    /// raw pointers and FFI calls.
    pub unsafe fn launch_kernel(
        kernel: *const std::ffi::c_void,
        grid_dim: [u32; 3],
        block_dim: [u32; 3],
        shared_mem: usize,
        args: &[*const std::ffi::c_void],
    ) {
        // This is a placeholder for the actual CUDA kernel launch
        // In a real implementation, this would call the CUDA runtime API
    }
}
"#.to_string()
    }
    
    /// Generate imports section
    fn generate_imports(&self) -> String {
        "// No imports needed for generated code\n".to_string()
    }
}

impl CodeGenerator for RustCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating Rust OmniCodex");
        
        // Generate header
        let mut code = String::new();
        code.push_str(&self.generate_module_doc());
        code.push('\n');
        code.push_str(&self.generate_imports());
        code.push('\n');
        
        // Generate codex entry struct
        code.push_str(&self.generate_codex_entry_struct());
        code.push('\n');
        
        // Collect all functions
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate dispatch table
        code.push_str(&self.generate_dispatch_table(&entries));
        code.push('\n');
        
        // Generate helper functions
        code.push_str(&self.generate_helper_functions());
        
        // Generate wrapper code if requested
        let wrapper_code = if options.generate_wrappers {
            Some(self.generate_wrappers(metadata, options)?)
        } else {
            None
        };
        
        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code,
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating Rust wrappers");
        
        let mut code = String::new();
        
        // Generate header
        code.push_str("//! OmniCodex wrapper functions generated by OmniForge.\n");
        code.push_str("//!\n");
        code.push_str("//! This module contains wrapper functions for the OmniCodex dispatch table.\n");
        code.push_str("//! These wrappers provide a type-safe interface to the functions in the\n");
        code.push_str("//! OmniCodex dispatch table.\n");
        code.push('\n');
        
        // Generate imports
        code.push_str("use super::{execute, OmniCodexError};\n\n");
        
        // Generate wrappers for each function
        let mut processed_functions = HashMap::new();
        
        for meta in metadata {
            for function in &meta.functions {
                // Skip if we've already processed this function
                if processed_functions.contains_key(&function.name) {
                    continue;
                }
                
                // Generate function signature
                if let Some(signature) = &function.signature {
                    // Extract return type
                    let return_type = Codegen::map_type_to_arg_type(
                        &signature.return_type.name,
                        signature.return_type.is_pointer,
                    );
                    let rust_return_type = self.generate_rust_type(&return_type);
                    
                    // Extract parameter types
                    let params = signature
                        .parameter_types
                        .iter()
                        .enumerate()
                        .map(|(i, param)| {
                            let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                            let rust_type = self.generate_rust_type(&arg_type);
                            format!("arg{i}: {rust_type}")
                        })
                        .collect::<Vec<_>>()
                        .join(", ");
                    
                    // Generate wrapper function
                    let func_name = function.name.to_lowercase();
                    
                    code.push_str(&format!(
                        r#"/// Wrapper for the `{}` function
///
/// # Safety
///
/// This function is unsafe because it involves FFI calls and raw pointers.
pub unsafe fn {}({}) -> Result<{}, OmniCodexError> {{
    execute::<{}>("{}", &[{}])
}}

"#,
                        function.name,
                        func_name,
                        params,
                        rust_return_type,
                        rust_return_type,
                        function.name,
                        (0..signature.parameter_types.len())
                            .map(|i| format!("&arg{i}"))
                            .collect::<Vec<_>>()
                            .join(", "),
                    ));
                    
                    // Mark this function as processed
                    processed_functions.insert(function.name.clone(), true);
                }
            }
        }
        
        Ok(code)
    }
}



Directory: src\codegen
File: mod.rs
============
// src/codegen/mod.rs
//! Code generation for the OmniForge compiler.
//!
//! This module provides functionality for generating code for the OmniCodex
//! dispatch tables and wrapper functions.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use serde::{Serialize, Deserialize};

use crate::error::{OmniError, OmniResult};
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction, FunctionType};
use crate::ahaw::{self, AccelerationHint, TaskCharacteristics};


mod c_codegen;
mod cpp_codegen;
mod cu_codegen;
mod rs_codegen;
mod py_codegen;
mod ts_codegen;

pub use self::c_codegen::CCodeGenerator;
pub use self::cpp_codegen::CppCodeGenerator;
pub use self::cu_codegen::CudaCodeGenerator;
pub use self::rs_codegen::RustCodeGenerator;
pub use self::py_codegen::PythonCodeGenerator;
pub use self::ts_codegen::TypeScriptCodeGenerator;

/// Code generation options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodegenOptions {
    /// Target language
    pub target_language: String,
    
    /// Generate documentation comments
    pub generate_docs: bool,
    
    /// Generate type-safe wrappers
    pub generate_wrappers: bool,
    
    /// Generate error handling code
    pub generate_error_handling: bool,
    
    /// Include file paths in generated code
    pub include_file_paths: bool,
}

impl Default for CodegenOptions {
    fn default() -> Self {
        Self {
            target_language: "rust".to_string(),
            generate_docs: true,
            generate_wrappers: true,
            generate_error_handling: true,
            include_file_paths: true,
        }
    }
}

/// CodexEntry represents an entry in the OmniCodex dispatch table
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodexEntry {
    /// Function name
    pub name: String,
    
    /// Target type (CPU, GPU, etc.)
    pub target_type: TargetType,
    
    /// Function pointer
    pub function_pointer: String,
    
    /// Metadata for the function
    pub metadata: FunctionMetadata,
}

/// Target type
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TargetType {
    /// CPU (default)
    CPU,
    
    /// GPU (CUDA)
    GPU,
    
    /// CPU with SIMD
    CPUSIMD,
    
    /// TPU
    TPU,
    
    /// FPGA
    FPGA,
    
    /// Other
    Other,
}

impl std::fmt::Display for TargetType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TargetType::CPU => write!(f, "CPU"),
            TargetType::GPU => write!(f, "GPU"),
            TargetType::CPUSIMD => write!(f, "CPUSIMD"),
            TargetType::TPU => write!(f, "TPU"),
            TargetType::FPGA => write!(f, "FPGA"),
            TargetType::Other => write!(f, "Other"),
        }
    }
}

/// Function metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FunctionMetadata {
    /// Argument layout
    pub arg_layout: Vec<ArgType>,
    
    /// Return type
    pub return_type: ArgType,
    
    /// Grid size (for CUDA kernels)
    pub grid_size: Option<[u32; 3]>,
    
    /// Block size (for CUDA kernels)
    pub block_size: Option<[u32; 3]>,
    
    /// Shared memory size (for CUDA kernels)
    pub shared_memory: Option<usize>,
    
    /// Is the function a kernel
    pub is_kernel: bool,
}

/// Argument type
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ArgType {
    /// Void
    Void,
    
    /// 8-bit integer
    I8,
    
    /// 16-bit integer
    I16,
    
    /// 32-bit integer
    I32,
    
    /// 64-bit integer
    I64,
    
    /// 8-bit unsigned integer
    U8,
    
    /// 16-bit unsigned integer
    U16,
    
    /// 32-bit unsigned integer
    U32,
    
    /// 64-bit unsigned integer
    U64,
    
    /// 32-bit float
    F32,
    
    /// 64-bit float
    F64,
    
    /// Boolean
    Bool,
    
    /// Pointer to 8-bit integer
    I8Ptr,
    
    /// Pointer to 16-bit integer
    I16Ptr,
    
    /// Pointer to 32-bit integer
    I32Ptr,
    
    /// Pointer to 64-bit integer
    I64Ptr,
    
    /// Pointer to 8-bit unsigned integer
    U8Ptr,
    
    /// Pointer to 16-bit unsigned integer
    U16Ptr,
    
    /// Pointer to 32-bit unsigned integer
    U32Ptr,
    
    /// Pointer to 64-bit unsigned integer
    U64Ptr,
    
    /// Pointer to 32-bit float
    F32Ptr,
    
    /// Pointer to 64-bit float
    F64Ptr,
    
    /// Pointer to boolean
    BoolPtr,
    
    /// Pointer to void
    VoidPtr,
    
    /// Custom type
    Custom(String),
}

impl std::fmt::Display for ArgType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ArgType::Void => write!(f, "Void"),
            ArgType::I8 => write!(f, "I8"),
            ArgType::I16 => write!(f, "I16"),
            ArgType::I32 => write!(f, "I32"),
            ArgType::I64 => write!(f, "I64"),
            ArgType::U8 => write!(f, "U8"),
            ArgType::U16 => write!(f, "U16"),
            ArgType::U32 => write!(f, "U32"),
            ArgType::U64 => write!(f, "U64"),
            ArgType::F32 => write!(f, "F32"),
            ArgType::F64 => write!(f, "F64"),
            ArgType::Bool => write!(f, "Bool"),
            ArgType::I8Ptr => write!(f, "I8Ptr"),
            ArgType::I16Ptr => write!(f, "I16Ptr"),
            ArgType::I32Ptr => write!(f, "I32Ptr"),
            ArgType::I64Ptr => write!(f, "I64Ptr"),
            ArgType::U8Ptr => write!(f, "U8Ptr"),
            ArgType::U16Ptr => write!(f, "U16Ptr"),
            ArgType::U32Ptr => write!(f, "U32Ptr"),
            ArgType::U64Ptr => write!(f, "U64Ptr"),
            ArgType::F32Ptr => write!(f, "F32Ptr"),
            ArgType::F64Ptr => write!(f, "F64Ptr"),
            ArgType::BoolPtr => write!(f, "BoolPtr"),
            ArgType::VoidPtr => write!(f, "VoidPtr"),
            ArgType::Custom(name) => write!(f, "Custom({name})"),
        }
    }
}

/// Generated codex
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratedCodex {
    /// Table name
    pub table_name: String,
    
    /// Entries
    pub entries: Vec<CodexEntry>,
    
    /// Generated code
    pub code: String,
    
    /// Generated wrapper code
    pub wrapper_code: Option<String>,
    
    /// Header code (for C/C++)
    pub header_code: Option<String>,
}

/// Code generator trait
pub trait CodeGenerator {
    /// Generate OmniCodex dispatch table
    fn generate_codex(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<GeneratedCodex>;
    
    /// Generate wrapper functions
    fn generate_wrappers(&self, metadata: &[ExtractedMetadata], options: &CodegenOptions) -> OmniResult<String>;
}

/// Code generator factory
pub struct CodeGeneratorFactory;

impl CodeGeneratorFactory {
    /// Create a code generator for the specified language
    pub fn create_generator(language: &str) -> OmniResult<Box<dyn CodeGenerator>> {
        match language.to_lowercase().as_str() {
            "c" => Ok(Box::new(CCodeGenerator::new())),
            "cpp" | "c++" => Ok(Box::new(CppCodeGenerator::new())),
            "cuda" | "cu" => Ok(Box::new(CudaCodeGenerator::new())),
            "python" | "py" => Ok(Box::new(PythonCodeGenerator::new())),  
            "rust" | "rs" => Ok(Box::new(RustCodeGenerator::new())),
            "typescript" | "ts" => Ok(Box::new(TypeScriptCodeGenerator::new())),        
            _ => Err(OmniError::UnsupportedLanguage(language.to_string())),
        }
    }
}

/// Code generator
pub struct Codegen {
    /// Codegen options
    options: CodegenOptions,
}

impl Codegen {
    /// Create a new code generator
    pub fn new(options: CodegenOptions) -> Self {
        Self { options }
    }
    
    /// Generate OmniCodex dispatch table
    pub fn generate_codex(&self, metadata: &[ExtractedMetadata], output_path: &Path) -> OmniResult<()> {
        log::info!("Generating OmniCodex dispatch table to {}", output_path.display());
        
        // Create code generator for the target language
        let generator = CodeGeneratorFactory::create_generator(&self.options.target_language)?;
        
        // Generate codex
        let codex = generator.generate_codex(metadata, &self.options)?;
        
        // Write codex to file
        std::fs::write(output_path, &codex.code)?;
        
        // If wrappers are enabled and available, write them to a separate file
        if self.options.generate_wrappers && codex.wrapper_code.is_some() {
            let wrapper_path = output_path.with_file_name(format!("{}_wrappers.{}", 
                output_path.file_stem().unwrap().to_string_lossy(),
                output_path.extension().unwrap_or_default().to_string_lossy()));
            
            std::fs::write(&wrapper_path, codex.wrapper_code.unwrap())?;
            log::info!("Wrote wrapper code to {}", wrapper_path.display());
        }
        
        // If header code is available, write it to a separate file
        if let Some(header_code) = codex.header_code {
            let header_path = output_path.with_extension("h");
            std::fs::write(&header_path, header_code)?;
            log::info!("Wrote header code to {}", header_path.display());
        }
        
        log::info!("Generated OmniCodex dispatch table with {} entries", codex.entries.len());
        
        Ok(())
    }
    
    /// Map function type to target type
    pub fn map_function_type_to_target(function_type: FunctionType) -> TargetType {
        match function_type {
            FunctionType::Kernel => TargetType::GPU,
            FunctionType::Device => TargetType::GPU,
            FunctionType::Host => TargetType::CPU,
        }
    }
    
    /// Map extracted function to codex entry
    pub fn map_function_to_codex_entry(function: &ExtractedFunction, _binary_path: &str) -> OmniResult<CodexEntry> {
        // Determine target type
        let target_type = Self::map_function_type_to_target(function.function_type);
        
        // Generate function pointer name
        let function_pointer = match target_type {
            TargetType::GPU => format!("launch_{}", function.name),
            _ => function.name.clone(),
        };
        
        // Extract argument layout
        let arg_layout = if let Some(signature) = &function.signature {
            signature.parameter_types.iter()
                .map(|param| Self::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer))
                .collect()
        } else {
            Vec::new()
        };
        
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            Self::map_type_to_arg_type(&signature.return_type.name, signature.return_type.is_pointer)
        } else {
            ArgType::Void
        };
        
        // Extract grid and block size for CUDA kernels
        let (grid_size, block_size, shared_memory) = if let Some(launch_params) = &function.launch_params {
            (
                Some(launch_params.grid_dim),
                Some(launch_params.block_dim),
                Some(launch_params.shared_mem_bytes),
            )
        } else {
            (None, None, None)
        };
        
        Ok(CodexEntry {
            name: function.name.clone(),
            target_type,
            function_pointer,
            metadata: FunctionMetadata {
                arg_layout,
                return_type,
                grid_size,
                block_size,
                shared_memory,
                is_kernel: function.function_type == FunctionType::Kernel,
            },
        })
    }

    /// Accelerated template processing for code generation
    pub fn accelerated_template_processing(&self, template_data: &[u8]) -> OmniResult<Vec<u8>> {
        // Convert template data to f32 for acceleration
        let mut template_floats: Vec<f32> = template_data.iter().map(|&b| b as f32).collect();

        // Use acceleration for large templates
        if template_floats.len() > 2000 {
            match ahaw::codegen::accelerate_template_processing(&mut template_floats) {
                Ok(result) => {
                    println!("🚀 Accelerated template processing: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Template acceleration failed, falling back to CPU: {}", e);
                }
            }
        }

        // Convert back to bytes (simplified processing)
        let processed_data: Vec<u8> = template_floats.iter().map(|&f| f as u8).collect();
        Ok(processed_data)
    }

    /// Accelerated code synthesis with dynamic backend selection
    pub fn accelerated_code_synthesis(&self, code_vectors: &[f32], target: TargetType) -> OmniResult<String> {
        let mut data = code_vectors.to_vec();

        // Choose acceleration hint based on target type
        let hint = match target {
            TargetType::GPU => AccelerationHint::PreferGPU,
            TargetType::CPUSIMD => AccelerationHint::PreferCPU,
            _ => AccelerationHint::Auto,
        };

        // Create task characteristics based on workload
        let characteristics = TaskCharacteristics {
            data_size: data.len(),
            compute_intensity: match target {
                TargetType::GPU => 0.9,
                TargetType::CPUSIMD => 0.7,
                _ => 0.5,
            },
            parallelizability: 0.85,
            priority: "normal".to_string(),
            ..Default::default()
        };

        // Use acceleration for code synthesis
        if data.len() > 1000 {
            match ahaw::codegen::accelerate_template_processing_with_params(&mut data, ahaw::VectorOperation::FractalIteration, &hint, characteristics) {
                Ok(result) => {
                    println!("🚀 Accelerated code synthesis: {} ms, backend: {}",
                            result.execution_time_ms, result.backend_path);
                },
                Err(e) => {
                    println!("⚠️ Code synthesis acceleration failed: {}", e);
                }
            }
        }

        // Generate code based on processed vectors (simplified)
        Ok(format!("// Generated code for {} target\n// Processed {} elements\n", target, data.len()))
    }

    /// Map type name to ArgType
    fn map_type_to_arg_type(type_name: &str, is_pointer: bool) -> ArgType {
        match (type_name, is_pointer) {
            ("void", false) => ArgType::Void,
            ("void", true) => ArgType::VoidPtr,
            (t, false) if t.contains("i8") || t.contains("s8") || t.contains("char") => ArgType::I8,
            (t, false) if t.contains("i16") || t.contains("s16") || t.contains("short") => ArgType::I16,
            (t, false) if t.contains("i32") || t.contains("s32") || t.contains("int") => ArgType::I32,
            (t, false) if t.contains("i64") || t.contains("s64") || t.contains("long") => ArgType::I64,
            (t, false) if t.contains("u8") || t.contains("uchar") => ArgType::U8,
            (t, false) if t.contains("u16") || t.contains("ushort") => ArgType::U16,
            (t, false) if t.contains("u32") || t.contains("uint") => ArgType::U32,
            (t, false) if t.contains("u64") || t.contains("ulong") => ArgType::U64,
            (t, false) if t.contains("f32") || t.contains("float") => ArgType::F32,
            (t, false) if t.contains("f64") || t.contains("double") => ArgType::F64,
            (t, false) if t.contains("bool") => ArgType::Bool,
            (t, true) if t.contains("i8") || t.contains("s8") || t.contains("char") => ArgType::I8Ptr,
            (t, true) if t.contains("i16") || t.contains("s16") || t.contains("short") => ArgType::I16Ptr,
            (t, true) if t.contains("i32") || t.contains("s32") || t.contains("int") => ArgType::I32Ptr,
            (t, true) if t.contains("i64") || t.contains("s64") || t.contains("long") => ArgType::I64Ptr,
            (t, true) if t.contains("u8") || t.contains("uchar") => ArgType::U8Ptr,
            (t, true) if t.contains("u16") || t.contains("ushort") => ArgType::U16Ptr,
            (t, true) if t.contains("u32") || t.contains("uint") => ArgType::U32Ptr,
            (t, true) if t.contains("u64") || t.contains("ulong") => ArgType::U64Ptr,
            (t, true) if t.contains("f32") || t.contains("float") => ArgType::F32Ptr,
            (t, true) if t.contains("f64") || t.contains("double") => ArgType::F64Ptr,
            (t, true) if t.contains("bool") => ArgType::BoolPtr,
            _ => ArgType::Custom(type_name.to_string()),
        }
    }
}

impl Default for Codegen {
    fn default() -> Self {
        Self::new(CodegenOptions::default())
    }
}



Directory: src\codegen
File: ts_codegen.rs
===================
// src/codegen/ts_codegen.rs
//! TypeScript code generator for the OmniForge compiler.
//!
//! This module provides functionality for generating TypeScript code for the OmniCodex
//! dispatch tables and wrapper functions. It enables seamless integration of heterogeneous
//! computing capabilities into TypeScript/JavaScript applications.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use std::fmt::Write as FmtWrite;

use crate::error::OmniResult;
use crate::metadata_extractor::{ExtractedMetadata, ExtractedFunction};
use super::{CodeGenerator, CodegenOptions, GeneratedCodex, CodexEntry, Codegen, ArgType, TargetType};

/// TypeScript code generator
pub struct TypeScriptCodeGenerator {
    /// Use ESM modules
    use_esm: bool,
    
    /// Use WebAssembly
    use_wasm: bool,
    
    /// Use WebGPU
    use_webgpu: bool,
    
    /// Use TypeScript strict mode
    use_strict: bool,
    
    /// Target TypeScript version
    target_version: String,
}

#[allow(dead_code)]
impl TypeScriptCodeGenerator {
    /// Create a new TypeScript code generator with default settings
    pub fn new() -> Self {
        Self {
            use_esm: true,
            use_wasm: true,
            use_webgpu: false,
            use_strict: true,
            target_version: "4.9".to_string(),
        }
    }
    
    /// Create a new TypeScript code generator with specific configuration
    pub fn with_config(
        use_esm: bool,
        use_wasm: bool,
        use_webgpu: bool,
        use_strict: bool,
        target_version: String,
    ) -> Self {
        Self {
            use_esm,
            use_wasm,
            use_webgpu,
            use_strict,
            target_version,
        }
    }
    
    /// Generate TypeScript type from argument type
    fn generate_ts_type(&self, arg_type: &ArgType) -> String {
        match arg_type {
            ArgType::Void => "void".to_string(),
            ArgType::I8 => "number".to_string(),
            ArgType::I16 => "number".to_string(),
            ArgType::I32 => "number".to_string(),
            ArgType::I64 => "bigint".to_string(),
            ArgType::U8 => "number".to_string(),
            ArgType::U16 => "number".to_string(),
            ArgType::U32 => "number".to_string(),
            ArgType::U64 => "bigint".to_string(),
            ArgType::F32 => "number".to_string(),
            ArgType::F64 => "number".to_string(),
            ArgType::Bool => "boolean".to_string(),
            ArgType::I8Ptr => "Int8Array".to_string(),
            ArgType::I16Ptr => "Int16Array".to_string(),
            ArgType::I32Ptr => "Int32Array".to_string(),
            ArgType::I64Ptr => "BigInt64Array".to_string(),
            ArgType::U8Ptr => "Uint8Array".to_string(),
            ArgType::U16Ptr => "Uint16Array".to_string(),
            ArgType::U32Ptr => "Uint32Array".to_string(),
            ArgType::U64Ptr => "BigUint64Array".to_string(),
            ArgType::F32Ptr => "Float32Array".to_string(),
            ArgType::F64Ptr => "Float64Array".to_string(),
            ArgType::BoolPtr => "Uint8Array".to_string(), // TypeScript doesn't have a BooleanArray
            ArgType::VoidPtr => "ArrayBuffer".to_string(),
            ArgType::Custom(name) => format!("Custom_{}", name.replace(" ", "_").replace("*", "Ptr")),
        }
    }
    
    /// Generate TypeScript function signature
    fn generate_function_signature(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let ts_type = self.generate_ts_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if ts_type == "void" {
                "void".to_string()
            } else {
                ts_type
            }
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let ts_type = self.generate_ts_type(&arg_type);
                        format!("arg{i}: {ts_type}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "".to_string()
        };
        
        Ok(format!("function {}({}): {}", function.name, params, return_type))
    }
    
    /// Generate TypeScript interface for a function
    fn generate_function_interface(&self, function: &ExtractedFunction) -> OmniResult<String> {
        // Extract return type
        let return_type = if let Some(signature) = &function.signature {
            let ts_type = self.generate_ts_type(&Codegen::map_type_to_arg_type(
                &signature.return_type.name,
                signature.return_type.is_pointer,
            ));
            
            if ts_type == "void" {
                "void".to_string()
            } else {
                ts_type
            }
        } else {
            "void".to_string()
        };
        
        // Extract parameter types
        let params = if let Some(signature) = &function.signature {
            if signature.parameter_types.is_empty() {
                "".to_string()
            } else {
                signature
                    .parameter_types
                    .iter()
                    .enumerate()
                    .map(|(i, param)| {
                        let arg_type = Codegen::map_type_to_arg_type(&param.type_info.name, param.type_info.is_pointer);
                        let ts_type = self.generate_ts_type(&arg_type);
                        format!("arg{i}: {ts_type}")
                    })
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        } else {
            "".to_string()
        };
        
        Ok(format!("  {}: ({}) => {}", function.name, params, return_type))
    }
    
    /// Generate TypeScript enum
    fn generate_enum(&self, name: &str, variants: &[(&str, &str)]) -> String {
        let mut result = format!("export enum {name} {{\n");
        
        for (variant, comment) in variants {
            writeln!(result, "  /** {comment} */").unwrap();
            writeln!(result, "  {variant} = '{variant}',").unwrap();
        }
        
        result.push_str("}\n");
        
        result
    }
    
    /// Generate TypeScript OmniCodex module
    fn generate_module(&self, entries: &[CodexEntry]) -> OmniResult<String> {
        let mut result = String::new();
        
        // Generate header with comments
        result.push_str(&format!(
            r#"/**
 * OmniCodex TypeScript Integration
 * 
 * Generated by OmniForge - The OmniCodex Compiler Framework
 * 
 * This module provides TypeScript interfaces and implementations for
 * heterogeneous computing with the OmniCodex framework.
 * 
 * Target TypeScript Version: {target_version}
 * Module Type: {module_type}
 * WebAssembly: {wasm}
 * WebGPU: {webgpu}
 * Strict Mode: {strict}
 */

{strict_directive}

{imports}
"#,
            target_version = self.target_version,
            module_type = if self.use_esm { "ESM" } else { "CommonJS" },
            wasm = if self.use_wasm { "Enabled" } else { "Disabled" },
            webgpu = if self.use_webgpu { "Enabled" } else { "Disabled" },
            strict = if self.use_strict { "Enabled" } else { "Disabled" },
            strict_directive = if self.use_strict { "\"use strict\";\n" } else { "" },
            imports = if self.use_esm {
                "// No external imports required"
            } else {
                "// No external imports required"
            },
        ));
        
        // Generate type definitions
        result.push_str(r#"
/**
 * Target platform for computation
 */
export enum OmniTarget {
  /** Central Processing Unit */
  CPU = 'CPU',
  /** Graphics Processing Unit */
  GPU = 'GPU',
  /** CPU with SIMD instructions */
  CPUSIMD = 'CPUSIMD',
  /** Tensor Processing Unit */
  TPU = 'TPU',
  /** Field-Programmable Gate Array */
  FPGA = 'FPGA',
  /** Other compute device */
  Other = 'Other',
}

/**
 * Argument type enumeration
 */
export enum ArgType {
  /** Void type */
  Void = 'Void',
  /** 8-bit signed integer */
  I8 = 'I8',
  /** 16-bit signed integer */
  I16 = 'I16',
  /** 32-bit signed integer */
  I32 = 'I32',
  /** 64-bit signed integer */
  I64 = 'I64',
  /** 8-bit unsigned integer */
  U8 = 'U8',
  /** 16-bit unsigned integer */
  U16 = 'U16',
  /** 32-bit unsigned integer */
  U32 = 'U32',
  /** 64-bit unsigned integer */
  U64 = 'U64',
  /** 32-bit floating point */
  F32 = 'F32',
  /** 64-bit floating point */
  F64 = 'F64',
  /** Boolean */
  Bool = 'Bool',
  /** Pointer to 8-bit signed integer */
  I8Ptr = 'I8Ptr',
  /** Pointer to 16-bit signed integer */
  I16Ptr = 'I16Ptr',
  /** Pointer to 32-bit signed integer */
  I32Ptr = 'I32Ptr',
  /** Pointer to 64-bit signed integer */
  I64Ptr = 'I64Ptr',
  /** Pointer to 8-bit unsigned integer */
  U8Ptr = 'U8Ptr',
  /** Pointer to 16-bit unsigned integer */
  U16Ptr = 'U16Ptr',
  /** Pointer to 32-bit unsigned integer */
  U32Ptr = 'U32Ptr',
  /** Pointer to 64-bit unsigned integer */
  U64Ptr = 'U64Ptr',
  /** Pointer to 32-bit floating point */
  F32Ptr = 'F32Ptr',
  /** Pointer to 64-bit floating point */
  F64Ptr = 'F64Ptr',
  /** Pointer to boolean */
  BoolPtr = 'BoolPtr',
  /** Pointer to void */
  VoidPtr = 'VoidPtr',
}

/**
 * Function metadata for compute operations
 */
export interface ComputeMetadata {
  /** Grid dimensions for parallel execution */
  gridSize: [number, number, number];
  /** Block dimensions for parallel execution */
  blockSize: [number, number, number];
  /** Shared memory size in bytes */
  sharedMem: number;
  /** Argument types */
  argsLayout: ArgType[];
}

/**
 * Entry in the OmniCodex dispatch table
 */
export interface OmniCodexEntry {
  /** Function name */
  name: string;
  /** Target platform */
  target: OmniTarget;
  /** Function implementation */
  impl: Function;
  /** Function metadata */
  metadata: ComputeMetadata;
}

/**
 * Error codes for OmniCodex operations
 */
export enum OmniErrorCode {
  /** No error */
  None = 'None',
  /** Function not found in OmniCodex */
  FunctionNotFound = 'FunctionNotFound',
  /** Argument count mismatch */
  ArgumentCountMismatch = 'ArgumentCountMismatch',
  /** Argument type mismatch */
  ArgumentTypeMismatch = 'ArgumentTypeMismatch',
  /** Not implemented */
  NotImplemented = 'NotImplemented',
  /** WebAssembly not available */
  WasmNotAvailable = 'WasmNotAvailable',
  /** WebGPU not available */
  WebGpuNotAvailable = 'WebGpuNotAvailable',
  /** Runtime error */
  RuntimeError = 'RuntimeError',
}

/**
 * OmniCodex error
 */
export class OmniError extends Error {
  constructor(
    /** Error code */
    public code: OmniErrorCode,
    /** Error message */
    message: string,
  ) {
    super(message);
    this.name = 'OmniError';
  }
}

/**
 * Interface for all functions in the OmniCodex
 */
export interface OmniFunctions {
"#);
        
        // Generate function interfaces
        let mut function_interfaces = Vec::new();
        let mut function_map = HashMap::new();
        
        for entry in entries {
            if function_map.contains_key(&entry.name) {
                continue;
            }
            
            let interface = format!("  /** {} function */\n  {}: Function;", entry.target_type, entry.name);
            function_interfaces.push(interface);
            function_map.insert(entry.name.clone(), true);
        }
        
        result.push_str(&function_interfaces.join("\n"));
        result.push_str("\n}\n\n");
        
        // Generate OmniCodex dispatch table
        result.push_str("/**\n");
        result.push_str(" * OmniCodex dispatch table\n");
        result.push_str(" */\n");
        result.push_str("export const OMNI_CODEX: OmniCodexEntry[] = [\n");
        
        for entry in entries {
            // Generate grid and block size
            let (grid_size, block_size) = if let (Some(grid), Some(block)) = (entry.metadata.grid_size, entry.metadata.block_size) {
                (
                    format!("[{}, {}, {}]", grid[0], grid[1], grid[2]),
                    format!("[{}, {}, {}]", block[0], block[1], block[2]),
                )
            } else {
                ("[1, 1, 1]".to_string(), "[256, 1, 1]".to_string())
            };
            
            // Generate shared memory size
            let shared_mem = entry.metadata.shared_memory.unwrap_or(0);
            
            // Generate args layout
            let args_layout = entry
                .metadata
                .arg_layout
                .iter()
                .map(|arg| format!("ArgType.{arg}"))
                .collect::<Vec<_>>()
                .join(", ");
            
            // Generate target type
            let target_type = match entry.target_type {
                TargetType::CPU => "OmniTarget.CPU",
                TargetType::GPU => "OmniTarget.GPU",
                TargetType::CPUSIMD => "OmniTarget.CPUSIMD",
                TargetType::TPU => "OmniTarget.TPU",
                TargetType::FPGA => "OmniTarget.FPGA",
                TargetType::Other => "OmniTarget.Other",
            };
            
            // Generate entry
            result.push_str(&format!(
                r#"  {{
    // {name} - {target_desc}
    name: '{name}',
    target: {target},
    impl: {impl_name},
    metadata: {{
      gridSize: {grid_size},
      blockSize: {block_size},
      sharedMem: {shared_mem},
      argsLayout: [{args_layout}],
    }},
  }},
"#,
                name = entry.name,
                target_desc = entry.target_type,
                target = target_type,
                impl_name = format!("{}_{}", entry.target_type.to_string().to_lowercase(), entry.name),
                grid_size = grid_size,
                block_size = block_size,
                shared_mem = shared_mem,
                args_layout = args_layout,
            ));
        }
        
        result.push_str("];\n\n");
        
        // Generate platform-specific implementations
        result.push_str("// CPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::CPU) {
            result.push_str(&format!(
                r#"/** CPU implementation of {name} */
function cpu_{name}(...args: any[]): any {{
  throw new OmniError(OmniErrorCode.NotImplemented, 'CPU implementation not provided');
}}

"#,
                name = entry.name,
            ));
        }
        
        result.push_str("// GPU implementations\n");
        
        for entry in entries.iter().filter(|e| e.target_type == TargetType::GPU) {
            result.push_str(&format!(
                r#"/** GPU implementation of {name} */
function gpu_{name}(...args: any[]): any {{
  throw new OmniError(OmniErrorCode.NotImplemented, 'GPU implementation not provided');
}}

"#,
                name = entry.name,
            ));
        }
        
        // Generate utility functions
        result.push_str(r#"/**
 * Find a function in the OmniCodex dispatch table
 * 
 * @param name Function name
 * @returns OmniCodexEntry or undefined if not found
 */
export function findFunction(name: string): OmniCodexEntry | undefined {
  return OMNI_CODEX.find(entry => entry.name === name);
}

/**
 * Execute a function by name
 * 
 * @param name Function name
 * @param args Function arguments
 * @returns Function result
 * @throws OmniError if an error occurs
 */
export function execute<T>(name: string, ...args: any[]): T {
  // Find the function in the dispatch table
  const entry = findFunction(name);
  if (!entry) {
    throw new OmniError(
      OmniErrorCode.FunctionNotFound,
      `Function not found: ${name}`
    );
  }
  
  // Check argument count
  if (args.length !== entry.metadata.argsLayout.length) {
    throw new OmniError(
      OmniErrorCode.ArgumentCountMismatch,
      `Argument count mismatch: expected ${entry.metadata.argsLayout.length}, got ${args.length}`
    );
  }
  
  // Execute function
  try {
    return entry.impl(...args) as T;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `Runtime error: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Generate a type-safe API for OmniCodex functions
 * 
 * @returns OmniFunctions object with type-safe function wrappers
 */
export function createApi(): OmniFunctions {
  const api: Record<string, Function> = {};
  
  for (const entry of OMNI_CODEX) {
    api[entry.name] = (...args: any[]) => execute(entry.name, ...args);
  }
  
  return api as unknown as OmniFunctions;
}

/**
 * Check if WebAssembly is available
 * 
 * @returns True if WebAssembly is available
 */
export function isWasmAvailable(): boolean {
  return typeof WebAssembly !== 'undefined';
}

/**
 * Check if WebGPU is available
 * 
 * @returns True if WebGPU is available
 */
export function isWebGpuAvailable(): boolean {
  return typeof navigator !== 'undefined' && 'gpu' in navigator;
}

/**
 * OmniCodex runtime
 */
export const runtime = {
  /** OmniCodex dispatch table */
  codex: OMNI_CODEX,
  /** Type-safe API for OmniCodex functions */
  api: createApi(),
  /** Find a function in the OmniCodex dispatch table */
  findFunction,
  /** Execute a function by name */
  execute,
  /** Check if WebAssembly is available */
  isWasmAvailable,
  /** Check if WebGPU is available */
  isWebGpuAvailable,
};

/**
 * Default export
 */
export default runtime;
"#);
        
        Ok(result)
    }
    
    /// Generate WebAssembly integration
    fn generate_wasm_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate WebAssembly integration if enabled
        if !self.use_wasm {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"/**
 * OmniCodex WebAssembly Integration
 * 
 * This module provides WebAssembly integration for the OmniCodex framework.
 */

import { OmniError, OmniErrorCode } from './omnicodex';

/**
 * WebAssembly instance
 */
let wasmInstance: WebAssembly.Instance | null = null;

/**
 * WebAssembly memory
 */
let wasmMemory: WebAssembly.Memory | null = null;

/**
 * WebAssembly exports
 */
let wasmExports: Record<string, any> = {};

/**
 * Initialize WebAssembly
 * 
 * @param wasmUrl URL to WebAssembly module
 * @returns Promise that resolves when WebAssembly is initialized
 * @throws OmniError if WebAssembly is not available or initialization fails
 */
export async function initWasm(wasmUrl: string): Promise<void> {
  if (typeof WebAssembly === 'undefined') {
    throw new OmniError(
      OmniErrorCode.WasmNotAvailable,
      'WebAssembly is not available in this environment'
    );
  }
  
  try {
    // Create import object with memory
    const importObject = {
      env: {
        memory: new WebAssembly.Memory({ initial: 256, maximum: 1024 }),
      },
    };
    
    // Fetch and instantiate WebAssembly module
    const response = await fetch(wasmUrl);
    const wasmBytes = await response.arrayBuffer();
    const wasmModule = await WebAssembly.compile(wasmBytes);
    const instance = await WebAssembly.instantiate(wasmModule, importObject);
    
    // Store WebAssembly instance, memory, and exports
    wasmInstance = instance;
    wasmMemory = importObject.env.memory;
    wasmExports = instance.exports as Record<string, any>;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebAssembly initialization failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Get WebAssembly exports
 * 
 * @returns WebAssembly exports
 * @throws OmniError if WebAssembly is not initialized
 */
export function getWasmExports(): Record<string, any> {
  if (!wasmInstance || !wasmExports) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebAssembly not initialized. Call initWasm() first.'
    );
  }
  
  return wasmExports;
}

/**
 * Get WebAssembly memory
 * 
 * @returns WebAssembly memory
 * @throws OmniError if WebAssembly is not initialized
 */
export function getWasmMemory(): WebAssembly.Memory {
  if (!wasmMemory) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebAssembly not initialized. Call initWasm() first.'
    );
  }
  
  return wasmMemory;
}

/**
 * Create a TypedArray view of WebAssembly memory
 * 
 * @param byteOffset Byte offset in WebAssembly memory
 * @param byteLength Byte length of the view
 * @param type Type of the view
 * @returns TypedArray view of WebAssembly memory
 * @throws OmniError if WebAssembly is not initialized
 */
export function createWasmView<T extends ArrayBufferView>(
  byteOffset: number,
  byteLength: number,
  type: new (buffer: ArrayBuffer, byteOffset: number, length: number) => T
): T {
  const memory = getWasmMemory();
  const buffer = memory.buffer;
  
  return new type(buffer, byteOffset, byteLength / (type.prototype as any).BYTES_PER_ELEMENT);
}

/**
 * Call a WebAssembly function
 * 
 * @param name Function name
 * @param args Function arguments
 * @returns Function result
 * @throws OmniError if WebAssembly is not initialized or function not found
 */
export function callWasm<T>(name: string, ...args: any[]): T {
  const exports = getWasmExports();
  
  if (!(name in exports)) {
    throw new OmniError(
      OmniErrorCode.FunctionNotFound,
      `WebAssembly function not found: ${name}`
    );
  }
  
  try {
    return exports[name](...args) as T;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebAssembly function call failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * WebAssembly utilities
 */
export const wasmUtils = {
  /** Initialize WebAssembly */
  initWasm,
  /** Get WebAssembly exports */
  getWasmExports,
  /** Get WebAssembly memory */
  getWasmMemory,
  /** Create a TypedArray view of WebAssembly memory */
  createWasmView,
  /** Call a WebAssembly function */
  callWasm,
};

export default wasmUtils;
"#);
        
        Ok(result)
    }
    
    /// Generate WebGPU integration
    fn generate_webgpu_integration(&self, _entries: &[CodexEntry]) -> OmniResult<String> {
        // Only generate WebGPU integration if enabled
        if !self.use_webgpu {
            return Ok(String::new());
        }
        
        let mut result = String::new();
        
        result.push_str(r#"/**
 * OmniCodex WebGPU Integration
 * 
 * This module provides WebGPU integration for the OmniCodex framework.
 */

import { OmniError, OmniErrorCode } from './omnicodex';

/**
 * WebGPU device
 */
let gpuDevice: GPUDevice | null = null;

/**
 * Check if WebGPU is available
 * 
 * @returns True if WebGPU is available
 */
export function isWebGpuAvailable(): boolean {
  return typeof navigator !== 'undefined' && 'gpu' in navigator;
}

/**
 * Initialize WebGPU
 * 
 * @returns Promise that resolves when WebGPU is initialized
 * @throws OmniError if WebGPU is not available or initialization fails
 */
export async function initWebGpu(): Promise<void> {
  if (!isWebGpuAvailable()) {
    throw new OmniError(
      OmniErrorCode.WebGpuNotAvailable,
      'WebGPU is not available in this environment'
    );
  }
  
  try {
    // Get GPU adapter
    const adapter = await (navigator as any).gpu.requestAdapter();
    if (!adapter) {
      throw new OmniError(
        OmniErrorCode.WebGpuNotAvailable,
        'No WebGPU adapter found'
      );
    }
    
    // Get GPU device
    const device = await adapter.requestDevice();
    if (!device) {
      throw new OmniError(
        OmniErrorCode.WebGpuNotAvailable,
        'Failed to get WebGPU device'
      );
    }
    
    // Store GPU device
    gpuDevice = device;
  } catch (error) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      `WebGPU initialization failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Get WebGPU device
 * 
 * @returns WebGPU device
 * @throws OmniError if WebGPU is not initialized
 */
export function getGpuDevice(): GPUDevice {
  if (!gpuDevice) {
    throw new OmniError(
      OmniErrorCode.RuntimeError,
      'WebGPU not initialized. Call initWebGpu() first.'
    );
  }
  
  return gpuDevice;
}

/**
 * Create a compute pipeline
 * 
 * @param shaderCode WebGPU shader code
 * @param entryPoint Entry point function name
 * @returns Compute pipeline
 * @throws OmniError if WebGPU is not initialized
 */
export function createComputePipeline(
  shaderCode: string,
  entryPoint: string = 'main'
): GPUComputePipeline {
  const device = getGpuDevice();
  
  // Create shader module
  const shaderModule = device.createShaderModule({
    code: shaderCode,
  });
  
  // Create compute pipeline
  const pipeline = device.createComputePipeline({
    layout: 'auto',
    compute: {
      module: shaderModule,
      entryPoint,
    },
  });
  
  return pipeline;
}

/**
 * Create a GPU buffer
 * 
 * @param size Buffer size in bytes
 * @param usage Buffer usage flags
 * @param mappedAtCreation Whether the buffer should be mapped at creation
 * @returns GPU buffer
 * @throws OmniError if WebGPU is not initialized
 */
export function createBuffer(
  size: number,
  usage: GPUBufferUsageFlags,
  mappedAtCreation: boolean = false
): GPUBuffer {
  const device = getGpuDevice();
  
  return device.createBuffer({
    size,
    usage,
    mappedAtCreation,
  });
}

/**
 * Create a buffer from TypedArray
 * 
 * @param data TypedArray data
 * @param usage Buffer usage flags
 * @returns GPU buffer
 * @throws OmniError if WebGPU is not initialized
 */
export function createBufferFromTypedArray(
  data: ArrayBufferView,
  usage: GPUBufferUsageFlags
): GPUBuffer {
  const device = getGpuDevice();
  
  const buffer = device.createBuffer({
    size: data.byteLength,
    usage,
    mappedAtCreation: true,
  });
  
  const arrayBuffer = buffer.getMappedRange();
  new Uint8Array(arrayBuffer).set(new Uint8Array(data.buffer, data.byteOffset, data.byteLength));
  buffer.unmap();
  
  return buffer;
}

/**
 * Read buffer data
 * 
 * @param buffer GPU buffer
 * @param type TypedArray constructor
 * @returns Promise that resolves to TypedArray with buffer data
 * @throws OmniError if WebGPU is not initialized
 */
export async function readBuffer<T extends ArrayBufferView>(
  buffer: GPUBuffer,
  type: new (buffer: ArrayBuffer, byteOffset?: number, length?: number) => T
): Promise<T> {
  const device = getGpuDevice();
  
  // Create staging buffer
  const stagingBuffer = device.createBuffer({
    size: buffer.size,
    usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.MAP_READ,
  });
  
  // Create command encoder
  const encoder = device.createCommandEncoder();
  
  // Copy buffer to staging buffer
  encoder.copyBufferToBuffer(buffer, 0, stagingBuffer, 0, buffer.size);
  
  // Submit commands
  const commands = encoder.finish();
  device.queue.submit([commands]);
  
  // Map staging buffer
  await stagingBuffer.mapAsync(GPUMapMode.READ);
  
  // Get mapped range
  const arrayBuffer = stagingBuffer.getMappedRange();
  
  // Create TypedArray view
  const typedArray = new type(arrayBuffer);
  
  // Create a copy of the data
  const result = new type(typedArray.length);
  result.set(typedArray);
  
  // Unmap staging buffer
  stagingBuffer.unmap();
  
  return result;
}

/**
 * WebGPU utilities
 */
export const gpuUtils = {
  /** Check if WebGPU is available */
  isWebGpuAvailable,
  /** Initialize WebGPU */
  initWebGpu,
  /** Get WebGPU device */
  getGpuDevice,
  /** Create a compute pipeline */
  createComputePipeline,
  /** Create a GPU buffer */
  createBuffer,
  /** Create a buffer from TypedArray */
  createBufferFromTypedArray,
  /** Read buffer data */
  readBuffer,
};

export default gpuUtils;
"#);
        
        Ok(result)
    }
}

impl CodeGenerator for TypeScriptCodeGenerator {
    fn generate_codex(&self, metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<GeneratedCodex> {
        log::debug!("Generating TypeScript OmniCodex");
        
        // Collect all functions for the codex entries
        let mut entries = Vec::new();
        
        for meta in metadata {
            for function in &meta.functions {
                if let Ok(entry) = Codegen::map_function_to_codex_entry(function, &meta.binary_metadata.path) {
                    entries.push(entry);
                } else {
                    log::warn!("Failed to map function {} to codex entry", function.name);
                }
            }
        }
        
        // Generate main module
        let main_module = self.generate_module(&entries)?;
        
        // Generate WebAssembly integration if enabled
        let wasm_module = self.generate_wasm_integration(&entries)?;
        
        // Generate WebGPU integration if enabled
        let webgpu_module = self.generate_webgpu_integration(&entries)?;
        
        // Combine modules
        let code = main_module;
        
        // Generate wrapper modules
        let mut wrapper_code = String::new();
        
        if !wasm_module.is_empty() {
            wrapper_code.push_str("// WebAssembly Integration\n\n");
            wrapper_code.push_str(&wasm_module);
            wrapper_code.push_str("\n\n");
        }
        
        if !webgpu_module.is_empty() {
            wrapper_code.push_str("// WebGPU Integration\n\n");
            wrapper_code.push_str(&webgpu_module);
        }
        
        let wrapper_code_option = if wrapper_code.is_empty() {
            None
        } else {
            Some(wrapper_code)
        };

        Ok(GeneratedCodex {
            table_name: "OMNI_CODEX".to_string(),
            entries,
            code,
            wrapper_code: wrapper_code_option,
            header_code: None,
        })
    }
    
    fn generate_wrappers(&self, _metadata: &[ExtractedMetadata], _options: &CodegenOptions) -> OmniResult<String> {
        log::debug!("Generating TypeScript wrappers");
        
        // This is covered by the WebAssembly and WebGPU integration modules
        Ok(String::new())
    }
}

impl Default for TypeScriptCodeGenerator {
    fn default() -> Self {
        Self::new()
    }
}


