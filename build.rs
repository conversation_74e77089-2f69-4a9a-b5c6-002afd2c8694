// build.rs
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord <PERSON> ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::env;
use std::path::PathBuf;
use std::process::Command;

fn main() {
    let out_dir = env::var("OUT_DIR").unwrap();
    let cargo_manifest_dir = env::var("CARGO_MANIFEST_DIR").unwrap();
    
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=haal/");
    println!("cargo:rerun-if-changed=ui/");
    println!("cargo:rerun-if-changed=src/gui/");

    // Build HAAL (Hardware Acceleration Abstraction Layer)
    build_haal_library(&cargo_manifest_dir, &out_dir);
    
    // Set up Slint compilation
    setup_slint_compilation();
}

fn build_haal_library(manifest_dir: &str, out_dir: &str) {
    println!("cargo:warning=Building HAAL (Hardware Acceleration Abstraction Layer)");
    
    let haal_dir = PathBuf::from(manifest_dir).join("haal");
    
    // Check if HAAL directory exists
    if !haal_dir.exists() {
        println!("cargo:warning=HAAL directory not found, skipping hardware acceleration");
        return;
    }
    
    // Detect CUDA availability using a unified detection function
    if detect_cuda() {
        println!("cargo:warning=CUDA detected - enabling GPU acceleration");
        println!("🚀 Building HAAL with your optimized kernels");
        build_with_cuda_enhanced(out_dir, manifest_dir);
    } else {
        println!("cargo:warning=CUDA not found - using CPU-only acceleration");
        println!("cargo:warning=CUDA not found, building AVX2-only version");
        build_avx2_only_enhanced(out_dir, &haal_dir);
    }
    
    println!("✅ HAAL build completed successfully");
}

/// Unified CUDA detection. Checks environment variables, common installation
/// paths, and finally the `nvcc` command as a fallback.
fn detect_cuda() -> bool {
    // 1. Check for CUDA_PATH environment variable first.
    if let Ok(cuda_path) = env::var("CUDA_PATH") {
        let nvcc_path = PathBuf::from(&cuda_path).join("bin").join("nvcc.exe");
        if nvcc_path.exists() {
            println!("cargo:rustc-env=CUDA_PATH={}", cuda_path);
            return true;
        }
    }

    // 2. Check common installation paths for various CUDA versions.
    let common_paths = [
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.6",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.5",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.4",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.3",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.2",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.1",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.0",
        "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8",
        "/usr/local/cuda",
    ];

    for path in &common_paths {
        let nvcc_path = PathBuf::from(path).join("bin").join("nvcc.exe");
        if nvcc_path.exists() {
            println!("cargo:rustc-env=CUDA_PATH={}", path);
            env::set_var("CUDA_PATH", path); // Set for subsequent build steps
            return true;
        }
    }

    // 3. Fallback to checking if `nvcc` is in the system's PATH.
    Command::new("nvcc").arg("--version").output().is_ok()
}

#[allow(dead_code)]
fn build_haal_cpp_library_enhanced(haal_dir: &PathBuf, out_dir: &str, cuda_available: bool) {
    if cuda_available {
        build_with_cuda_enhanced(out_dir, haal_dir.to_str().unwrap());
    } else {
        build_avx2_only_enhanced(out_dir, haal_dir);
    }
}

fn setup_slint_compilation() {
    println!("cargo:warning=Compiling Slint UI files");

    // Set the environment variable that Slint needs
    std::env::set_var("SLINT_INCLUDE_GENERATED", "1");

    // Compile the Slint UI file
    slint_build::compile("ui/omniforge.slint").unwrap();

    println!("cargo:warning=Slint UI compilation completed successfully");
}

// Enhanced HAAL build functions from the original HAAL build.rs

fn build_with_cuda_enhanced(out_dir: &str, manifest_dir: &str) {
    let cuda_path = env::var("CUDA_PATH").expect("CUDA_PATH not set");
    
    println!("🚀 Building with CUDA support");
    println!("   CUDA Path: {}", cuda_path);
    
    // Try to compile CUDA code
    match compile_cuda_enhanced(&cuda_path, out_dir, manifest_dir) {
        Ok(_) => {
            println!("✅ CUDA compilation successful");
            // Compile C++ with CUDA enabled
            build_cpp_with_cuda_enhanced(out_dir, &cuda_path, manifest_dir);
        }
        Err(e) => {
            println!("⚠️ CUDA compilation failed: {}", e);
            println!("🔄 Falling back to AVX2-only build");
            let haal_dir = PathBuf::from(manifest_dir).join("haal");
            build_avx2_only_enhanced(out_dir, &haal_dir);
        }
    }
}

fn compile_cuda_enhanced(cuda_path: &str, out_dir: &str, manifest_dir: &str) -> Result<PathBuf, String> {
    let nvcc_path = format!("{}/bin/nvcc.exe", cuda_path);
    let haal_dir = PathBuf::from(manifest_dir).join("haal");
    let cuda_kernels = haal_dir.join("haal-cuda.cu");
    let cuda_launchers = haal_dir.join("haal-cuda-launchers.cu");
    let cuda_obj = format!("{}/haal_cuda.lib", out_dir);
    
    println!("🔧 Compiling CUDA files with nvcc");
    
    let mut nvcc_args = vec![
        "-lib",
        "--std=c++17",
        "-O3",
        "-gencode", "arch=compute_75,code=sm_75",
        "-gencode", "arch=compute_86,code=sm_86", 
        "-gencode", "arch=compute_89,code=sm_89",
        "-I", "include",
        "-o", &cuda_obj,
    ];
    
    // Add source files if they exist
    if cuda_kernels.exists() {
        nvcc_args.push(cuda_kernels.to_str().unwrap());
    }
    if cuda_launchers.exists() {
        nvcc_args.push(cuda_launchers.to_str().unwrap());
    }
    
    let output = Command::new(&nvcc_path)
        .args(&nvcc_args)
        .output()
        .map_err(|e| format!("Failed to execute nvcc: {}", e))?;
    
    if !output.status.success() {
        return Err(format!(
            "NVCC failed:\nstdout: {}\nstderr: {}",
            String::from_utf8_lossy(&output.stdout),
            String::from_utf8_lossy(&output.stderr)
        ));
    }
    
    Ok(PathBuf::from(cuda_obj))
}

fn build_cpp_with_cuda_enhanced(out_dir: &str, cuda_path: &str, manifest_dir: &str) {
    let haal_dir = PathBuf::from(manifest_dir).join("haal");
    let mut builder = cc::Build::new();
    
    builder
        .cpp(true)
        .std("c++17")
        .include(haal_dir.join("include"))
        .include(format!("{}/include", cuda_path))
        .define("CUDA_AVAILABLE", "1")
        .define("AVX2_ENABLED", "1")
        .flag("/arch:AVX2")
        .flag("/O2")
        .flag("/openmp")
        .flag("/fp:fast")
        .flag("/EHsc")  // Enable C++ exception handling
        .warnings(false);
    
    // Add C++ source files
    let cpp_sources = [
        "haal-orc.cpp",
        "haal-avx2.cpp",
        "haal-c-api.cpp",
    ];
    
    for source in &cpp_sources {
        let source_path = haal_dir.join(source);
        if source_path.exists() {
            builder.file(&source_path);
        }
    }
    
    builder.compile("haal_cpp");
    
    // Link CUDA libraries
    println!("cargo:rustc-link-search=native={}/lib/x64", cuda_path);
    println!("cargo:rustc-link-lib=cudart");
    println!("cargo:rustc-link-lib=cublas");
    
    // Link the CUDA object
    println!("cargo:rustc-link-search=native={}", out_dir);
    println!("cargo:rustc-link-lib=static=haal_cuda");
}

fn build_avx2_only_enhanced(_out_dir: &str, haal_dir: &PathBuf) {
    println!("🔧 Building AVX2-only version with your optimized kernels");
    
    let mut builder = cc::Build::new();
    builder
        .cpp(true)
        .std("c++17")
        .include(haal_dir.join("include"))
        .define("AVX2_ENABLED", "1")
        .flag("/arch:AVX2")
        .flag("/O2")
        .flag("/openmp")
        .flag("/fp:fast")
        .flag("/EHsc")  // Enable C++ exception handling
        .warnings(false);
    
    // Add source files
    let sources = [
        "haal-orc.cpp",
        "haal-avx2.cpp",
        "haal-c-api.cpp",
    ];
    
    for source in &sources {
        let source_path = haal_dir.join(source);
        if source_path.exists() {
            builder.file(&source_path);
        }
    }
    
    // Add CUDA files if they exist (for compilation without CUDA runtime)
    let cuda_file = haal_dir.join("haal-cuda.cu");
    if cuda_file.exists() {
        builder.file(&cuda_file);
    }
    
    builder.compile("haal_cpp");
    
    println!("✅ HAAL AVX2-only build completed");
}