{"rustc": 1842507548689473721, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15221872889701672926, "path": 13378455292704437807, "deps": [[1457576002496728321, "clap_derive", false, 18114143084769572761], [7361794428713524931, "clap_builder", false, 12595531812265285355]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-a497d1ce1fcdd66c\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}