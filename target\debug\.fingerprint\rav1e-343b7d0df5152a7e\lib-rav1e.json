{"rustc": 1842507548689473721, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2225463790103693989, "path": 13256437254231881463, "deps": [[2687729594444538932, "debug_unreachable", false, 2510415128379642964], [2828590642173593838, "cfg_if", false, 17409436240543687002], [3722963349756955755, "once_cell", false, 12403945523159540472], [4684437522915235464, "libc", false, 2778355766363917801], [5157631553186200874, "num_traits", false, 4845638491947260247], [5237962722597217121, "simd_helpers", false, 8056623353462384449], [5626665093607998638, "build_script_build", false, 14581084941773946484], [5986029879202738730, "log", false, 8183517435296832891], [6697151524989202978, "profiling", false, 5918547256009592745], [7074416887430417773, "av1_grain", false, 2717984243286492231], [8008191657135824715, "thiserror", false, 7227976159636571614], [11263754829263059703, "num_derive", false, 11085563525993056425], [12672448913558545127, "noop_proc_macro", false, 634457649611583305], [13847662864258534762, "arrayvec", false, 11976762736244033497], [14931062873021150766, "itertools", false, 10800505053476326500], [15325537792103828505, "v_frame", false, 17631169028683797465], [16507960196461048755, "rayon", false, 10756253276855355201], [17605717126308396068, "paste", false, 10624360197493180133], [17706129463675219700, "arg_enum_proc_macro", false, 13907965952694463178], [17933778289016427379, "bitstream_io", false, 15672647929993398798]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-343b7d0df5152a7e\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}