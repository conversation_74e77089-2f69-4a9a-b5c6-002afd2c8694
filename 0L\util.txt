
[OmniCodex.code-workspace]binary
================================

Directory: src\util
File: file_utils.rs
===================
// src/util/file_utils.rs
//! File utility functions for the OmniForge compiler.
//!
//! This module provides common file operations used throughout the OmniCodex framework,
//! with robust error handling and path manipulation capabilities.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::{self, File, OpenOptions};
use std::io::{self, Read, Write, BufReader, BufWriter};


/// Ensure that a directory exists, creating it if necessary
///
/// # Arguments
///
/// * `path` - The directory path to ensure exists
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::ensure_directory_exists;
/// use std::path::Path;
///
/// let dir_path = Path::new("output/data");
/// ensure_directory_exists(dir_path).expect("Failed to create directory");
/// ```
pub fn ensure_directory_exists<P: AsRef<Path>>(path: P) -> io::Result<()> {
    let path = path.as_ref();
    
    if !path.exists() {
        fs::create_dir_all(path)?;
    } else if !path.is_dir() {
        return Err(io::Error::new(
            io::ErrorKind::AlreadyExists,
            format!("Path exists but is not a directory: {}", path.display()),
        ));
    }
    
    Ok(())
}

/// Ensure that the parent directory of a file exists, creating it if necessary
///
/// # Arguments
///
/// * `path` - The file path whose parent directory should exist
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::ensure_parent_directory_exists;
/// use std::path::Path;
///
/// let file_path = Path::new("output/data/results.txt");
/// ensure_parent_directory_exists(file_path).expect("Failed to create parent directory");
/// ```
pub fn ensure_parent_directory_exists<P: AsRef<Path>>(path: P) -> io::Result<()> {
    let path = path.as_ref();
    
    if let Some(parent) = path.parent() {
        ensure_directory_exists(parent)?;
    }
    
    Ok(())
}

/// Read a file into a string
///
/// # Arguments
///
/// * `path` - The path to the file to read
///
/// # Returns
///
/// * `io::Result<String>` - The contents of the file as a string
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::read_file_to_string;
/// use std::path::Path;
///
/// let file_path = Path::new("input/data.txt");
/// let contents = read_file_to_string(file_path).expect("Failed to read file");
/// println!("File contents: {}", contents);
/// ```
pub fn read_file_to_string<P: AsRef<Path>>(path: P) -> io::Result<String> {
    let file = File::open(path)?;
    let mut reader = BufReader::new(file);
    let mut contents = String::new();
    reader.read_to_string(&mut contents)?;
    Ok(contents)
}

/// Write a string to a file
///
/// # Arguments
///
/// * `path` - The path to the file to write
/// * `contents` - The string to write to the file
///
/// # Returns
///
/// * `io::Result<()>` - Result indicating success or failure
///
/// # Examples
///
/// ```no_run
/// use omni_forge::util::file_utils::write_string_to_file;
/// use std::path::Path;
///
/// let file_path = Path::new("output/data.txt");
/// let contents = "Hello, world!";
/// write_string_to_file(file_path, contents).expect("Failed to write file");
/// ```
pub fn write_string_to_file<P: AsRef<Path>, C: AsRef<str>>(path: P, contents: C) -> io::Result<()> {
    ensure_parent_directory_exists(&path)?;
    
    let file = OpenOptions::new()
        .write(true)
        .create(true)
        .truncate(true)
        .open(path)?;
    
    let mut writer = BufWriter::new(file);
    writer.write_all(contents.as_ref().as_bytes())?;
    writer.flush()?;
    
    Ok(())
}











Directory: src\util
File: mod.rs
============
// src/util/mod.rs
//! Utility functions and modules for the OmniForge compiler.
//!
//! This module provides common utilities shared across the OmniCodex framework,
//! including file handling, type conversions, logging utilities, and platform
//! detection capabilities.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::{Path, PathBuf};
use std::fs;
use std::io;
use std::env;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

use crate::error::{OmniError, OmniResult};
use crate::ahaw::{self, VectorOperation, AccelerationHint, TaskCharacteristics};

mod file_utils;
mod type_conversion;
mod platform;
mod logging;
mod hash;

pub use self::file_utils::{ensure_directory_exists, ensure_parent_directory_exists, read_file_to_string, write_string_to_file};
pub use self::type_conversion::{convert_to_c_type, convert_to_rust_type, convert_to_cpp_type, convert_to_python_type, convert_to_ts_type};
pub use self::platform::{detect_platform, Platform, Architecture, OperatingSystem};
pub use self::logging::{log_timing, log_with_timing, benchmark_fn};
pub use self::hash::{compute_file_hash, compute_string_hash, HashAlgorithm};

/// Cache implementation with a generic key and value
#[derive(Debug, Clone)]
pub struct Cache<K, V>
where
    K: std::hash::Hash + Eq + Clone,
    V: Clone,
{
    inner: Arc<Mutex<HashMap<K, CacheEntry<V>>>>,
    max_size: usize,
    ttl: Option<Duration>,
}

/// Cache entry with value and timestamp
#[derive(Debug, Clone)]
struct CacheEntry<V> {
    value: V,
    timestamp: Instant,
}

impl<K, V> Cache<K, V>
where
    K: std::hash::Hash + Eq + Clone,
    V: Clone,
{
    /// Create a new cache with the given maximum size
    pub fn new(max_size: usize) -> Self {
        Self {
            inner: Arc::new(Mutex::new(HashMap::new())),
            max_size,
            ttl: None,
        }
    }
    
    /// Create a new cache with the given maximum size and time-to-live
    pub fn with_ttl(max_size: usize, ttl: Duration) -> Self {
        Self {
            inner: Arc::new(Mutex::new(HashMap::new())),
            max_size,
            ttl: Some(ttl),
        }
    }
    
    /// Get a value from the cache
    pub fn get(&self, key: &K) -> Option<V> {
        let mut cache = self.inner.lock().unwrap();
        
        if let Some(entry) = cache.get(key) {
            // Check if entry has expired
            if let Some(ttl) = self.ttl {
                if entry.timestamp.elapsed() > ttl {
                    // Entry has expired, remove it
                    cache.remove(key);
                    return None;
                }
            }
            
            return Some(entry.value.clone());
        }
        
        None
    }
    
    /// Insert a value into the cache
    pub fn insert(&self, key: K, value: V) {
        let mut cache = self.inner.lock().unwrap();
        
        // Check if cache is full
        if cache.len() >= self.max_size && !cache.contains_key(&key) {
            // Remove oldest entry
            if let Some(ttl) = self.ttl {
                // Remove expired entries first
                cache.retain(|_, entry| entry.timestamp.elapsed() <= ttl);
            }
            
            // If still full, remove oldest entry
            if cache.len() >= self.max_size {
                let oldest_key = cache
                    .iter()
                    .min_by_key(|(_, entry)| entry.timestamp)
                    .map(|(k, _)| k.clone());
                
                if let Some(oldest_key) = oldest_key {
                    cache.remove(&oldest_key);
                }
            }
        }
        
        // Insert new entry
        cache.insert(key, CacheEntry {
            value,
            timestamp: Instant::now(),
        });
    }
    
    /// Remove a value from the cache
    pub fn remove(&self, key: &K) -> Option<V> {
        let mut cache = match self.inner.lock() {
            Ok(guard) => guard,
            Err(_) => return None,
        };
        cache.remove(key).map(|entry| entry.value)
    }
    
    /// Clear the cache
    pub fn clear(&self) {
        if let Ok(mut cache) = self.inner.lock() {
            cache.clear();
        }
    }
    
    /// Get the number of entries in the cache
    pub fn len(&self) -> usize {
        match self.inner.lock() {
            Ok(cache) => cache.len(),
            Err(_) => 0,
        }
    }
    
    /// Check if the cache is empty
    pub fn is_empty(&self) -> bool {
        match self.inner.lock() {
            Ok(cache) => cache.is_empty(),
            Err(_) => true,
        }
    }
    
    /// Remove expired entries from the cache
    pub fn remove_expired(&self) -> usize {
        if let Some(ttl) = self.ttl {
            if let Ok(mut cache) = self.inner.lock() {
                let initial_size = cache.len();
                cache.retain(|_, entry| entry.timestamp.elapsed() <= ttl);
                return initial_size - cache.len();
            }
        }
        0
    }
}

/// Execute a shell command and return the output
pub fn execute_command(command: &str, args: &[&str]) -> io::Result<String> {
    let output = std::process::Command::new(command)
        .args(args)
        .output()?;
    
    if output.status.success() {
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    } else {
        Err(io::Error::other(
            format!(
                "Command '{}' failed with exit code {}: {}",
                command,
                output.status.code().unwrap_or(-1),
                String::from_utf8_lossy(&output.stderr)
            ),
        ))
    }
}

/// Find files matching a pattern
pub fn find_files<P: AsRef<Path>>(dir: P, pattern: &str) -> io::Result<Vec<PathBuf>> {
    let mut files = Vec::new();
    let pattern_regex = regex::Regex::new(pattern)
        .map_err(|e| io::Error::new(io::ErrorKind::InvalidInput, e))?;
    
    for entry in fs::read_dir(dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            if let Some(filename) = path.file_name().and_then(|f| f.to_str()) {
                if pattern_regex.is_match(filename) {
                    files.push(path);
                }
            }
        } else if path.is_dir() {
            // Recursive search
            let mut sub_files = find_files(&path, pattern)?;
            files.append(&mut sub_files);
        }
    }
    
    Ok(files)
}

/// Find a command in the PATH
pub fn find_command(command: &str) -> Option<PathBuf> {
    let path_var = env::var("PATH").unwrap_or_default();
    let paths: Vec<&str> = if cfg!(windows) {
        path_var.split(';').collect()
    } else {
        path_var.split(':').collect()
    };
    
    // Add common extensions for Windows
    let extensions = if cfg!(windows) {
        vec![".exe", ".cmd", ".bat", ""]
    } else {
        vec![""]
    };
    
    for path in paths {
        for ext in &extensions {
            let cmd_path = PathBuf::from(path).join(format!("{command}{ext}"));
            if cmd_path.exists() {
                return Some(cmd_path);
            }
        }
    }
    
    None
}

/// Check if a command exists in the PATH
pub fn command_exists(command: &str) -> bool {
    find_command(command).is_some()
}

/// Get the current executable path
pub fn get_executable_path() -> io::Result<PathBuf> {
    env::current_exe()
}

/// Get a temporary file path
pub fn get_temp_file_path(prefix: &str, extension: &str) -> PathBuf {
    let temp_dir = env::temp_dir();
    let mut file_name = prefix.to_string();
    
    // Add timestamp
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    file_name.push_str(&format!("_{timestamp}"));
    
    // Add random suffix
    use rand::Rng;
    let mut rng = rand::rng();
    let random_suffix: u32 = rng.random();
    file_name.push_str(&format!("_{random_suffix:x}"));
    
    // Add extension
    if !extension.is_empty() {
        if !extension.starts_with('.') {
            file_name.push('.');
        }
        file_name.push_str(extension);
    }
    
    temp_dir.join(file_name)
}

/// Check if a file is newer than another file
pub fn is_file_newer<P: AsRef<Path>, Q: AsRef<Path>>(file: P, reference: Q) -> io::Result<bool> {
    let file_metadata = fs::metadata(file)?;
    let reference_metadata = fs::metadata(reference)?;
    
    let file_modified = file_metadata.modified()?;
    let reference_modified = reference_metadata.modified()?;
    
    Ok(file_modified > reference_modified)
}

/// Split a string into lines, handling different line endings
pub fn split_lines(text: &str) -> Vec<String> {
    text.lines().map(|line| line.to_string()).collect()
}

/// Join lines with the platform-specific line ending
pub fn join_lines(lines: &[String]) -> String {
    let line_ending = if cfg!(windows) { "\r\n" } else { "\n" };
    lines.join(line_ending)
}

/// Parse a version string
pub fn parse_version(version: &str) -> OmniResult<(u32, u32, u32)> {
    let parts: Vec<&str> = version.split('.').collect();
    
    if parts.len() < 2 || parts.len() > 3 {
        return Err(OmniError::General(format!(
            "Invalid version format: {version}"
        )));
    }
    
    let major = parts[0].parse::<u32>()
        .map_err(|_| OmniError::General(format!("Invalid major version: {}", parts[0])))?;
    
    let minor = parts[1].parse::<u32>()
        .map_err(|_| OmniError::General(format!("Invalid minor version: {}", parts[1])))?;
    
    let patch = if parts.len() == 3 {
        parts[2].parse::<u32>()
            .map_err(|_| OmniError::General(format!("Invalid patch version: {}", parts[2])))?
    } else {
        0
    };
    
    Ok((major, minor, patch))
}

/// Compare two version strings
pub fn compare_versions(v1: &str, v2: &str) -> OmniResult<std::cmp::Ordering> {
    let v1 = parse_version(v1)?;
    let v2 = parse_version(v2)?;
    
    Ok(v1.cmp(&v2))
}

/// Format a size in bytes to a human-readable string
pub fn format_size(size: u64) -> String {
    const KB: u64 = 1024;
    const MB: u64 = KB * 1024;
    const GB: u64 = MB * 1024;
    const TB: u64 = GB * 1024;
    
    if size < KB {
        format!("{size} B")
    } else if size < MB {
        format!("{:.2} KB", size as f64 / KB as f64)
    } else if size < GB {
        format!("{:.2} MB", size as f64 / MB as f64)
    } else if size < TB {
        format!("{:.2} GB", size as f64 / GB as f64)
    } else {
        format!("{:.2} TB", size as f64 / TB as f64)
    }
}

/// Format a duration in milliseconds to a human-readable string
pub fn format_duration(duration_ms: u64) -> String {
    const SECOND: u64 = 1000;
    const MINUTE: u64 = SECOND * 60;
    const HOUR: u64 = MINUTE * 60;

    if duration_ms < SECOND {
        format!("{duration_ms} ms")
    } else if duration_ms < MINUTE {
        format!("{:.2} s", duration_ms as f64 / SECOND as f64)
    } else if duration_ms < HOUR {
        format!("{:.2} min", duration_ms as f64 / MINUTE as f64)
    } else {
        format!("{:.2} h", duration_ms as f64 / HOUR as f64)
    }
}

/// Accelerated mathematical operations with dynamic backend selection
pub fn accelerated_math_operation(data: &mut [f32], operation: VectorOperation) -> OmniResult<Vec<f32>> {
    // Use acceleration for mathematical operations on large datasets
    if data.len() > 1000 {
        let hint = AccelerationHint::Auto;
        let characteristics = TaskCharacteristics {
            data_size: data.len(),
            compute_intensity: 0.7,
            parallelizability: 0.85,
            ..Default::default()
        };
        match ahaw::util::accelerate_mathematical_operations(data, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated math operation ({:?}): {} ms, backend: {}",
                        operation, result.execution_time_ms, result.backend_path);
                println!("   Performance: {:.2} GFLOPS, efficiency: {:.1}%",
                        result.performance_metrics.throughput_gflops,
                        result.performance_metrics.vectorization_efficiency * 100.0);
            },
            Err(e) => {
                println!("⚠️ Math operation acceleration failed: {}", e);
            }
        }
    }

    Ok(data.to_vec())
}

/// Accelerated data processing with automatic workload characterization
pub fn accelerated_data_processing(data: &mut [f32], hint: AccelerationHint) -> OmniResult<Vec<f32>> {
    // Characterize the workload automatically
    let characteristics = TaskCharacteristics {
        data_size: data.len(),
        compute_intensity: match data.len() {
            0..=1000 => 0.3,
            1001..=10000 => 0.6,
            _ => 0.9,
        },
        parallelizability: 0.85,
        memory_access_pattern: "sequential".to_string(),
        cache_locality_index: 0.8,
        expected_duration_ms: (data.len() as f64 * 0.001).max(1.0),
        priority: "normal".to_string(),
    };

    // Use acceleration for data processing
    if data.len() > 500 {
        let operation = VectorOperation::Add; // Default operation for data processing
        match ahaw::util::accelerate_data_processing(data, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated data processing: {} ms, backend: {}",
                        result.execution_time_ms, result.backend_path);

                // Log performance characteristics
                if result.performance_metrics.thermal_throttling {
                    println!("⚠️ Thermal throttling detected during processing");
                }
            },
            Err(e) => {
                println!("⚠️ Data processing acceleration failed: {}", e);
            }
        }
    }

    Ok(data.to_vec())
}

/// Accelerated vector operations with performance monitoring
pub fn accelerated_vector_ops(a: &mut [f32], b: &[f32], operation: VectorOperation) -> OmniResult<Vec<f32>> {
    let b_copy = b.to_vec();

    // Create task characteristics based on operation type
    let characteristics = TaskCharacteristics {
        data_size: a.len(),
        compute_intensity: match operation {
            VectorOperation::Add | VectorOperation::Multiply => 0.4,
            VectorOperation::DotProduct | VectorOperation::Norm => 0.6,
            VectorOperation::MatrixMultiply | VectorOperation::Convolution => 0.9,
            VectorOperation::FourierTransform | VectorOperation::QuantumEvolution => 0.95,
            _ => 0.7,
        },
        parallelizability: match operation {
            VectorOperation::Add | VectorOperation::Multiply => 0.95,
            VectorOperation::MatrixMultiply => 0.90,
            VectorOperation::FourierTransform => 0.85,
            _ => 0.80,
        },
        memory_access_pattern: match operation {
            VectorOperation::Add | VectorOperation::Multiply => "sequential".to_string(),
            VectorOperation::MatrixMultiply => "strided".to_string(),
            _ => "random".to_string(),
        },
        priority: "normal".to_string(),
        ..Default::default()
    };

    // Use acceleration for vector operations
    if a.len() > 100 {
        let hint = AccelerationHint::Auto;
        match ahaw::util::accelerate_mathematical_operations(a, operation, &hint, characteristics) {
            Ok(result) => {
                println!("🚀 Accelerated vector operation ({:?}): {} ms, backend: {}",
                        operation, result.execution_time_ms, result.backend_path);
            },
            Err(e) => {
                println!("⚠️ Vector operation acceleration failed: {}", e);
            }
        }
    }

    // Perform the actual operation (simplified)
    let result = match operation {
        VectorOperation::Add => a.iter().zip(b_copy.iter().cycle()).map(|(x, y)| x + y).collect(),
        VectorOperation::Multiply => a.iter().zip(b_copy.iter().cycle()).map(|(x, y)| x * y).collect(),
        _ => a.to_vec(),
    };

    Ok(result)
}



Directory: src\util
File: logging.rs
================
// src/util/logging.rs
//! Logging utilities for the OmniForge compiler.
//!
//! This module provides logging and performance tracking functionality
//! to support debugging and optimization of the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::time::{Duration, Instant};
use std::fmt::Debug;
use std::sync::atomic::{AtomicUsize, Ordering};

/// Log a message with timing information
///
/// # Arguments
///
/// * `level` - Log level (e.g., "INFO", "DEBUG")
/// * `message` - Message to log
/// * `duration` - Duration to include in the log
///
/// # Examples
///
/// ```
/// use std::time::{Duration, Instant};
/// use omni_forge::util::logging::log_timing;
///
/// let start = Instant::now();
/// // Perform some operation
/// let duration = start.elapsed();
/// log_timing("INFO", "Operation completed", duration);
/// ```
pub fn log_timing(level: &str, message: &str, duration: Duration) {
    let duration_ms = duration.as_secs() * 1000 + duration.subsec_millis() as u64;
    
    if level == "ERROR" || level == "WARN" {
        eprintln!("[{level}] {message} (took {duration_ms} ms)");
    } else {
        println!("[{level}] {message} (took {duration_ms} ms)");
    }
}

/// Execute a function and log timing information
///
/// # Arguments
///
/// * `level` - Log level (e.g., "INFO", "DEBUG")
/// * `message` - Message to log
/// * `f` - Function to execute
///
/// # Returns
///
/// * The result of the function
///
/// # Examples
///
/// ```
/// use omni_forge::util::logging::log_with_timing;
///
/// let result = log_with_timing("INFO", "Calculating factorial", || {
///     // Calculate factorial
///     let mut result = 1;
///     for i in 1..10 {
///         result *= i;
///     }
///     result
/// });
/// ```
pub fn log_with_timing<F, R>(level: &str, message: &str, f: F) -> R
where
    F: FnOnce() -> R,
{
    let start = Instant::now();
    let result = f();
    let duration = start.elapsed();
    
    log_timing(level, message, duration);
    
    result
}

/// Benchmark a function
///
/// # Arguments
///
/// * `f` - Function to benchmark
/// * `iterations` - Number of iterations to run
///
/// # Returns
///
/// * `(R, Duration)` - The result of the function and the average duration per iteration
///
/// # Examples
///
/// ```
/// use omni_forge::util::logging::benchmark_fn;
///
/// let (result, avg_duration) = benchmark_fn(|| {
///     // Function to benchmark
///     let mut sum = 0;
///     for i in 0..1000 {
///         sum += i;
///     }
///     sum
/// }, 100); // Run 100 iterations
///
/// println!("Result: {}, Average duration: {:?}", result, avg_duration);
/// ```
pub fn benchmark_fn<F, R>(f: F, iterations: usize) -> (R, Duration)
where
    F: Fn() -> R,
    R: Clone,
{
    // Run once to get the result
    let result = f();
    
    // Run the remaining iterations
    let start = Instant::now();
    for _ in 1..iterations {
        let _ = f();
    }
    let total_duration = start.elapsed();
    
    let avg_duration = if iterations > 1 {
        total_duration / (iterations as u32 - 1)
    } else {
        total_duration
    };
    
    (result, avg_duration)
}

/// Performance counter for tracking function calls and durations
#[derive(Debug)]
pub struct PerformanceCounter {
    /// Counter name (for debugging and reporting)
    #[allow(dead_code)]
    name: String,
    /// Number of calls
    calls: AtomicUsize,
    /// Total duration
    total_duration: std::sync::Mutex<Duration>,
    /// Minimum duration
    min_duration: std::sync::Mutex<Option<Duration>>,
    /// Maximum duration
    max_duration: std::sync::Mutex<Option<Duration>>,
}

#[allow(dead_code)]
impl PerformanceCounter {
    /// Create a new performance counter
    ///
    /// # Arguments
    ///
    /// * `name` - Counter name
    ///
    /// # Returns
    ///
    /// * `PerformanceCounter` - The new counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// ```
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            calls: AtomicUsize::new(0),
            total_duration: std::sync::Mutex::new(Duration::from_secs(0)),
            min_duration: std::sync::Mutex::new(None),
            max_duration: std::sync::Mutex::new(None),
        }
    }
    
    /// Record a function call with the given duration
    ///
    /// # Arguments
    ///
    /// * `duration` - Duration of the function call
    ///
    /// # Examples
    ///
    /// ```
    /// use std::time::Instant;
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let start = Instant::now();
    /// // Perform some operation
    /// let duration = start.elapsed();
    /// counter.record(duration);
    /// ```
    pub fn record(&self, duration: Duration) {
        self.calls.fetch_add(1, Ordering::Relaxed);
        
        // Update total duration
        {
            let mut total_duration = self.total_duration.lock().unwrap();
            *total_duration += duration;
        }
        
        // Update min duration
        {
            let mut min_duration = self.min_duration.lock().unwrap();
            match *min_duration {
                Some(min) if duration < min => *min_duration = Some(duration),
                None => *min_duration = Some(duration),
                _ => {}
            }
        }
        
        // Update max duration
        {
            let mut max_duration = self.max_duration.lock().unwrap();
            match *max_duration {
                Some(max) if duration > max => *max_duration = Some(duration),
                None => *max_duration = Some(duration),
                _ => {}
            }
        }
    }
    
    /// Execute a function and record its duration
    ///
    /// # Arguments
    ///
    /// * `f` - Function to execute
    ///
    /// # Returns
    ///
    /// * The result of the function
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let result = counter.execute(|| {
    ///     // Perform some operation
    ///     42
    /// });
    /// ```
    pub fn execute<F, R>(&self, f: F) -> R
    where
        F: FnOnce() -> R,
    {
        let start = Instant::now();
        let result = f();
        let duration = start.elapsed();
        
        self.record(duration);
        
        result
    }
    
    /// Get the number of calls
    ///
    /// # Returns
    ///
    /// * `usize` - Number of calls
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let calls = counter.get_calls();
    /// ```
    pub fn get_calls(&self) -> usize {
        self.calls.load(Ordering::Relaxed)
    }
    
    /// Get the total duration
    ///
    /// # Returns
    ///
    /// * `Duration` - Total duration
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// let total_duration = counter.get_total_duration();
    /// ```
    pub fn get_total_duration(&self) -> Duration {
        *self.total_duration.lock().unwrap()
    }
    
    /// Get the average duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Average duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(avg_duration) = counter.get_average_duration() {
    ///     println!("Average duration: {:?}", avg_duration);
    /// }
    /// ```
    pub fn get_average_duration(&self) -> Option<Duration> {
        let calls = self.get_calls();
        if calls == 0 {
            None
        } else {
            let total_duration = self.get_total_duration();
            Some(total_duration / calls as u32)
        }
    }
    
    /// Get the minimum duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Minimum duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(min_duration) = counter.get_min_duration() {
    ///     println!("Minimum duration: {:?}", min_duration);
    /// }
    /// ```
    pub fn get_min_duration(&self) -> Option<Duration> {
        *self.min_duration.lock().unwrap()
    }
    
    /// Get the maximum duration
    ///
    /// # Returns
    ///
    /// * `Option<Duration>` - Maximum duration, or None if no calls have been recorded
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// if let Some(max_duration) = counter.get_max_duration() {
    ///     println!("Maximum duration: {:?}", max_duration);
    /// }
    /// ```
    pub fn get_max_duration(&self) -> Option<Duration> {
        *self.max_duration.lock().unwrap()
    }
    
    /// Reset the counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// // Record some calls
    /// counter.reset();
    /// ```
    pub fn reset(&self) {
        self.calls.store(0, Ordering::Relaxed);
        *self.total_duration.lock().unwrap() = Duration::from_secs(0);
        *self.min_duration.lock().unwrap() = None;
        *self.max_duration.lock().unwrap() = None;
    }
    
    /// Get a summary of the counter
    ///
    /// # Returns
    ///
    /// * `String` - Summary string
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceCounter;
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// // Record some calls
    /// let summary = counter.summary();
    /// println!("{}", summary);
    /// ```
    pub fn summary(&self) -> String {
        let calls = self.get_calls();
        if calls == 0 {
            return format!("{}: No calls recorded", self.name);
        }
        
        let total_duration = self.get_total_duration();
        let avg_duration = self.get_average_duration().unwrap();
        let min_duration = self.get_min_duration().unwrap();
        let max_duration = self.get_max_duration().unwrap();
        
        format!(
            "{}: {} calls, total: {:?}, avg: {:?}, min: {:?}, max: {:?}",
            self.name, calls, total_duration, avg_duration, min_duration, max_duration
        )
    }
}

/// Performance tracer for tracing function calls with detailed timing information
#[allow(dead_code)]
pub struct PerformanceTracer {
    /// Tracer name
    name: String,
    /// Start time
    start: Instant,
    /// Current phase
    current_phase: String,
    /// Phase transitions
    phases: Vec<(String, Duration)>,
}

#[allow(dead_code)]
impl PerformanceTracer {
    /// Create a new performance tracer
    ///
    /// # Arguments
    ///
    /// * `name` - Tracer name
    ///
    /// # Returns
    ///
    /// * `PerformanceTracer` - The new tracer
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let tracer = PerformanceTracer::new("compilation");
    /// ```
    pub fn new(name: &str) -> Self {
        let start = Instant::now();
        Self {
            name: name.to_string(),
            start,
            current_phase: "start".to_string(),
            phases: vec![("start".to_string(), Duration::from_secs(0))],
        }
    }
    
    /// Start a new phase
    ///
    /// # Arguments
    ///
    /// * `phase` - Phase name
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// // Perform parsing
    /// tracer.start_phase("analysis");
    /// // Perform analysis
    /// ```
    pub fn start_phase(&mut self, phase: &str) {
        let elapsed = self.start.elapsed();
        self.phases.push((phase.to_string(), elapsed));
        self.current_phase = phase.to_string();
    }
    
    /// Finish the tracer and return a summary
    ///
    /// # Returns
    ///
    /// * `String` - Summary string
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// // Perform parsing
    /// tracer.start_phase("analysis");
    /// // Perform analysis
    /// let summary = tracer.finish();
    /// println!("{}", summary);
    /// ```
    pub fn finish(mut self) -> String {
        let elapsed = self.start.elapsed();
        self.phases.push(("end".to_string(), elapsed));
        
        let mut summary = format!("Performance trace for {}: total {:?}\n", self.name, elapsed);
        let mut prev_duration = Duration::from_secs(0);
        
        for (_i, (phase, duration)) in self.phases.iter().enumerate().skip(1) {
            let phase_duration = *duration - prev_duration;
            prev_duration = *duration;
            
            let percent = if elapsed.as_secs_f64() > 0.0 {
                phase_duration.as_secs_f64() / elapsed.as_secs_f64() * 100.0
            } else {
                0.0
            };
            
            summary.push_str(&format!(
                "  Phase {phase}: {phase_duration:?} ({percent:.2}%)\n"
            ));
        }
        
        summary
    }
    
    /// Get the elapsed time since the start of the tracer
    ///
    /// # Returns
    ///
    /// * `Duration` - Elapsed time
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let tracer = PerformanceTracer::new("compilation");
    /// let elapsed = tracer.elapsed();
    /// println!("Elapsed time: {:?}", elapsed);
    /// ```
    pub fn elapsed(&self) -> Duration {
        self.start.elapsed()
    }
    
    /// Get the current phase
    ///
    /// # Returns
    ///
    /// * `&str` - Current phase name
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::PerformanceTracer;
    ///
    /// let mut tracer = PerformanceTracer::new("compilation");
    /// tracer.start_phase("parsing");
    /// let current_phase = tracer.current_phase();
    /// println!("Current phase: {}", current_phase);
    /// ```
    pub fn current_phase(&self) -> &str {
        &self.current_phase
    }
}

/// Scope-based performance counter
///
/// This struct automatically records the duration of a scope when it is dropped.
pub struct ScopedPerformanceCounter<'a> {
    /// Counter to record the duration
    counter: &'a PerformanceCounter,
    /// Start time
    start: Instant,
}

#[allow(dead_code)]
impl<'a> ScopedPerformanceCounter<'a> {
    /// Create a new scoped performance counter
    ///
    /// # Arguments
    ///
    /// * `counter` - Performance counter to record the duration
    ///
    /// # Returns
    ///
    /// * `ScopedPerformanceCounter` - The new scoped counter
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::logging::{PerformanceCounter, ScopedPerformanceCounter};
    ///
    /// let counter = PerformanceCounter::new("binary_analyzer");
    /// {
    ///     let _scoped_counter = ScopedPerformanceCounter::new(&counter);
    ///     // Perform some operation
    /// } // Duration is automatically recorded when _scoped_counter is dropped
    /// ```
    pub fn new(counter: &'a PerformanceCounter) -> Self {
        Self {
            counter,
            start: Instant::now(),
        }
    }
}

impl<'a> Drop for ScopedPerformanceCounter<'a> {
    fn drop(&mut self) {
        let duration = self.start.elapsed();
        self.counter.record(duration);
    }
}



Directory: src\util
File: platform.rs
=================
// src/util/platform.rs
//! Platform detection utilities for the OmniForge compiler.
//!
//! This module provides functionality for detecting and identifying the
//! current operating system, architecture, and platform-specific features,
//! enabling platform-aware compilation and code generation.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::env;
use std::process::Command;

/// Operating system types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum OperatingSystem {
    /// Windows operating system
    Windows,
    /// Linux operating system
    Linux,
    /// macOS operating system
    MacOS,
    /// BSD operating system
    BSD,
    /// Android operating system
    Android,
    /// iOS operating system
    IOS,
    /// Other/unknown operating system
    Other,
}

impl OperatingSystem {
    /// Get the string representation of the operating system
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Windows => "windows",
            Self::Linux => "linux",
            Self::MacOS => "macos",
            Self::BSD => "bsd",
            Self::Android => "android",
            Self::IOS => "ios",
            Self::Other => "other",
        }
    }
    
    /// Check if the operating system is Windows
    pub fn is_windows(&self) -> bool {
        matches!(self, Self::Windows)
    }
    
    /// Check if the operating system is Linux
    pub fn is_linux(&self) -> bool {
        matches!(self, Self::Linux)
    }
    
    /// Check if the operating system is macOS
    pub fn is_macos(&self) -> bool {
        matches!(self, Self::MacOS)
    }
    
    /// Check if the operating system is Unix-like
    pub fn is_unix_like(&self) -> bool {
        matches!(self, Self::Linux | Self::MacOS | Self::BSD | Self::Android | Self::IOS)
    }
}

/// Processor architecture types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Architecture {
    /// x86 (32-bit) architecture
    X86,
    /// x86_64 (64-bit) architecture
    X86_64,
    /// ARM (32-bit) architecture
    ARM,
    /// ARM64 (64-bit) architecture
    ARM64,
    /// MIPS architecture
    MIPS,
    /// PowerPC architecture
    PowerPC,
    /// RISC-V architecture
    RISCV,
    /// WebAssembly
    WASM,
    /// Other/unknown architecture
    Other,
}

impl Architecture {
    /// Get the string representation of the architecture
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::X86 => "x86",
            Self::X86_64 => "x86_64",
            Self::ARM => "arm",
            Self::ARM64 => "aarch64",
            Self::MIPS => "mips",
            Self::PowerPC => "powerpc",
            Self::RISCV => "riscv",
            Self::WASM => "wasm",
            Self::Other => "other",
        }
    }
    
    /// Check if the architecture is 64-bit
    pub fn is_64bit(&self) -> bool {
        matches!(self, Self::X86_64 | Self::ARM64)
    }
    
    /// Check if the architecture is x86 family
    pub fn is_x86_family(&self) -> bool {
        matches!(self, Self::X86 | Self::X86_64)
    }
    
    /// Check if the architecture is ARM family
    pub fn is_arm_family(&self) -> bool {
        matches!(self, Self::ARM | Self::ARM64)
    }
}

/// Platform information combining operating system and architecture
#[derive(Debug, Clone)]
pub struct Platform {
    /// Operating system
    pub os: OperatingSystem,
    /// Architecture
    pub arch: Architecture,
    /// Operating system version
    pub os_version: Option<String>,
    /// Additional platform features
    pub features: Vec<String>,
}

impl Platform {
    /// Check if the platform supports CUDA
    pub fn supports_cuda(&self) -> bool {
        // CUDA is supported on Windows, Linux, and macOS (up to certain versions)
        // And only on x86_64 and ARM64 architectures
        match (self.os, self.arch) {
            (OperatingSystem::Windows | OperatingSystem::Linux, Architecture::X86_64) => true,
            (OperatingSystem::MacOS, Architecture::X86_64) => {
                // macOS dropped official CUDA support after 10.13 (High Sierra)
                if let Some(ref version) = self.os_version {
                    // Simple version check (not entirely accurate for all cases)
                    version.starts_with("10.") && version.split('.').nth(1).is_some_and(|minor| {
                        minor.parse::<u32>().is_ok_and(|m| m <= 13)
                    })
                } else {
                    false
                }
            }
            (OperatingSystem::Linux, Architecture::ARM64) => true, // NVIDIA Jetson
            _ => false,
        }
    }
    
    /// Check if the platform supports OpenCL
    pub fn supports_opencl(&self) -> bool {
        // OpenCL is more widely supported than CUDA
        match self.os {
            OperatingSystem::Windows | OperatingSystem::Linux | OperatingSystem::MacOS => true,
            _ => false,
        }
    }
    
    /// Check if the platform supports AVX instructions
    pub fn supports_avx(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx")
    }
    
    /// Check if the platform supports AVX2 instructions
    pub fn supports_avx2(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx2")
    }
    
    /// Check if the platform supports AVX-512 instructions
    pub fn supports_avx512(&self) -> bool {
        self.arch.is_x86_family() && self.features.iter().any(|f| f == "avx512f")
    }
    
    /// Check if the platform supports NEON instructions
    pub fn supports_neon(&self) -> bool {
        self.arch.is_arm_family() && self.features.iter().any(|f| f == "neon")
    }
    
    /// Get the standard library name prefix for the platform
    pub fn lib_prefix(&self) -> &'static str {
        if self.os.is_windows() {
            ""
        } else {
            "lib"
        }
    }
    
    /// Get the standard library extension for the platform
    pub fn lib_extension(&self) -> &'static str {
        match self.os {
            OperatingSystem::Windows => "dll",
            OperatingSystem::MacOS => "dylib",
            OperatingSystem::Linux | OperatingSystem::BSD | OperatingSystem::Android => "so",
            _ => "so", // Default to .so for unknown platforms
        }
    }
    
    /// Get the executable extension for the platform
    pub fn exe_extension(&self) -> &'static str {
        if self.os.is_windows() {
            "exe"
        } else {
            ""
        }
    }
    
    /// Get the standard environment variable separator for the platform
    pub fn path_separator(&self) -> &'static str {
        if self.os.is_windows() {
            ";"
        } else {
            ":"
        }
    }
    
    /// Get the standard path separator for the platform
    pub fn dir_separator(&self) -> &'static str {
        if self.os.is_windows() {
            "\\"
        } else {
            "/"
        }
    }
}

/// Detect the current platform
pub fn detect_platform() -> Platform {
    // Detect operating system
    let os = if cfg!(windows) {
        OperatingSystem::Windows
    } else if cfg!(target_os = "macos") {
        OperatingSystem::MacOS
    } else if cfg!(target_os = "ios") {
        OperatingSystem::IOS
    } else if cfg!(target_os = "android") {
        OperatingSystem::Android
    } else if cfg!(target_os = "linux") {
        OperatingSystem::Linux
    } else if cfg!(target_family = "unix") {
        // Could be BSD or other Unix-like
        if cfg!(target_os = "freebsd") || cfg!(target_os = "openbsd") || cfg!(target_os = "netbsd") {
            OperatingSystem::BSD
        } else {
            OperatingSystem::Other
        }
    } else {
        OperatingSystem::Other
    };
    
    // Detect architecture
    let arch = if cfg!(target_arch = "x86") {
        Architecture::X86
    } else if cfg!(target_arch = "x86_64") {
        Architecture::X86_64
    } else if cfg!(target_arch = "arm") {
        Architecture::ARM
    } else if cfg!(target_arch = "aarch64") {
        Architecture::ARM64
    } else if cfg!(target_arch = "mips") {
        Architecture::MIPS
    } else if cfg!(target_arch = "powerpc") {
        Architecture::PowerPC
    } else if cfg!(any(target_arch = "riscv32", target_arch = "riscv64")) {
        Architecture::RISCV
    } else if cfg!(target_arch = "wasm32") {
        Architecture::WASM
    } else {
        Architecture::Other
    };
    
    // Detect OS version
    let os_version = detect_os_version(&os);
    
    // Detect CPU features
    let features = detect_cpu_features(&arch);
    
    Platform {
        os,
        arch,
        os_version,
        features,
    }
}

/// Detect the operating system version
fn detect_os_version(os: &OperatingSystem) -> Option<String> {
    match os {
        OperatingSystem::Windows => {
            // Use PowerShell to get Windows version
            let output = Command::new("powershell")
                .args(["-Command", "[System.Environment]::OSVersion.Version.ToString()"])
                .output()
                .ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                // Fallback to env var if PowerShell fails
                env::var("WINVER").ok()
            }
        }
        OperatingSystem::MacOS => {
            // Use sw_vers to get macOS version
            let output = Command::new("sw_vers")
                .arg("-productVersion")
                .output()
                .ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        OperatingSystem::Linux => {
            // Try to get Linux distribution version
            if let Ok(output) = Command::new("lsb_release").arg("-r").arg("-s").output() {
                if output.status.success() {
                    let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                    return Some(version);
                }
            }
            
            // Fallback to kernel version
            let output = Command::new("uname").arg("-r").output().ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        OperatingSystem::BSD => {
            // Use uname for BSD version
            let output = Command::new("uname").arg("-r").output().ok()?;
            
            if output.status.success() {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                Some(version)
            } else {
                None
            }
        }
        _ => None,
    }
}

/// Detect CPU features
fn detect_cpu_features(arch: &Architecture) -> Vec<String> {
    let mut features = Vec::new();
    
    match arch {
        Architecture::X86 | Architecture::X86_64 => {
            // Use is_x86_feature_detected on x86 platforms
            #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
            {
                if std::is_x86_feature_detected!("sse") {
                    features.push("sse".to_string());
                }
                if std::is_x86_feature_detected!("sse2") {
                    features.push("sse2".to_string());
                }
                if std::is_x86_feature_detected!("sse3") {
                    features.push("sse3".to_string());
                }
                if std::is_x86_feature_detected!("ssse3") {
                    features.push("ssse3".to_string());
                }
                if std::is_x86_feature_detected!("sse4.1") {
                    features.push("sse4.1".to_string());
                }
                if std::is_x86_feature_detected!("sse4.2") {
                    features.push("sse4.2".to_string());
                }
                if std::is_x86_feature_detected!("avx") {
                    features.push("avx".to_string());
                }
                if std::is_x86_feature_detected!("avx2") {
                    features.push("avx2".to_string());
                }
                if std::is_x86_feature_detected!("avx512f") {
                    features.push("avx512f".to_string());
                }
                if std::is_x86_feature_detected!("fma") {
                    features.push("fma".to_string());
                }
                if std::is_x86_feature_detected!("bmi1") {
                    features.push("bmi1".to_string());
                }
                if std::is_x86_feature_detected!("bmi2") {
                    features.push("bmi2".to_string());
                }
                if std::is_x86_feature_detected!("popcnt") {
                    features.push("popcnt".to_string());
                }
                if std::is_x86_feature_detected!("lzcnt") {
                    features.push("lzcnt".to_string());
                }
                if std::is_x86_feature_detected!("aes") {
                    features.push("aes".to_string());
                }
            }
            
            // Fallback when compile-time detection is not available
            #[cfg(not(any(target_arch = "x86", target_arch = "x86_64")))]
            {
                // Parse /proc/cpuinfo on Linux
                if let Ok(cpuinfo) = std::fs::read_to_string("/proc/cpuinfo") {
                    let mut cpu_flags = String::new();
                    
                    for line in cpuinfo.lines() {
                        if line.starts_with("flags") || line.starts_with("Features") {
                            cpu_flags = line.split(':').nth(1).unwrap_or("").trim().to_string();
                            break;
                        }
                    }
                    
                    for flag in cpu_flags.split_whitespace() {
                        features.push(flag.to_string());
                    }
                }
            }
        }
        Architecture::ARM | Architecture::ARM64 => {
            // ARM feature detection
            #[cfg(any(target_arch = "arm", target_arch = "aarch64"))]
            {
                // Check for NEON support on ARM
                #[cfg(target_arch = "arm")]
                if std::arch::is_arm_feature_detected!("neon") {
                    features.push("neon".to_string());
                }
                
                // NEON is mandatory on AArch64
                #[cfg(target_arch = "aarch64")]
                features.push("neon".to_string());
                
                // Check for additional ARM features
                #[cfg(target_arch = "aarch64")]
                {
                    if std::arch::is_aarch64_feature_detected!("sve") {
                        features.push("sve".to_string());
                    }
                    if std::arch::is_aarch64_feature_detected!("crc") {
                        features.push("crc".to_string());
                    }
                    if std::arch::is_aarch64_feature_detected!("lse") {
                        features.push("lse".to_string());
                    }
                }
            }
            
            // Fallback when compile-time detection is not available
            #[cfg(not(any(target_arch = "arm", target_arch = "aarch64")))]
            {
                if *arch == Architecture::ARM64 {
                    // NEON is mandatory on AArch64
                    features.push("neon".to_string());
                }
                
                // Parse /proc/cpuinfo on Linux
                if let Ok(cpuinfo) = std::fs::read_to_string("/proc/cpuinfo") {
                    let mut cpu_features = String::new();
                    
                    for line in cpuinfo.lines() {
                        if line.starts_with("Features") {
                            cpu_features = line.split(':').nth(1).unwrap_or("").trim().to_string();
                            break;
                        }
                    }
                    
                    for feature in cpu_features.split_whitespace() {
                        features.push(feature.to_string());
                    }
                }
            }
        }
        _ => {
            // Other architectures not yet supported for feature detection
        }
    }
    
    features
}



Directory: src\util
File: hash.rs
=============
// src/util/hash.rs
//! Hash utilities for the OmniForge compiler.
//!
//! This module provides functionality for computing hashes of files and strings,
//! which is useful for caching and validation in the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::path::Path;
use std::fs::File;
use std::io::{self, Read, BufReader};
use std::fmt::Write as FmtWrite;

/// Hash algorithm types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum HashAlgorithm {
    /// MD5 hash algorithm
    MD5,
    /// SHA-1 hash algorithm
    SHA1,
    /// SHA-256 hash algorithm
    SHA256,
    /// SHA-512 hash algorithm
    SHA512,
    /// Blake2b hash algorithm
    Blake2b,
    /// Blake3 hash algorithm
    Blake3,
    /// xxHash algorithm
    XXHash,
}

impl HashAlgorithm {
    /// Get the string representation of the hash algorithm
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::MD5 => "md5",
            Self::SHA1 => "sha1",
            Self::SHA256 => "sha256",
            Self::SHA512 => "sha512",
            Self::Blake2b => "blake2b",
            Self::Blake3 => "blake3",
            Self::XXHash => "xxhash",
        }
    }
}

/// Compute the hash of a file
///
/// # Arguments
///
/// * `path` - Path to the file
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `io::Result<String>` - Hex-encoded hash string
///
/// # Examples
///
/// ```no_run
/// use std::path::Path;
/// use omni_forge::util::hash::{compute_file_hash, HashAlgorithm};
///
/// let hash = compute_file_hash(Path::new("src/main.rs"), HashAlgorithm::SHA256).unwrap();
/// println!("Hash: {}", hash);
/// ```
pub fn compute_file_hash<P: AsRef<Path>>(path: P, algorithm: HashAlgorithm) -> io::Result<String> {
    let file = File::open(path)?;
    let mut reader = BufReader::new(file);
    let mut buffer = Vec::new();
    reader.read_to_end(&mut buffer)?;
    
    Ok(compute_hash(&buffer, algorithm))
}

/// Compute the hash of a string
///
/// # Arguments
///
/// * `input` - Input string
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
///
/// # Examples
///
/// ```
/// use omni_forge::util::hash::{compute_string_hash, HashAlgorithm};
///
/// let hash = compute_string_hash("Hello, world!", HashAlgorithm::SHA256);
/// println!("Hash: {}", hash);
/// ```
pub fn compute_string_hash(input: &str, algorithm: HashAlgorithm) -> String {
    compute_hash(input.as_bytes(), algorithm)
}

/// Compute the hash of a byte slice
///
/// # Arguments
///
/// * `data` - Input data
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
fn compute_hash(data: &[u8], algorithm: HashAlgorithm) -> String {
    match algorithm {
        HashAlgorithm::MD5 => {
            let digest = md5::compute(data);
            format_hash_bytes(&digest.0)
        }
        HashAlgorithm::SHA1 => {
            use sha1::{Sha1, Digest};
            let mut hasher = Sha1::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA256 => {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA512 => {
            use sha2::{Sha512, Digest};
            let mut hasher = Sha512::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake2b => {
            use blake2::{Blake2b512, Digest};
            let mut hasher = Blake2b512::new();
            hasher.update(data);
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake3 => {
            let hash = blake3::hash(data);
            hash.to_hex().to_string()
        }
        HashAlgorithm::XXHash => {
            use twox_hash::XxHash64;
            use std::hash::Hasher;
            let mut hasher = XxHash64::with_seed(0);
            hasher.write(data);
            format!("{:016x}", hasher.finish())
        }
    }
}

/// Format hash bytes as a hex string
///
/// # Arguments
///
/// * `bytes` - Hash bytes
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
fn format_hash_bytes(bytes: &[u8]) -> String {
    let mut s = String::with_capacity(bytes.len() * 2);
    for &b in bytes {
        write!(&mut s, "{b:02x}").unwrap();
    }
    s
}

/// Check if a file matches a hash
///
/// # Arguments
///
/// * `path` - Path to the file
/// * `expected_hash` - Expected hash
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `io::Result<bool>` - Whether the file matches the hash
///
/// # Examples
///
/// ```no_run
/// use std::path::Path;
/// use omni_forge::util::hash::{check_file_hash, HashAlgorithm};
///
/// let matches = check_file_hash(
///     Path::new("src/main.rs"),
///     "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
///     HashAlgorithm::SHA256
/// ).unwrap();
///
/// if matches {
///     println!("File hash matches");
/// } else {
///     println!("File hash does not match");
/// }
/// ```
#[allow(dead_code)]
pub fn check_file_hash<P: AsRef<Path>>(path: P, expected_hash: &str, algorithm: HashAlgorithm) -> io::Result<bool> {
    let hash = compute_file_hash(path, algorithm)?;
    Ok(hash == expected_hash)
}

/// Generate a hash of multiple inputs
///
/// # Arguments
///
/// * `inputs` - Input strings or byte arrays
/// * `algorithm` - Hash algorithm to use
///
/// # Returns
///
/// * `String` - Hex-encoded hash string
///
/// # Examples
///
/// ```
/// use omni_forge::util::hash::{compute_combined_hash, HashAlgorithm};
///
/// let hash = compute_combined_hash(&["Hello", "world"], HashAlgorithm::SHA256);
/// println!("Hash: {}", hash);
/// ```
#[allow(dead_code)]
pub fn compute_combined_hash<I, B>(inputs: I, algorithm: HashAlgorithm) -> String
where
    I: IntoIterator<Item = B>,
    B: AsRef<[u8]>,
{
    match algorithm {
        HashAlgorithm::MD5 => {
            let mut context = md5::Context::new();
            for input in inputs {
                context.consume(input.as_ref());
            }
            let digest = context.finalize();
            format_hash_bytes(&digest.0)
        }
        HashAlgorithm::SHA1 => {
            use sha1::{Sha1, Digest};
            let mut hasher = Sha1::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA256 => {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::SHA512 => {
            use sha2::{Sha512, Digest};
            let mut hasher = Sha512::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake2b => {
            use blake2::{Blake2b512, Digest};
            let mut hasher = Blake2b512::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            let result = hasher.finalize();
            format_hash_bytes(&result)
        }
        HashAlgorithm::Blake3 => {
            let mut hasher = blake3::Hasher::new();
            for input in inputs {
                hasher.update(input.as_ref());
            }
            hasher.finalize().to_hex().to_string()
        }
        HashAlgorithm::XXHash => {
            use twox_hash::XxHash64;
            use std::hash::Hasher;
            let mut hasher = XxHash64::with_seed(0);
            for input in inputs {
                hasher.write(input.as_ref());
            }
            format!("{:016x}", hasher.finish())
        }
    }
}

/// Compute an incremental hash
///
/// This struct allows you to compute a hash incrementally, adding data in chunks.
#[allow(dead_code)]
pub struct IncrementalHasher {
    /// Hash algorithm
    algorithm: HashAlgorithm,
    /// Internal hasher state
    state: IncrementalHasherState,
}

/// Internal hasher state
#[allow(dead_code)]
enum IncrementalHasherState {
    /// MD5 hasher
    MD5(md5::Context),
    /// SHA-1 hasher
    SHA1(sha1::Sha1),
    /// SHA-256 hasher
    SHA256(sha2::Sha256),
    /// SHA-512 hasher
    SHA512(sha2::Sha512),
    /// Blake2b hasher
    Blake2b(blake2::Blake2b512),
    /// Blake3 hasher
    Blake3(blake3::Hasher),
    /// XXHash hasher
    XXHash(twox_hash::XxHash64),
}

#[allow(dead_code)]
impl IncrementalHasher {
    /// Create a new incremental hasher
    ///
    /// # Arguments
    ///
    /// * `algorithm` - Hash algorithm to use
    ///
    /// # Returns
    ///
    /// * `IncrementalHasher` - The new hasher
    ///
    /// # Examples
    ///
    /// ```
    /// use omni_forge::util::hash::{IncrementalHasher, HashAlgorithm};
    ///
    /// let mut hasher = IncrementalHasher::new(HashAlgorithm::SHA256);
    /// hasher.update("Hello, ");
    /// hasher.update("world!");
    /// let hash = hasher.finalize();
    /// println!("Hash: {}", hash);
    /// ```
    pub fn new(algorithm: HashAlgorithm) -> Self {
        let state = match algorithm {
            HashAlgorithm::MD5 => IncrementalHasherState::MD5(md5::Context::new()),
            HashAlgorithm::SHA1 => IncrementalHasherState::SHA1(sha1::Sha1::default()),
            HashAlgorithm::SHA256 => IncrementalHasherState::SHA256(sha2::Sha256::default()),
            HashAlgorithm::SHA512 => IncrementalHasherState::SHA512(sha2::Sha512::default()),
            HashAlgorithm::Blake2b => IncrementalHasherState::Blake2b(blake2::Blake2b512::default()),
            HashAlgorithm::Blake3 => IncrementalHasherState::Blake3(blake3::Hasher::new()),
            HashAlgorithm::XXHash => IncrementalHasherState::XXHash(twox_hash::XxHash64::with_seed(0)),
        };
        
        Self {
            algorithm,
            state,
        }
    }
    
    /// Update the hasher with more data
    ///
    /// # Arguments
    ///
    /// * `data` - Data to add to the hash
    pub fn update<T: AsRef<[u8]>>(&mut self, data: T) {
        match &mut self.state {
            IncrementalHasherState::MD5(hasher) => {
                hasher.consume(data.as_ref());
            }
            IncrementalHasherState::SHA1(hasher) => {
                use sha1::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::SHA256(hasher) => {
                use sha2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::SHA512(hasher) => {
                use sha2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::Blake2b(hasher) => {
                use blake2::Digest;
                hasher.update(data);
            }
            IncrementalHasherState::Blake3(hasher) => {
                hasher.update(data.as_ref());
            }
            IncrementalHasherState::XXHash(hasher) => {
                use std::hash::Hasher;
                hasher.write(data.as_ref());
            }
        }
    }
    
    /// Finalize the hash and return the result
    ///
    /// # Returns
    ///
    /// * `String` - Hex-encoded hash string
    pub fn finalize(self) -> String {
        match self.state {
            IncrementalHasherState::MD5(hasher) => {
                let result = hasher.finalize();
                format_hash_bytes(&result.0)
            }
            IncrementalHasherState::SHA1(hasher) => {
                use sha1::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::SHA256(hasher) => {
                use sha2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::SHA512(hasher) => {
                use sha2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::Blake2b(hasher) => {
                use blake2::Digest;
                let result = hasher.finalize();
                format_hash_bytes(&result)
            }
            IncrementalHasherState::Blake3(hasher) => {
                hasher.finalize().to_hex().to_string()
            }
            IncrementalHasherState::XXHash(hasher) => {
                use std::hash::Hasher;
                format!("{:016x}", hasher.finish())
            }
        }
    }
    
    /// Get the hash algorithm
    ///
    /// # Returns
    ///
    /// * `HashAlgorithm` - Hash algorithm
    pub fn algorithm(&self) -> HashAlgorithm {
        self.algorithm
    }
}



Directory: src\util
File: type_conversion.rs
========================
// src/util/type_conversion.rs
//! Type conversion utilities for the OmniForge compiler.
//!
//! This module provides type mapping and conversion functionality between
//! different programming languages, supporting the code generation process
//! for various target languages in the OmniCodex framework.
/*▫~•◦────────────────────────────────────────────────────────────────────────────────────‣
 * © 2025 ArcMoon Studios ◦ SPDX-License-Identifier MIT OR Apache-2.0 ◦ Author: Lord Xyn ✶
 *///◦────────────────────────────────────────────────────────────────────────────────────‣

use std::collections::HashMap;
use std::sync::OnceLock;

use crate::codegen::ArgType;
use crate::error::{OmniError, OmniResult};

/// Type conversion maps are lazily initialized to avoid initialization overhead
static RUST_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static C_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static CPP_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static PYTHON_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();
static TS_TYPE_MAP: OnceLock<HashMap<ArgType, &'static str>> = OnceLock::new();

/// Initialize the Rust type map
fn get_rust_type_map() -> &'static HashMap<ArgType, &'static str> {
    RUST_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "()");
        map.insert(ArgType::I8, "i8");
        map.insert(ArgType::I16, "i16");
        map.insert(ArgType::I32, "i32");
        map.insert(ArgType::I64, "i64");
        map.insert(ArgType::U8, "u8");
        map.insert(ArgType::U16, "u16");
        map.insert(ArgType::U32, "u32");
        map.insert(ArgType::U64, "u64");
        map.insert(ArgType::F32, "f32");
        map.insert(ArgType::F64, "f64");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "*mut i8");
        map.insert(ArgType::I16Ptr, "*mut i16");
        map.insert(ArgType::I32Ptr, "*mut i32");
        map.insert(ArgType::I64Ptr, "*mut i64");
        map.insert(ArgType::U8Ptr, "*mut u8");
        map.insert(ArgType::U16Ptr, "*mut u16");
        map.insert(ArgType::U32Ptr, "*mut u32");
        map.insert(ArgType::U64Ptr, "*mut u64");
        map.insert(ArgType::F32Ptr, "*mut f32");
        map.insert(ArgType::F64Ptr, "*mut f64");
        map.insert(ArgType::BoolPtr, "*mut bool");
        map.insert(ArgType::VoidPtr, "*mut std::ffi::c_void");
        
        map
    })
}

/// Initialize the C type map
fn get_c_type_map() -> &'static HashMap<ArgType, &'static str> {
    C_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "int8_t");
        map.insert(ArgType::I16, "int16_t");
        map.insert(ArgType::I32, "int32_t");
        map.insert(ArgType::I64, "int64_t");
        map.insert(ArgType::U8, "uint8_t");
        map.insert(ArgType::U16, "uint16_t");
        map.insert(ArgType::U32, "uint32_t");
        map.insert(ArgType::U64, "uint64_t");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "double");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "int8_t*");
        map.insert(ArgType::I16Ptr, "int16_t*");
        map.insert(ArgType::I32Ptr, "int32_t*");
        map.insert(ArgType::I64Ptr, "int64_t*");
        map.insert(ArgType::U8Ptr, "uint8_t*");
        map.insert(ArgType::U16Ptr, "uint16_t*");
        map.insert(ArgType::U32Ptr, "uint32_t*");
        map.insert(ArgType::U64Ptr, "uint64_t*");
        map.insert(ArgType::F32Ptr, "float*");
        map.insert(ArgType::F64Ptr, "double*");
        map.insert(ArgType::BoolPtr, "bool*");
        map.insert(ArgType::VoidPtr, "void*");
        
        map
    })
}

/// Initialize the C++ type map
fn get_cpp_type_map() -> &'static HashMap<ArgType, &'static str> {
    CPP_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "std::int8_t");
        map.insert(ArgType::I16, "std::int16_t");
        map.insert(ArgType::I32, "std::int32_t");
        map.insert(ArgType::I64, "std::int64_t");
        map.insert(ArgType::U8, "std::uint8_t");
        map.insert(ArgType::U16, "std::uint16_t");
        map.insert(ArgType::U32, "std::uint32_t");
        map.insert(ArgType::U64, "std::uint64_t");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "double");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "std::int8_t*");
        map.insert(ArgType::I16Ptr, "std::int16_t*");
        map.insert(ArgType::I32Ptr, "std::int32_t*");
        map.insert(ArgType::I64Ptr, "std::int64_t*");
        map.insert(ArgType::U8Ptr, "std::uint8_t*");
        map.insert(ArgType::U16Ptr, "std::uint16_t*");
        map.insert(ArgType::U32Ptr, "std::uint32_t*");
        map.insert(ArgType::U64Ptr, "std::uint64_t*");
        map.insert(ArgType::F32Ptr, "float*");
        map.insert(ArgType::F64Ptr, "double*");
        map.insert(ArgType::BoolPtr, "bool*");
        map.insert(ArgType::VoidPtr, "void*");
        
        map
    })
}

/// Initialize the Python type map
fn get_python_type_map() -> &'static HashMap<ArgType, &'static str> {
    PYTHON_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "None");
        map.insert(ArgType::I8, "int");
        map.insert(ArgType::I16, "int");
        map.insert(ArgType::I32, "int");
        map.insert(ArgType::I64, "int");
        map.insert(ArgType::U8, "int");
        map.insert(ArgType::U16, "int");
        map.insert(ArgType::U32, "int");
        map.insert(ArgType::U64, "int");
        map.insert(ArgType::F32, "float");
        map.insert(ArgType::F64, "float");
        map.insert(ArgType::Bool, "bool");
        map.insert(ArgType::I8Ptr, "np.ndarray");
        map.insert(ArgType::I16Ptr, "np.ndarray");
        map.insert(ArgType::I32Ptr, "np.ndarray");
        map.insert(ArgType::I64Ptr, "np.ndarray");
        map.insert(ArgType::U8Ptr, "np.ndarray");
        map.insert(ArgType::U16Ptr, "np.ndarray");
        map.insert(ArgType::U32Ptr, "np.ndarray");
        map.insert(ArgType::U64Ptr, "np.ndarray");
        map.insert(ArgType::F32Ptr, "np.ndarray");
        map.insert(ArgType::F64Ptr, "np.ndarray");
        map.insert(ArgType::BoolPtr, "np.ndarray");
        map.insert(ArgType::VoidPtr, "ctypes.c_void_p");
        
        map
    })
}

/// Initialize the TypeScript type map
fn get_ts_type_map() -> &'static HashMap<ArgType, &'static str> {
    TS_TYPE_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        
        map.insert(ArgType::Void, "void");
        map.insert(ArgType::I8, "number");
        map.insert(ArgType::I16, "number");
        map.insert(ArgType::I32, "number");
        map.insert(ArgType::I64, "bigint");
        map.insert(ArgType::U8, "number");
        map.insert(ArgType::U16, "number");
        map.insert(ArgType::U32, "number");
        map.insert(ArgType::U64, "bigint");
        map.insert(ArgType::F32, "number");
        map.insert(ArgType::F64, "number");
        map.insert(ArgType::Bool, "boolean");
        map.insert(ArgType::I8Ptr, "Int8Array");
        map.insert(ArgType::I16Ptr, "Int16Array");
        map.insert(ArgType::I32Ptr, "Int32Array");
        map.insert(ArgType::I64Ptr, "BigInt64Array");
        map.insert(ArgType::U8Ptr, "Uint8Array");
        map.insert(ArgType::U16Ptr, "Uint16Array");
        map.insert(ArgType::U32Ptr, "Uint32Array");
        map.insert(ArgType::U64Ptr, "BigUint64Array");
        map.insert(ArgType::F32Ptr, "Float32Array");
        map.insert(ArgType::F64Ptr, "Float64Array");
        map.insert(ArgType::BoolPtr, "Uint8Array");
        map.insert(ArgType::VoidPtr, "ArrayBuffer");
        
        map
    })
}

/// Convert an ArgType to a Rust type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
///
/// # Returns
///
/// * `OmniResult<String>` - The Rust type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_rust_type;
///
/// let rust_type = convert_to_rust_type(&ArgType::I32, false).unwrap();
/// assert_eq!(rust_type, "i32");
///
/// let const_ptr = convert_to_rust_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(const_ptr, "*const f32");
/// ```
pub fn convert_to_rust_type(arg_type: &ArgType, const_qualifier: bool) -> OmniResult<String> {
    let type_map = get_rust_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier for pointers
        if const_qualifier && arg_type_name.contains("mut") {
            Ok(arg_type_name.replace("*mut", "*const"))
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for Rust: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a C type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
///
/// # Returns
///
/// * `OmniResult<String>` - The C type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_c_type;
///
/// let c_type = convert_to_c_type(&ArgType::I32, false).unwrap();
/// assert_eq!(c_type, "int32_t");
///
/// let const_ptr = convert_to_c_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(const_ptr, "const float*");
/// ```
pub fn convert_to_c_type(arg_type: &ArgType, const_qualifier: bool) -> OmniResult<String> {
    let type_map = get_c_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier for pointers
        if const_qualifier && arg_type_name.contains('*') {
            Ok(format!("const {arg_type_name}"))
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for C: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a C++ type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `const_qualifier` - Whether to add a const qualifier (for pointers)
/// * `use_references` - Whether to use references instead of pointers
///
/// # Returns
///
/// * `OmniResult<String>` - The C++ type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_cpp_type;
///
/// let cpp_type = convert_to_cpp_type(&ArgType::I32, false, false).unwrap();
/// assert_eq!(cpp_type, "std::int32_t");
///
/// let const_ref = convert_to_cpp_type(&ArgType::F32Ptr, true, true).unwrap();
/// assert_eq!(const_ref, "const float&");
/// ```
pub fn convert_to_cpp_type(arg_type: &ArgType, const_qualifier: bool, use_references: bool) -> OmniResult<String> {
    let type_map = get_cpp_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        // Handle const qualifier and references for pointers
        if arg_type_name.contains('*') {
            if use_references {
                // Convert pointer to reference
                let base_type = arg_type_name.replace('*', "");
                if const_qualifier {
                    Ok(format!("const {base_type}&"))
                } else {
                    Ok(format!("{base_type}&"))
                }
            } else if const_qualifier {
                // Use const pointer
                Ok(format!("const {arg_type_name}"))
            } else {
                Ok(arg_type_name.to_string())
            }
        } else {
            Ok(arg_type_name.to_string())
        }
    } else if let ArgType::Custom(name) = arg_type {
        Ok(name.clone())
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for C++: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a Python type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
/// * `use_type_hints` - Whether to generate type hints
///
/// # Returns
///
/// * `OmniResult<String>` - The Python type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_python_type;
///
/// let py_type = convert_to_python_type(&ArgType::I32, true).unwrap();
/// assert_eq!(py_type, "int");
///
/// let array_type = convert_to_python_type(&ArgType::F32Ptr, true).unwrap();
/// assert_eq!(array_type, "np.ndarray");
/// ```
pub fn convert_to_python_type(arg_type: &ArgType, use_type_hints: bool) -> OmniResult<String> {
    if !use_type_hints {
        return Ok(String::new());
    }
    
    let type_map = get_python_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        Ok(arg_type_name.to_string())
    } else if let ArgType::Custom(name) = arg_type {
        Ok(format!("Any # {name}"))
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for Python: {arg_type:?}"
        )))
    }
}

/// Convert an ArgType to a TypeScript type string
///
/// # Arguments
///
/// * `arg_type` - The ArgType to convert
///
/// # Returns
///
/// * `OmniResult<String>` - The TypeScript type string
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::convert_to_ts_type;
///
/// let ts_type = convert_to_ts_type(&ArgType::I32).unwrap();
/// assert_eq!(ts_type, "number");
///
/// let array_type = convert_to_ts_type(&ArgType::F32Ptr).unwrap();
/// assert_eq!(array_type, "Float32Array");
/// ```
pub fn convert_to_ts_type(arg_type: &ArgType) -> OmniResult<String> {
    let type_map = get_ts_type_map();
    
    if let Some(arg_type_name) = type_map.get(arg_type) {
        Ok(arg_type_name.to_string())
    } else if let ArgType::Custom(name) = arg_type {
        Ok(format!("any /* {name} */"))
    } else {
        Err(OmniError::General(format!(
            "Unsupported ArgType for TypeScript: {arg_type:?}"
        )))
    }
}

/// Get the size of a type in bytes
///
/// # Arguments
///
/// * `arg_type` - The ArgType to get the size of
///
/// # Returns
///
/// * `OmniResult<usize>` - The size in bytes
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::get_type_size;
///
/// let size = get_type_size(&ArgType::I32).unwrap();
/// assert_eq!(size, 4);
/// ```
#[allow(dead_code)]
pub fn get_type_size(arg_type: &ArgType) -> OmniResult<usize> {
    match arg_type {
        ArgType::Void => Ok(0),
        ArgType::I8 | ArgType::U8 | ArgType::Bool => Ok(1),
        ArgType::I16 | ArgType::U16 => Ok(2),
        ArgType::I32 | ArgType::U32 | ArgType::F32 => Ok(4),
        ArgType::I64 | ArgType::U64 | ArgType::F64 => Ok(8),
        ArgType::I8Ptr | ArgType::I16Ptr | ArgType::I32Ptr | ArgType::I64Ptr |
        ArgType::U8Ptr | ArgType::U16Ptr | ArgType::U32Ptr | ArgType::U64Ptr |
        ArgType::F32Ptr | ArgType::F64Ptr | ArgType::BoolPtr | ArgType::VoidPtr => {
            #[cfg(target_pointer_width = "64")]
            return Ok(8);
            #[cfg(target_pointer_width = "32")]
            return Ok(4);
        }
        ArgType::Custom(_) => {
            Err(OmniError::General(format!(
                "Cannot determine size of custom type: {arg_type:?}"
            )))
        }
    }
}

/// Get the alignment of a type in bytes
///
/// # Arguments
///
/// * `arg_type` - The ArgType to get the alignment of
///
/// # Returns
///
/// * `OmniResult<usize>` - The alignment in bytes
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::get_type_alignment;
///
/// let alignment = get_type_alignment(&ArgType::I32).unwrap();
/// assert_eq!(alignment, 4);
/// ```
#[allow(dead_code)]
pub fn get_type_alignment(arg_type: &ArgType) -> OmniResult<usize> {
    match arg_type {
        ArgType::Void => Ok(1), // Void has alignment 1 by convention
        ArgType::I8 | ArgType::U8 | ArgType::Bool => Ok(1),
        ArgType::I16 | ArgType::U16 => Ok(2),
        ArgType::I32 | ArgType::U32 | ArgType::F32 => Ok(4),
        ArgType::I64 | ArgType::U64 | ArgType::F64 => Ok(8),
        ArgType::I8Ptr | ArgType::I16Ptr | ArgType::I32Ptr | ArgType::I64Ptr |
        ArgType::U8Ptr | ArgType::U16Ptr | ArgType::U32Ptr | ArgType::U64Ptr |
        ArgType::F32Ptr | ArgType::F64Ptr | ArgType::BoolPtr | ArgType::VoidPtr => {
            #[cfg(target_pointer_width = "64")]
            return Ok(8);
            #[cfg(target_pointer_width = "32")]
            return Ok(4);
        }
        ArgType::Custom(_) => {
            Err(OmniError::General(format!(
                "Cannot determine alignment of custom type: {arg_type:?}"
            )))
        }
    }
}

/// Parse a type string into an ArgType
///
/// # Arguments
///
/// * `type_string` - The type string to parse
/// * `language` - The programming language of the type string
///
/// # Returns
///
/// * `OmniResult<ArgType>` - The parsed ArgType
///
/// # Examples
///
/// ```
/// use omni_forge::codegen::ArgType;
/// use omni_forge::util::type_conversion::parse_type_string;
///
/// let arg_type = parse_type_string("int32_t", "c").unwrap();
/// assert_eq!(arg_type, ArgType::I32);
///
/// let ptr_type = parse_type_string("float*", "c").unwrap();
/// assert_eq!(ptr_type, ArgType::F32Ptr);
/// ```
#[allow(dead_code)]
pub fn parse_type_string(type_string: &str, language: &str) -> OmniResult<ArgType> {
    // Remove const qualifier and trim whitespace
    let type_string = type_string
        .replace("const ", "")
        .replace("&", "")
        .trim()
        .to_string();
    
    // Check if it's a pointer type
    let is_pointer = type_string.contains('*');
    let base_type = type_string.replace('*', "").trim().to_string();
    
    match language.to_lowercase().as_str() {
        "c" | "cpp" => {
            // Parse C/C++ type string
            match base_type.as_str() {
                "void" => {
                    if is_pointer {
                        Ok(ArgType::VoidPtr)
                    } else {
                        Ok(ArgType::Void)
                    }
                }
                "char" | "int8_t" | "std::int8_t" => {
                    if is_pointer {
                        Ok(ArgType::I8Ptr)
                    } else {
                        Ok(ArgType::I8)
                    }
                }
                "short" | "int16_t" | "std::int16_t" => {
                    if is_pointer {
                        Ok(ArgType::I16Ptr)
                    } else {
                        Ok(ArgType::I16)
                    }
                }
                "int" | "int32_t" | "std::int32_t" => {
                    if is_pointer {
                        Ok(ArgType::I32Ptr)
                    } else {
                        Ok(ArgType::I32)
                    }
                }
                "long long" | "int64_t" | "std::int64_t" => {
                    if is_pointer {
                        Ok(ArgType::I64Ptr)
                    } else {
                        Ok(ArgType::I64)
                    }
                }
                "unsigned char" | "uint8_t" | "std::uint8_t" => {
                    if is_pointer {
                        Ok(ArgType::U8Ptr)
                    } else {
                        Ok(ArgType::U8)
                    }
                }
                "unsigned short" | "uint16_t" | "std::uint16_t" => {
                    if is_pointer {
                        Ok(ArgType::U16Ptr)
                    } else {
                        Ok(ArgType::U16)
                    }
                }
                "unsigned int" | "uint32_t" | "std::uint32_t" => {
                    if is_pointer {
                        Ok(ArgType::U32Ptr)
                    } else {
                        Ok(ArgType::U32)
                    }
                }
                "unsigned long long" | "uint64_t" | "std::uint64_t" => {
                    if is_pointer {
                        Ok(ArgType::U64Ptr)
                    } else {
                        Ok(ArgType::U64)
                    }
                }
                "float" => {
                    if is_pointer {
                        Ok(ArgType::F32Ptr)
                    } else {
                        Ok(ArgType::F32)
                    }
                }
                "double" => {
                    if is_pointer {
                        Ok(ArgType::F64Ptr)
                    } else {
                        Ok(ArgType::F64)
                    }
                }
                "bool" => {
                    if is_pointer {
                        Ok(ArgType::BoolPtr)
                    } else {
                        Ok(ArgType::Bool)
                    }
                }
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "rust" => {
            // Parse Rust type string
            match base_type.as_str() {
                "()" => Ok(ArgType::Void),
                "i8" => {
                    if is_pointer {
                        Ok(ArgType::I8Ptr)
                    } else {
                        Ok(ArgType::I8)
                    }
                }
                "i16" => {
                    if is_pointer {
                        Ok(ArgType::I16Ptr)
                    } else {
                        Ok(ArgType::I16)
                    }
                }
                "i32" => {
                    if is_pointer {
                        Ok(ArgType::I32Ptr)
                    } else {
                        Ok(ArgType::I32)
                    }
                }
                "i64" => {
                    if is_pointer {
                        Ok(ArgType::I64Ptr)
                    } else {
                        Ok(ArgType::I64)
                    }
                }
                "u8" => {
                    if is_pointer {
                        Ok(ArgType::U8Ptr)
                    } else {
                        Ok(ArgType::U8)
                    }
                }
                "u16" => {
                    if is_pointer {
                        Ok(ArgType::U16Ptr)
                    } else {
                        Ok(ArgType::U16)
                    }
                }
                "u32" => {
                    if is_pointer {
                        Ok(ArgType::U32Ptr)
                    } else {
                        Ok(ArgType::U32)
                    }
                }
                "u64" => {
                    if is_pointer {
                        Ok(ArgType::U64Ptr)
                    } else {
                        Ok(ArgType::U64)
                    }
                }
                "f32" => {
                    if is_pointer {
                        Ok(ArgType::F32Ptr)
                    } else {
                        Ok(ArgType::F32)
                    }
                }
                "f64" => {
                    if is_pointer {
                        Ok(ArgType::F64Ptr)
                    } else {
                        Ok(ArgType::F64)
                    }
                }
                "bool" => {
                    if is_pointer {
                        Ok(ArgType::BoolPtr)
                    } else {
                        Ok(ArgType::Bool)
                    }
                }
                "c_void" => {
                    if is_pointer {
                        Ok(ArgType::VoidPtr)
                    } else {
                        Ok(ArgType::Void)
                    }
                }
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "python" => {
            // Parse Python type string
            match base_type.as_str() {
                "None" => Ok(ArgType::Void),
                "int" => {
                    // Default to 32-bit integer for Python int
                    Ok(ArgType::I32)
                }
                "float" => {
                    // Default to 64-bit float for Python float
                    Ok(ArgType::F64)
                }
                "bool" => Ok(ArgType::Bool),
                "np.ndarray" | "ndarray" => {
                    // Default to float32 array for NumPy arrays
                    Ok(ArgType::F32Ptr)
                }
                "ctypes.c_void_p" | "c_void_p" => Ok(ArgType::VoidPtr),
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        "typescript" | "ts" => {
            // Parse TypeScript type string
            match base_type.as_str() {
                "void" => Ok(ArgType::Void),
                "number" => {
                    // Default to 32-bit float for TypeScript number
                    Ok(ArgType::F32)
                }
                "bigint" => Ok(ArgType::I64),
                "boolean" => Ok(ArgType::Bool),
                "Int8Array" => Ok(ArgType::I8Ptr),
                "Int16Array" => Ok(ArgType::I16Ptr),
                "Int32Array" => Ok(ArgType::I32Ptr),
                "BigInt64Array" => Ok(ArgType::I64Ptr),
                "Uint8Array" => Ok(ArgType::U8Ptr),
                "Uint16Array" => Ok(ArgType::U16Ptr),
                "Uint32Array" => Ok(ArgType::U32Ptr),
                "BigUint64Array" => Ok(ArgType::U64Ptr),
                "Float32Array" => Ok(ArgType::F32Ptr),
                "Float64Array" => Ok(ArgType::F64Ptr),
                "ArrayBuffer" => Ok(ArgType::VoidPtr),
                _ => Ok(ArgType::Custom(type_string)),
            }
        }
        _ => Err(OmniError::General(format!(
            "Unsupported language for type parsing: {language}"
        ))),
    }
}


