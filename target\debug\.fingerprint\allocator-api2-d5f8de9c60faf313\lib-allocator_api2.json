{"rustc": 1842507548689473721, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 187265481308423917, "path": 5981102575508147745, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\allocator-api2-d5f8de9c60faf313\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}